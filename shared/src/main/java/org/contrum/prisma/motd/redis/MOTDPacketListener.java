package org.contrum.prisma.motd.redis;

import lombok.RequiredArgsConstructor;
import org.contrum.prisma.motd.MOTDService;
import org.contrum.prisma.utils.redis.pyrite.packet.PacketContainer;
import org.contrum.prisma.utils.redis.pyrite.packet.RedisPacketListener;

@RequiredArgsConstructor
public class MOTDPacketListener implements PacketContainer {

    private final MOTDService service;

    @RedisPacketListener
    public void packet(MOTDRefreshPacket packet) {
        service.loadFromDB();
    }
}
