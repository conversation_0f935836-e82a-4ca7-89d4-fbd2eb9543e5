package org.contrum.prisma.kit.menu;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.inventory.ItemBuilder;
import org.contrum.chorpu.menu.button.Button;
import org.contrum.chorpu.menu.impl.Menu;
import org.contrum.chorpu.menu.impl.centered.CenteredMenu;
import org.contrum.prisma.DuelsAddon;
import org.contrum.prisma.kit.Kit;
import org.contrum.prisma.utils.menus.buttons.BackButton;
import org.contrum.tritosa.Translator;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@AllArgsConstructor
public class SelectKitMenu extends CenteredMenu {

    private final DuelsAddon plugin;
    private final Predicate<Kit> filter;
    private final Consumer<Kit> onSelect;
    private final Runnable onSelectRandom;
    private final Consumer<Player> onSelectOwnInventory;
    private final Menu backMenu;

    public SelectKitMenu(DuelsAddon plugin, Predicate<Kit> filter, Consumer<Kit> onSelect, Runnable onSelectRandom) {
        this.plugin = plugin;
        this.filter = filter;
        this.onSelect = onSelect;
        this.onSelectRandom = onSelectRandom;
        this.backMenu = null;
        this.onSelectOwnInventory = null;
    }

    public SelectKitMenu(DuelsAddon plugin, Predicate<Kit> filter, Consumer<Kit> onSelect) {
        this.plugin = plugin;
        this.filter = filter;
        this.onSelect = onSelect;
        this.onSelectRandom = null;
        this.backMenu = null;
        this.onSelectOwnInventory = null;
    }

    @Override
    public int getRows(Player player) {
        return 4;
    }

    @Override
    public List<Button> getAllItems(Player player) {
        return plugin.getKitService().getKits().stream()
                .filter(filter)
                .map(KitButton::new)
                .collect(Collectors.toList());
    }

    @Override
    public Map<Integer, Button> getGlobalButtons(Player player) {
        Map<Integer, Button> buttons = Maps.newHashMap();

        Translator translator = plugin.getServices().getTranslator();

        if (onSelectRandom != null){
            buttons.put(4, Button.of(new ItemBuilder(Material.NETHER_STAR)
                    .setName(translator.getAsText(player, "KIT.MENU.RANDOM.NAME"))
                    .addLore(translator.getListString(player, "KIT.MENU.RANDOM.LORE"))
                    .build(), (clicker, clickType) -> onSelectRandom.run()));
        }

        if (onSelectOwnInventory != null) {
            buttons.put(5, Button.of(new ItemBuilder(Material.CRAFTING_TABLE)
                    .setName(translator.getAsText(player, "KIT.MENU.OWN_INVENTORY.NAME"))
                    .addLore(translator.getListString(player, "KIT.MENU.OWN_INVENTORY.LORE"))
                    .build(), (clicker, clickType) -> {
                onSelectOwnInventory.accept(clicker);
                backMenu.open(clicker);
            }));
        }

        if (backMenu != null) {
            buttons.put(0, new BackButton(translator, backMenu));
        }

        return buttons;
    }

    @Override
    public String getTitle(Player player) {
        return "Select kit";
    }

    @RequiredArgsConstructor
    public class KitButton extends Button {

        private final Kit kit;

        @Override
        public ItemStack getDisplayItem(Player player) {
            ItemBuilder builder = new ItemBuilder(kit.getDisplayItem());

            builder.setName(kit.getName());

            return builder.build();
        }

        @Override
        public void clicked(Player player) {
            onSelect.accept(kit);
        }
    }
}
