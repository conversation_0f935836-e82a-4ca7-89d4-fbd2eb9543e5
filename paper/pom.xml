<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.contrum.prisma</groupId>
        <artifactId>PrismaCore</artifactId>
        <version>2.1-SNAPSHOT</version>
    </parent>

    <artifactId>paper</artifactId>

    <repositories>
         <repository>
            <id>jitpack.io</id>
            <url>https://jitpack.io</url>
        </repository>

        <repository>
            <id>PlugMan</id>
            <url>https://raw.githubusercontent.com/TheBlackEntity/PlugMan/repository/</url>
        </repository>

        <repository>
            <id>nms-repo</id>
            <url>https://repo.codemc.io/repository/nms/</url>
        </repository>

        <repository>
            <id>codemc-repo</id>
            <url>https://repo.codemc.io/repository/maven-public/</url>
            <layout>default</layout>
        </repository>
        <repository>
            <id>sk89q-repo</id>
            <url>https://maven.enginehub.org/repo/</url>
        </repository>

        <repository>
            <id>dv8tion</id>
            <name>m2-dv8tion</name>
            <url>https://m2.dv8tion.net/releases</url>
        </repository>

        <repository>
            <id>coreprotect</id>
            <url>https://maven.playpro.com</url>
        </repository>

        <repository>
            <id>citizens-repo</id>
            <url>https://maven.citizensnpcs.co/repo</url>
        </repository>

        <repository>
            <id>codemc-releases</id>
            <url>https://repo.codemc.io/repository/maven-releases/</url>
        </repository>

        <repository>
            <id>aikar-releases</id>
            <url>https://repo.aikar.co/nexus/content/repositories/aikar-release/</url>
        </repository>

        <repository>
            <id>lunarclient</id>
            <url>https://repo.lunarclient.dev</url>
        </repository>

        <repository>
            <id>sonatype-snapshots</id>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
        </repository>

        <repository>
            <id>fancyplugins-releases</id>
            <name>FancyPlugins Repository</name>
            <url>https://repo.fancyplugins.de/releases</url>
        </repository>

        <repository>
            <id>fancyinnovations-releases</id>
            <name>FancyInnovations Repository</name>
            <url>https://repo.fancyinnovations.com/releases</url>
        </repository>

        <repository>
            <id>feather-repo</id>
            <url>https://repo.feathermc.net/artifactory/maven-releases</url>
        </repository>

        <repository>
            <id>aikar</id>
            <url>https://repo.aikar.co/content/groups/aikar/</url>
        </repository>

        <repository>
            <id>placeholderapi</id>
            <url>https://repo.extendedclip.com/releases/</url>
        </repository>

        <repository>
            <id>opencollab-snapshot</id>
            <url>https://repo.opencollab.dev/main/</url>
        </repository>

        <repository>
            <id>contrum-repo</id>
            <url>https://repo.contrum.org/private</url>
        </repository>
    </repositories>

    <dependencies>
        <dependency>
            <groupId>org.purpurmc.purpur</groupId>
            <artifactId>purpur-api</artifactId>
            <version>1.21.5-R0.1-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.11.0</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.github.NEZNAMY</groupId>
            <artifactId>TAB-API</artifactId>
            <version>5.0.3</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.mojang</groupId>
            <artifactId>authlib</artifactId>
            <version>1.11</version>
            <scope>provided</scope>
        </dependency>


        <dependency>
            <groupId>fr.mrmicky</groupId>
            <artifactId>fastboard</artifactId>
            <version>2.1.4</version>
        </dependency>

        <dependency>
            <groupId>joserodpt</groupId>
            <artifactId>RealMinesAPI</artifactId>
            <version>1.8.8</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.contrum.prisma</groupId>
            <artifactId>shared</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>net.citizensnpcs</groupId>
            <artifactId>citizens-main</artifactId>
            <version>2.0.33-SNAPSHOT</version>
            <type>jar</type>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>de.oliver</groupId>
            <artifactId>FancyNpcs</artifactId>
            <version>2.5.2</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>de.oliver</groupId>
            <artifactId>FancyHolograms</artifactId>
            <version>2.7.0</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.github.PlaceholderAPI</groupId>
            <artifactId>PlaceholderAPI</artifactId>
            <version>2.11.6</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.sk89q.worldguard</groupId>
            <artifactId>worldguard-bukkit</artifactId>
            <version>7.0.7-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.drtshock</groupId>
            <artifactId>PlayerVaultsX</artifactId>
            <version>4.3.0</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.github.decentsoftware-eu</groupId>
            <artifactId>decentholograms</artifactId>
            <version>2.8.5</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>de.tr7zw</groupId>
            <artifactId>item-nbt-api</artifactId>
            <version>2.12.1</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>co.aikar</groupId>
            <artifactId>acf-paper</artifactId> <!-- Don't forget to replace this -->
            <version>0.5.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.github.retrooper</groupId>
            <artifactId>packetevents-spigot</artifactId>
            <version>2.8.0</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>net.kyori</groupId>
            <artifactId>adventure-text-minimessage</artifactId>
            <version>4.17.0</version>
        </dependency>

        <dependency>
            <groupId>com.lunarclient</groupId>
            <artifactId>apollo-api</artifactId>
            <version>1.1.4</version>
            <scope>provided</scope>
        </dependency>

        <!-- Download and compile from repo: https://github.com/BadlionClient/BadlionClientModAPI -->
        <dependency>
            <groupId>net.badlion</groupId>
            <artifactId>bukkit-1.17</artifactId>
            <version>2.1.7</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>me.lucko</groupId>
            <artifactId>spark-bukkit</artifactId>
            <version>1.10.136-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>dev.samstevens.totp</groupId>
            <artifactId>totp</artifactId>
            <version>1.7.1</version>
        </dependency>

        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.8.0</version>
        </dependency>

        <dependency>
            <groupId>com.github.UlrichBR</groupId>
            <artifactId>UClansV7-API</artifactId>
            <version>7.1.0</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.contrum.tritosa</groupId>
            <artifactId>api</artifactId>
            <version>2.0.4-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.geysermc.floodgate</groupId>
            <artifactId>api</artifactId>
            <version>2.2.4-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>net.digitalingot.feather-server-api</groupId>
            <artifactId>api</artifactId>
            <version>0.0.5</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <defaultGoal>clean install</defaultGoal>
        <finalName>PrismaCore</finalName>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <version>3.6.1</version>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>

                <configuration>
                    <encoding>UTF-8</encoding>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.0.0</version>

                <configuration>
                    <createDependencyReducedPom>false</createDependencyReducedPom>
                    <shadedArtifactAttached>false</shadedArtifactAttached>
                </configuration>

                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>
</project>