package org.contrum.prisma.deathmessage;

import org.contrum.chorpu.configuration.annotation.ConfigProperty;

public class DeathMessageSetting {

    @ConfigProperty(path = "services_settings.DEATH_MESSAGES.ENABLED")
    public static boolean DEATH_MESSAGE_ENABLED = true;
    @ConfigProperty(path = "services_settings.DEATH_MESSAGES.SEND_WHEN_CHAT_MUTED")
    public static boolean SEND_WHEN_CHAT_MUTED = false;
}
