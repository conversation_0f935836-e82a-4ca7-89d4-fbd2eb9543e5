package org.contrum.prisma.deathmessage;

import org.bukkit.Bukkit;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.contrum.prisma.PaperServices;

public class DeathMessageService implements Listener {

    private final PaperServices services;

    public DeathMessageService(PaperServices services) {
        this.services = services;
        services.getMainConfig().register(DeathMessageSetting.class, null);

        Bukkit.getPluginManager().registerEvents(new DeathMessageListener(services), services.getPlugin());
    }
}
