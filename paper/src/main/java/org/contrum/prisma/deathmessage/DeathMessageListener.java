package org.contrum.prisma.deathmessage;

import lombok.RequiredArgsConstructor;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.utils.PlaceholdersUtil;
import org.contrum.prisma.utils.PlayerUtils;

@RequiredArgsConstructor
public class DeathMessageListener implements Listener {

    private final PaperServices services;

    @EventHandler(priority = EventPriority.LOW)
    public void onDeath(PlayerDeathEvent event) {
        if (!DeathMessageSetting.DEATH_MESSAGE_ENABLED) {
            return;
        }

        if (!DeathMessageSetting.SEND_WHEN_CHAT_MUTED && services.getChatService().isGlobalChatMuted()) {
            return;
        }

        //ASync broadcast
        TaskUtil.runAsync(services.getPlugin(), () -> {
            Player killed = event.getEntity();
            Profile user = services.getProfileService().getProfile(killed.getUniqueId());
            if (user == null) return;
            Player killer = killed.getKiller();


            if (killer == null) {
                services.getTranslator().broadcast("DEATH_MESSAGE.DEATH", user);
                return;
            }

            String key = PlayerUtils.isInDuel(killer) ? "DEATH_MESSAGE.KILLED_DUEL" : "DEATH_MESSAGE.KILLED";
            services.getTranslator().broadcast(key,
                    PlaceholdersUtil.sender("killed", killed),
                    PlaceholdersUtil.sender("killer", killer));
        });
    }

}
