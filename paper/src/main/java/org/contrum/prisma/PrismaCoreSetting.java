package org.contrum.prisma;

import org.contrum.chorpu.configuration.annotation.ConfigProperty;
import org.contrum.prisma.enderchest.section.EnderChestSection;
import org.contrum.prisma.enderchest.section.EnderChestSectionSerializer;
import org.contrum.prisma.queue.priority.QueuePriority;
import org.contrum.prisma.queue.priority.QueuePrioritySerializer;
import org.contrum.prisma.utils.config.serializer.StringMapSerializer;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PrismaCoreSetting {

    @ConfigProperty(path = "services_settings.ABILITIES.GLOBAL_COOLDOWN")
    public static String GLOBAL_ABILITY_COOLDOWN = "4s";

    @ConfigProperty(path = "services_settings.HIDDEN_PLAYERS")
    public static List<String> HIDDEN_PLAYERS = new ArrayList<>();

    @ConfigProperty(path = "services_settings.ON_JOIN.SEND_TO_SPAWN")
    public static boolean SEND_TO_SPAWN_ON_JOIN = true;

    @ConfigProperty(path = "services_settings.ON_JOIN.SEND_TITLES")
    public static boolean SEND_TITLES_ON_JOIN = true;

    @ConfigProperty(path = "services_settings.CUSTOM-DROPS.ENABLED")
    public static boolean CUSTOM_DROPS_ENABLED = true;

    @ConfigProperty(path = "services_settings.CUSTOM-DROPS.DATABASE")
    public static String CUSTOM_DROPS_DATABASE = "customdrops";

    @ConfigProperty(path = "services_settings.CUSTOM-DROPS.TYPE")
    public static String CUSTOM_DROPS_TYPE = "OLD";

    @ConfigProperty(path = "services_settings.TOMBSTONES.ENABLED")
    public static boolean TOMBSTONES_ENABLED = false;

    @ConfigProperty(path = "services_settings.TOMBSTONES.TIME-TO-STEAL")
    public static int TOMBSTONES_TIME_TO_STEAL = 60;

    @ConfigProperty(path = "services_settings.TOMBSTONES.TIME-TO-DESTROY")
    public static int TOMBSTONES_TIME_TO_DESTROY = 120;

    @ConfigProperty(path = "services_settings.VOTE.COMMANDS")
    public static List<String> VOTE_COMMANDS = new ArrayList<>();

    @ConfigProperty(path = "services_settings.WARPS.ENABLED")
    public static boolean WARPS_ENABLED = true;

    @ConfigProperty(path = "services_settings.WARPS.MENU_ROWS")
    public static int WARPS_MENU_ROWS = 3;

    @ConfigProperty(path = "services_settings.WARPS.DATABASE")
    public static String WARPS_DATABASE = "";

    @ConfigProperty(path = "services_settings.CANCEL-SUFFOCATION")
    public static boolean CANCEL_SUFFOCATION = true;

    @ConfigProperty(path = "services_settings.CANCEL-COMPOST")
    public static boolean CANCEL_COMPOST = true;

    @ConfigProperty(path = "services_settings.COMBAT-TAG.ENABLED")
    public static boolean COMBAT_TAG_ENABLED = true;

    @ConfigProperty(path = "services_settings.COMBAT-TAG.BLACKLISTED_WORLDS")
    public static List<String> COMBAT_TAG_BLACKLIST_WORLDS = new ArrayList<>();

    @ConfigProperty(path = "services_settings.COMBAT-TAG.TIME")
    public static String COMBAT_TAG_TIME = "10s";

    @ConfigProperty(path = "services_settings.CUSTOM-ARMOR.ENABLED")
    public static boolean CUSTOM_ARMOR_ENABLED = true;

    @ConfigProperty(path = "services_settings.CUSTOM-ARMOR.ARMOR_REGIONS", serializeWith = StringMapSerializer.class)
    public static Map<String, String> CUSTOM_ARMOR_REGIONS = new HashMap<>();

    @ConfigProperty(path = "services_settings.STAFF-EVIDENCES.ENABLED")
    public static boolean STAFF_EVIDENCES_ENABLED = true;

    @ConfigProperty(path = "SPAWN_NOTIFICATION")
    public static boolean SPAWN_NOTIFICATION = true;

    @ConfigProperty(path = "services_settings.ON_JOIN.SEND_TO_SPAWN")
    public static boolean SPAWN_TELEPORT_ON_JOIN = true;

    @ConfigProperty(path = "services_settings.ON_JOIN.SPAWN_WORLD")
    public static String SPAWN_TELEPORT_WORLD = "world";

    @ConfigProperty(path = "services_settings.ANTI-DUPE.ENABLED")
    public static boolean ANTIDUPE_ENABLED = false;

    @ConfigProperty(path = "services_settings.SCOREBOARD.ENABLED")
    public static boolean SCOREBOARD_ENABLED = true;

    @ConfigProperty(path = "services_settings.SPARK_MONITOR.ENABLED")
    public static boolean SPARK_MONITOR_ENABLED = true;

    @ConfigProperty(path = "services_settings.SPARK_MONITOR.DATA_WEBHOOK")
    public static String SPARK_DATA_WEBHOOK = "";

    @ConfigProperty(path = "services_settings.TUTORIAL.TYPE")
    public static String TUTORIAL_TYPE = "None";

    @ConfigProperty(path = "services_settings.SECURITY.ENABLED")
    public static boolean SECURITY_ENABLED = true;

    @ConfigProperty(path = "services_settings.SECURITY.ENABLED_2FA")
    public static boolean TWO_FACTOR_AUTH_ENABLED = true;

    @ConfigProperty(path = "services_settings.MINES_COMMAND.ENABLED")
    public static boolean MINES_COMMAND_ENABLED = false;

    @ConfigProperty(path = "services_settings.MINES_COMMAND.TELEPORT_COOLDOWN")
    public static String MINES_COMMAND_TELEPORT_COOLDOWN = "15s";

    @ConfigProperty(path = "services_settings.MINES_COMMAND.RESET_COOLDOWN")
    public static String MINES_COMMAND_RESET_COOLDOWN = "1h";

    @ConfigProperty(path = "services_settings.MINES_COMMAND.MINES")
    public static List<String> MINES_COMMAND_MINES = new ArrayList<>();

    @ConfigProperty(path = "services_settings.EVENTS.ENABLED")
    public static boolean EVENTS_ENABLED = false;

    @ConfigProperty(path = "services_settings.EVENTS.START_EVERY")
    public static String EVENTS_START_COOLDOWN = "3h";

    @ConfigProperty(path = "services_settings.ECONOMY.DROP_MONEY_ON_DEATH")
    public static boolean ECONOMY_DROP_MONEY_ON_DEATH = false;

    @ConfigProperty(path = "services_settings.ECONOMY.MONEY_DROP_PERCENTAGE")
    public static double ECONOMY_MONEY_DROP_PERCENTAGE = 5;

    @ConfigProperty(path = "services_settings.CHAT.CHAT_FILTERED_WORDS")
    public static List<String> CHAT_FILTERED_WORDS = new ArrayList<>();

    @ConfigProperty(path = "services_settings.QUEUE.GROUPS", serializeWith = QueuePrioritySerializer.class)
    public static List<QueuePriority> QUEUE_PRIORITIES = new ArrayList<>();

    @ConfigProperty(path = "services_settings.DISGUISE.COOLDOWN")
    public static String DISGUISE_COOLDOWN = "30m";

    @ConfigProperty(path = "services_settings.DISGUISE.NICK_COOLDOWN")
    public static String DISGUISE_NICK_COOLDOWN = "2d";

    @ConfigProperty(path = "services_settings.CLIENTS.TEAM_VIEW_ENABLED")
    public static boolean TEAM_VIEW_ENABLED = false;

    @ConfigProperty(path = "services_settings.ENDER_CHEST.ENABLED")
    public static boolean CUSTOM_ENDER_CHEST_ENABLED = false;

    @ConfigProperty(path = "services_settings.ENDER_CHEST.SECTIONS", serializeWith = EnderChestSectionSerializer.class)
    public static List<EnderChestSection> CUSTOM_ENDER_CHEST_SECTIONS = new ArrayList<>();

    @ConfigProperty(path = "services_settings.GG_WAVE.REWARDS")
    public static List<String> GG_WAVE_REWARDS = new ArrayList<>();

    @ConfigProperty(path = "services_settings.STATISTICS.ENABLED")
    public static boolean STATISTICS_ENABLED = false;

    @ConfigProperty(path = "services_settings.JOIN_ME.ENABLED")
    public static boolean JOIN_ME_ENABLED = false;

    @ConfigProperty(path = "services_settings.TRADE_DROP_ITEMS")
    public static boolean TRADE_DROP_ITEMS = false;

    @ConfigProperty(path = "BasketName")
    public static List<String> BASKET_NAMES = List.of("6k2", "yMiller", "Aleexks", "izLoki");
}
