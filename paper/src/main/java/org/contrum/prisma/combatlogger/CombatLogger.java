/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by izLoki on 09/05/2024
 * Website: contrum.org
 */

package org.contrum.prisma.combatlogger;

import lombok.Getter;
import lombok.Setter;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Statistic;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.entity.Villager;
import org.bukkit.inventory.ItemStack;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitTask;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.utils.Cooldown;
import org.contrum.tritosa.placeholder.LocalPlaceholders;
import org.jetbrains.annotations.Nullable;

import java.time.Duration;
import java.util.UUID;

@Getter
public class CombatLogger {
    private final PaperServices services;
    @Getter
    private final Villager entity;
    private final Player player;
    private final String nick;
    private final UUID uuid;
    private final Location location;

    private BukkitTask tickTask;

    @Setter
    private ItemStack[] contents;
    private Cooldown despawnCooldown;
    private boolean isAlive;

    public CombatLogger(PaperServices services, Player player){
        this.services = services;
        this.player = player;
        this.nick = player.getName();
        this.uuid = player.getUniqueId();
        this.location = player.getLocation();
        this.isAlive = true;

        this.contents = player.getInventory().getContents();
        this.despawnCooldown = new Cooldown(Duration.ofSeconds(30));
        this.entity = (Villager) location.getWorld().spawnEntity(location, EntityType.VILLAGER);

        entity.setMetadata("CombatLogger", new FixedMetadataValue(services.getPlugin(), player.getUniqueId().toString()));
        entity.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, Integer.MAX_VALUE, 254, false, false));
        entity.setSilent(true);
        entity.setCustomNameVisible(true);
        entity.customName(services.getTranslator().getAsComponent(Bukkit.getServer().getConsoleSender(), "COMBAT_LOGGER.DISPLAY_NAME", player));
        tickTask = Bukkit.getScheduler().runTaskTimer(services.getPlugin(), this::tick, 0L, 1L);
    }

    public void unregister() {
        this.isAlive = false;

        if (this.tickTask != null && !this.tickTask.isCancelled()) tickTask.cancel();

        if (entity != null && !entity.isDead()){
            entity.remove();
        }
    }

    private void tick(){
        if (despawnCooldown.hasExpired()) this.unregister();
    }

    public boolean isAlive() {
        return isAlive;
    }

    public UUID getPlayerUUID() {
        return uuid;
    }

    public String getPlayerNick() {
        return nick;
    }

    public Location getCurrentLocation() {
        return (entity != null && !entity.isDead()) ? entity.getLocation() : location;
    }

    public void broadcastDeath(@Nullable Player killer){
        if (killer != null){
            services.getTranslator().broadcast("COMBAT_LOGGER.DEATH_MESSAGE", LocalPlaceholders.builder()
                    .add("<logger_nick>", nick)
                    .add("<logger_kills>", String.valueOf(player != null ? player.getStatistic(Statistic.PLAYER_KILLS) : -1))
                    .add("<killer_nick>", killer.getName())
                    .add("<killer_kills>", String.valueOf(killer.getStatistic(Statistic.PLAYER_KILLS))));
        } else {
            services.getTranslator().broadcast("COMBAT_LOGGER.DEATH_MESSAGE_GENERIC", LocalPlaceholders.builder()
                    .add("<logger_nick>", nick)
                    .add("<logger_kills>", String.valueOf(player != null ? player.getStatistic(Statistic.PLAYER_KILLS) : -1)));
        }
    }

    public String getDeathMessage(@Nullable Player killer){
        if (killer != null){
            return services.getTranslator().getAsText(killer, "COMBAT_LOGGER.DEATH_MESSAGE", LocalPlaceholders.builder()
                    .add("<logger_nick>", nick)
                    .add("<logger_kills>", String.valueOf(player != null ? player.getStatistic(Statistic.PLAYER_KILLS) : -1))
                    .add("<killer_nick>", killer.getName())
                    .add("<killer_kills>", String.valueOf(killer.getStatistic(Statistic.PLAYER_KILLS))));
        } else {
            return services.getTranslator().getAsText(Bukkit.getConsoleSender(), "COMBAT_LOGGER.DEATH_MESSAGE_GENERIC", LocalPlaceholders.builder()
                    .add("<logger_nick>", nick)
                    .add("<logger_kills>", String.valueOf(player != null ? player.getStatistic(Statistic.PLAYER_KILLS) : -1)));
        }
    }
}
