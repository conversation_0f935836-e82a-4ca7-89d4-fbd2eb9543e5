/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by izLoki on 06/05/2024
 * Website: contrum.org
 */

package org.contrum.prisma.combatlogger.events;

import com.google.common.base.Preconditions;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Villager;
import org.bukkit.event.Cancellable;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.contrum.prisma.combatlogger.CombatLogger;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Getter @Setter
public class CombatLoggerDeathEvent extends Event implements Cancellable {

    // HandlerList instance for managing event handlers.
    private static final HandlerList handlers = new HandlerList();

    // Boolean flag to indicate if the event is cancelled.
    private boolean cancelled = false;

    // The CombatLogger entity villager associated with this event.
    private final Villager entity;
    // The CombatLogger associated with this event.
    private final CombatLogger combatLogger;
    // The EntityDamageEvent responsible for the combat-logger's death.
    private final EntityDamageEvent damageEvent;
    // The combat logger deathMessage
    @Nullable
    private String deathMessage;
    // The entity who killed the combat-logger
    @Nullable
    private Entity killer;
    // Boolean flag to indicate if combat-logger items should be dropped.
    private boolean dropItems;


    /**
     * Constructor for the CombatLoggerDieEvent.
     *
     * @param combatLogger The CombatLogger associated with this event.
     * @param damageEvent The EntityDamageEvent responsible for the combat-logger's death.
     */
    public CombatLoggerDeathEvent(CombatLogger combatLogger, EntityDamageEvent damageEvent, String deathMessage){
        Preconditions.checkNotNull(combatLogger, "The combat-logger cannot be null!");
        this.combatLogger = combatLogger;
        this.damageEvent = damageEvent;
        this.entity = combatLogger.getEntity();
        this.deathMessage = deathMessage;
        this.dropItems = true;
        if (damageEvent instanceof EntityDamageByEntityEvent event){
            this.killer = event.getDamager();
        }
    }

    /**
     * Checks if the event is cancelled.
     *
     * @return true if the event is cancelled, false otherwise.
     */
    @Override
    public boolean isCancelled() {
        return cancelled;
    }

    /**
     * Sets the cancelled status of the event.
     *
     * @param b The cancelled status to be set.
     */
    @Override
    public void setCancelled(boolean b) {
        cancelled = b;
    }

    /**
     * Retrieves the handlers for this event.
     *
     * @return The handlers for this event.
     */
    @Override
    public @NotNull HandlerList getHandlers() {
        return handlers;
    }

    /**
     * Retrieves the HandlerList instance.
     *
     * @return The HandlerList instance.
     */
    public static HandlerList getHandlerList() {
        return handlers;
    }
}

