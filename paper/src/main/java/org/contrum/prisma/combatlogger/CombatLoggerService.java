/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by izLoki on 08/05/2024
 * Website: contrum.org
 */

package org.contrum.prisma.combatlogger;

import com.google.common.base.Preconditions;
import net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer;
import org.bukkit.Bukkit;
import org.bukkit.GameMode;
import org.bukkit.Location;
import org.bukkit.Statistic;
import org.bukkit.damage.DamageSource;
import org.bukkit.damage.DamageType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.inventory.ItemStack;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.PrismaCoreSetting;
import org.contrum.prisma.combatlogger.events.CombatLoggerDeathEvent;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;

import java.util.*;

public class CombatLoggerService implements Listener {
    private final PaperServices services;
    private final HashMap<UUID, CombatLogger> loggers = new HashMap<>();

    public CombatLoggerService(PaperServices services) {
        this.services = services;

        Bukkit.getPluginManager().registerEvents(this, services.getPlugin());
    }

    public void createCombatLogger(Player player) {
        Preconditions.checkArgument(PrismaCoreSetting.COMBAT_TAG_ENABLED, "CombatLoggers are not enabled!");
        CombatLogger combatLogger = new CombatLogger(services, player);
        loggers.put(player.getUniqueId(), combatLogger);
    }

    public void removeLogger(Player player) {
        CombatLogger combatLogger = loggers.get(player.getUniqueId());
        if (combatLogger != null && combatLogger.isAlive()) combatLogger.unregister();

        loggers.remove(player.getUniqueId());
    }

    public boolean hasCombatLogger(Player player) {
        return this.getCombatLogger(player) != null && this.getCombatLogger(player).isAlive();
    }

    public CombatLogger getCombatLogger(Player player) {
        return this.loggers.get(player.getUniqueId());
    }

    public CombatLogger getCombatLogger(UUID uuid) {
        return this.loggers.get(uuid);
    }

    @EventHandler(priority = EventPriority.LOW)
    public void playerQuitEvent(PlayerQuitEvent event){
        Player player = event.getPlayer();
        ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
        if (metadata.isTagged()) {
            if (event.getPlayer().getGameMode().equals(GameMode.CREATIVE) || services.getStaffModeService().isOnStaffMode(event.getPlayer()) || services.getStaffModeService().isVanished(event.getPlayer())) {
                return;
            }
            this.createCombatLogger(player);
        }
    }

    @EventHandler(priority = EventPriority.HIGHEST, ignoreCancelled = true)
    public void die(EntityDamageEvent event) {
        if (event.getEntity().hasMetadata("CombatLogger")) {
            String owner = event.getEntity().getMetadata("CombatLogger").get(0).asString();
            if (owner.isEmpty()) return;

            UUID uuid = UUID.fromString(owner);
            CombatLogger combatLogger = this.loggers.get(uuid);
            if (combatLogger == null) return;

            if (combatLogger.isAlive()) {
                //Cancel fall and suffocation damages
                if (event.getCause().equals(EntityDamageEvent.DamageCause.FALL) || event.getCause().equals(EntityDamageEvent.DamageCause.SUFFOCATION)) {
                    event.setCancelled(true);
                    return;
                }

                LivingEntity e = (LivingEntity) event.getEntity();
                double newHealth = e.getHealth() - event.getFinalDamage();
                if (newHealth <= 0){ //Check if entity should death.
                    //Broadcast death message and give player kill
                    Player killer = event instanceof EntityDamageByEntityEvent damageEvent && damageEvent.getDamager() instanceof Player ? (Player) damageEvent.getDamager() : null;
                    String deathMessage = combatLogger.getDeathMessage(killer);

                    //Call the combat-logger's death event
                    CombatLoggerDeathEvent deathEvent = new CombatLoggerDeathEvent(combatLogger, event, deathMessage);
                    if (!deathEvent.callEvent()){ //Cancel damage if event is cancelled
                        event.setCancelled(true);
                        return;
                    }

                    //Broadcast message and add one kill to killer
                    combatLogger.broadcastDeath(killer);
                    if (killer != null){
                        killer.incrementStatistic(Statistic.PLAYER_KILLS);
                    }

                    //Clear player inventory and teleport to spawn
                    List<ItemStack> dropItems = new ArrayList<>(Arrays.asList(combatLogger.getContents()));
                    Player player = combatLogger.getPlayer();
                    if (player != null) {
                        //Call player death event
                        PlayerDeathEvent playerDeathEvent = new PlayerDeathEvent(player, DamageSource.builder(DamageType.PLAYER_ATTACK).withCausingEntity(killer).build(), dropItems, 0, LegacyComponentSerializer.legacyAmpersand().deserialize(deathMessage), true);
                        playerDeathEvent.callEvent();
                        dropItems = playerDeathEvent.getDrops();

                        player.getInventory().clear();
                        playerDeathEvent.getItemsToKeep().forEach(i -> player.getInventory().addItem(i));
                        player.teleportOffline(combatLogger.getLocation().getWorld().getSpawnLocation());
                        player.saveData();
                    }

                    //Drop items & unregister
                    if (deathEvent.isDropItems()){
                        Location loc = combatLogger.getCurrentLocation();
                        for (ItemStack item : dropItems) {
                            if (item == null) continue;
                            loc.getWorld().dropItemNaturally(loc, item);
                        }
                    }

                    combatLogger.unregister();
                }
            }
        }
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void playerRejoin(PlayerJoinEvent event){
        CombatLogger combatLogger = this.loggers.get(event.getPlayer().getUniqueId());
        if (combatLogger == null) return;

        if (combatLogger.isAlive()) {
            combatLogger.unregister();
            event.getPlayer().teleport(combatLogger.getCurrentLocation());
        }
    }
}