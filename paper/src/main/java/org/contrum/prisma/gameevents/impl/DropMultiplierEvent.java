package org.contrum.prisma.gameevents.impl;

import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.events.BlockDropEvent;
import org.contrum.prisma.events.CurrencyAddEvent;
import org.contrum.prisma.gameevents.Event;

import java.time.Duration;

public class DropMultiplierEvent extends Event {

    public DropMultiplierEvent(PaperServices services) {
        super(services);
    }

    @Override
    public Duration getDuration() {
        return Duration.ofMinutes(5);
    }

    @Override
    public String getDescription() {
        return "&fTodo los bloques &ax2&f, ¡mina todo lo posible!";
    }

    @Override
    public String getFormattedDisplayName() {
        return "Multiplicador x2";
    }

    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void multiply(BlockDropEvent event){
        if (this.isActive()){
            event.setAmount(event.getAmount()+1);
        }
    }

    @EventHandler
    public void coinsAdd(CurrencyAddEvent event){
        if (event.getCause().equals(CurrencyAddEvent.Cause.BLOCK_BREAK)){
            event.setAmount(event.getAmount()*2);
        }
    }
}
