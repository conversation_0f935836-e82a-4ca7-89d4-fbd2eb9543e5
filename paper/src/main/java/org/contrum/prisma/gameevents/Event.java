package org.contrum.prisma.gameevents;

import lombok.Getter;
import lombok.Setter;
import org.bukkit.Bukkit;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.utils.time.TimeUtils;

import java.time.Duration;
import java.time.Instant;

@Getter @Setter
public abstract class Event implements Listener {
    private final PaperServices services;
    private boolean active = false;
    private Instant startTime;

    public Event(PaperServices services){
        this.services = services;
    }

    public void start(){
        this.startTime = Instant.now();
        this.active = true;
        Bukkit.getPluginManager().registerEvents(this, services.getPlugin());
    }

    public void stop(){
        this.active = false;
        HandlerList.unregisterAll(this);
    }

    public abstract Duration getDuration();

    public abstract String getDescription();

    public abstract String getFormattedDisplayName();

    public long getRemainingTime(){
        return startTime.plus(this.getDuration()).toEpochMilli() - System.currentTimeMillis();
    }

    public String getRemainingTimeFormatted(){
        long remaining = startTime.plus(this.getDuration()).toEpochMilli() - System.currentTimeMillis();
        return remaining > 0 ? TimeUtils.getFormattedTime(remaining, false) : "Finalizado";
    }
}
