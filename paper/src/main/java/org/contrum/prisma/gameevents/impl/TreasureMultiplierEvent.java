package org.contrum.prisma.gameevents.impl;

import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.events.TreasureChestRefillEvent;
import org.contrum.prisma.gameevents.Event;

import java.time.Duration;

public class TreasureMultiplierEvent extends Event {

    public TreasureMultiplierEvent(PaperServices services) {
        super(services);
    }

    @Override
    public Duration getDuration() {
        return Duration.ofMinutes(5);
    }

    @Override
    public String getDescription() {
        return "&fTodos los cofres &6mejorados&f, ¡ve y agarra el tuyo!";
    }

    @Override
    public String getFormattedDisplayName() {
        return "Cofres x2";
    }

    @EventHandler(priority = EventPriority.HIGH, ignoreCancelled = true)
    public void playerTotem(TreasureChestRefillEvent event){
        if (this.isActive()){
            for (ItemStack item : event.getItems().values()){
                int newAmount = item.getAmount()*2;
                if (newAmount > item.getMaxStackSize()) newAmount = item.getMaxStackSize();
                item.setAmount(newAmount);
            }
        }
    }
}
