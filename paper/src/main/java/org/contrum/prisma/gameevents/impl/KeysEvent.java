package org.contrum.prisma.gameevents.impl;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.events.LegalPlayerKillEvent;
import org.contrum.prisma.gameevents.Event;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

public class KeysEvent extends Event {
    private final HashMap<String, Integer> keys = new HashMap<>(Map.of(
            "comun", 5,
            "rara", 2,
            "kits_key", 1));

    public KeysEvent(PaperServices services) {
        super(services);
    }

    @Override
    public Duration getDuration() {
        return Duration.ofMinutes(5);
    }

    @Override
    public String getDescription() {
        return "&fAhora &casesinar &fa un jugador te dará &dllaves&f.";
    }

    @Override
    public String getFormattedDisplayName() {
        return "Drop keys";
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void deathEvent(LegalPlayerKillEvent event){
        Player player = event.getPlayer();
        Player killer = player.getKiller();
        if (killer != null){
            List<Map.Entry<String, Integer>> entries = List.copyOf(this.keys.entrySet());
            Map.Entry<String, Integer> entry = entries.get(new Random().nextInt(entries.size()));

            String keyName = entry.getKey();
            int amount = entry.getValue();

            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), "cr key give " + killer.getName() + " " + keyName + " " + amount);
            killer.sendMessage(CC.translate("&aHas recibido x"+amount+" key &e"+keyName+"&a por &casesinar &aa &e" + player.getName()));
        }
    }
}
