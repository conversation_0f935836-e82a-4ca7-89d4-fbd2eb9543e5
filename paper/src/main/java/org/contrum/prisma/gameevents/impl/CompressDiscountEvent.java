package org.contrum.prisma.gameevents.impl;

import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customarmor.CustomArmor;
import org.contrum.prisma.gameevents.Event;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

public class CompressDiscountEvent extends Event {

    private List<CustomArmor> discountArmors = new ArrayList<>();

    public CompressDiscountEvent(PaperServices services) {
        super(services);
    }

    @Override
    public void start(){
        super.start();
        discountArmors.clear();
        for (CustomArmor armor : this.getServices().getCustomArmorService().getCustomArmors().values()) {
            if (armor.getCompressRequired() <= 1) continue;
            armor.setCompressRequired(armor.getCompressRequired()-1);
            discountArmors.add(armor);
        }
    }

    @Override
    public void stop(){
        super.stop();
        for (CustomArmor armor : this.discountArmors) {
            armor.setCompressRequired(armor.getCompressRequired()+1);
        }
        this.discountArmors.clear();
    }

    @Override
    public Duration getDuration() {
        return Duration.ofMinutes(5);
    }

    @Override
    public String getDescription() {
        return "&fArmaduras &6baratas&f, ¡aprovecha y tradea!";
    }

    @Override
    public String getFormattedDisplayName() {
        return "Descuento";
    }
}
