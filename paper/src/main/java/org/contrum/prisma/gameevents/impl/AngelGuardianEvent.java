package org.contrum.prisma.gameevents.impl;

import org.bukkit.EntityEffect;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.gameevents.Event;

import java.time.Duration;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

public class AngelGuardianEvent extends Event {
    private final Set<UUID> resurrectedPlayers = new HashSet<>();

    public AngelGuardianEvent(PaperServices services) {
        super(services);
    }

    @Override
    public void start() {
        super.start();
        this.resurrectedPlayers.clear();
    }

    @Override
    public Duration getDuration() {
        return Duration.ofMinutes(5);
    }

    @Override
    public String getDescription() {
        return "&fRecibirás un &6totem &fgratis antes de morir.";
    }

    @Override
    public String getFormattedDisplayName() {
        return "Angel guardian";
    }

    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void playerTotem(EntityDamageEvent event){
        if (this.isActive() && event.getEntity() instanceof Player player){
            if ((player.getHealth() - event.getFinalDamage()) <= 0.0 && resurrectedPlayers.add(player.getUniqueId())){
                event.setDamage(0);
                player.sendMessage(CC.translate("&aHas sido revivido por el &eangel guardian!"));
                player.setHealth(8);
                player.addPotionEffect(new PotionEffect(PotionEffectType.ABSORPTION, 10*20, 3));
                player.addPotionEffect(new PotionEffect(PotionEffectType.REGENERATION, 50*20, 1));
                player.addPotionEffect(new PotionEffect(PotionEffectType.FIRE_RESISTANCE, 50*20, 0));
                player.playEffect(EntityEffect.TOTEM_RESURRECT);
                player.getWorld().playSound(player.getLocation(), Sound.ITEM_TOTEM_USE, 0.3F, 1);
            }
        }
    }
}
