package org.contrum.prisma.gameevents;

import lombok.SneakyThrows;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.gameevents.impl.*;

import java.util.Random;

public enum Events {
    ANGEL_GUARDIAN(AngelGuardianEvent.class),
    COMPRESS_DISCOUNT(CompressDiscountEvent.class),
    TREASURE_MULTIPLIER(TreasureMultiplierEvent.class),
    DROP_MULTIPLIER(DropMultiplierEvent.class),
    KEYS_EVENT(KeysEvent.class);

    private final Class<? extends Event> eventClass;
    private Events(Class<? extends Event> eventClass){
        this.eventClass = eventClass;
    }

    @SneakyThrows
    public Event get(PaperServices services){
        return eventClass.getDeclaredConstructor(PaperServices.class).newInstance(services);
    }

    public static Events getRandom(){
        return Events.values()[new Random().nextInt(Events.values().length)];
    }

    public static Event getRandomEvent(PaperServices services){
        return Events.getRandom().get(services);
    }
}
