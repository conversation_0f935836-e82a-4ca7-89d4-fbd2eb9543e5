package org.contrum.prisma.gameevents;

import lombok.Getter;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.PrismaCoreSetting;
import org.contrum.prisma.utils.tick.TickedCooldown;
import org.contrum.prisma.utils.time.TimeUtils;
import org.contrum.tritosa.Translator;

import javax.annotation.Nullable;
import java.time.Duration;

@Getter
public class GameEventsService {

    private final PaperServices services;
    private final Translator translator;

    private Event activeEvent;
    public TickedCooldown cooldown;

    public GameEventsService(PaperServices services){
        this.services = services;
        this.translator = services.getTranslator();

        if (PrismaCoreSetting.EVENTS_ENABLED)
            this.startTimer(Duration.ofSeconds(1));

        TaskUtil.runTimer(services.getPlugin(), ()->{
            if (!PrismaCoreSetting.EVENTS_ENABLED) return;

            if (this.getActiveEvent() != null){
                Event event = this.getActiveEvent();
                if (event.getRemainingTime() <= 0){
                    this.finishEvent();
                }
            }
        }, 0L, 20L);
    }

    public void reload() {
        if (this.cooldown == null && PrismaCoreSetting.EVENTS_ENABLED)
            this.startTimer(Duration.ofSeconds(1));
    }

    public void unload(){
        this.finishEvent();
    }

    public void startTimer(Duration plus) {
        if (cooldown != null && cooldown.isActive()) cooldown.cancel();
        cooldown = new TickedCooldown(services.getPlugin(), TimeUtils.parseDuration(PrismaCoreSetting.EVENTS_START_COOLDOWN).plus(plus))
                .onFinish(() -> {
                    this.startRandomEvent(false);
                });
    }

    public String getNextEventRemainingTime(){
        return TimeUtils.getFormattedTime(cooldown.getTimeLeftAsDuration().toMillis(), false);
    }

    @Nullable
    public Event getActiveEvent(){
        return (activeEvent != null ? (activeEvent.isActive() ? activeEvent : null) : null);
    }

    public void setActiveEvent(Events event, boolean force){
        this.setActiveEvent(event.get(services), force);
    }

    public void setActiveEvent(Event event, boolean force){
        if (this.getActiveEvent() != null){
            if (!force) return;
            this.activeEvent.stop();
        }

        activeEvent = event;
        activeEvent.start();

        translator.broadcast("EVENTS.EVENT_START", event);

        this.startTimer(event.getDuration());
    }

    public void finishEvent(){
        Event event = this.getActiveEvent();
        if (event == null) return;
        translator.broadcast("EVENTS.EVENT_STOP", event);
        event.stop();
    }

    public void startRandomEvent(boolean force){
        this.setActiveEvent(Events.getRandomEvent(services), force);
    }
}
