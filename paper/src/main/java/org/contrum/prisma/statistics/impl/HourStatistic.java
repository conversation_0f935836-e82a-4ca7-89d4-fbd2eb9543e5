package org.contrum.prisma.statistics.impl;

import lombok.Getter;
import org.bson.Document;
import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.contrum.prisma.statistics.Statistic;
import org.contrum.prisma.utils.GeyserUtil;
import org.contrum.prisma.utils.PlayerUtils;
import org.contrum.prisma.utils.serialize.DocumentSerialized;

import java.net.InetSocketAddress;
import java.util.*;
import java.util.stream.Collectors;

@Getter
public class HourStatistic extends Statistic {
    public final static String ID = "HOUR";

    private int connectionsCount;
    private int joinedPlayers;
    private int newPlayersCount;

    private  int onlinePlayers;
    private  int bedrockPlayers;
    private  int javaPlayers;
    private int alts;
    private int afkPlayers;

    private int playerDeaths;

    private final Map<String, Long> playtime = new HashMap<>(); //Players playtime (in seconds)
    private final Map<Long, MinuteData> minuteData = new HashMap<>();

    public HourStatistic(String server, int id) {
        super(server, id);

        this.saveCurrentOnlinePlayers();
    }

    public HourStatistic(Document document) {
        super(document);
        this.connectionsCount = document.getInteger("connectionsCount", 0);
        this.joinedPlayers = document.getInteger("joinedPlayers", 0);
        this.newPlayersCount = document.getInteger("newPlayersCount", 0);

        this.onlinePlayers = document.getInteger("onlinePlayers", 0);
        this.bedrockPlayers = document.getInteger("bedrockPlayers", 0);
        this.javaPlayers = document.getInteger("javaPlayers", 0);
        this.alts = document.getInteger("alts", 0);
        this.afkPlayers = document.getInteger("afkPlayers", 0);

        this.playerDeaths = document.getInteger("playerDeaths", 0);

        Document serializedPlaytime = document.get("playtime", Document.class);
        for (Map.Entry<String, Object> entry : serializedPlaytime.entrySet()) {
            String key = entry.getKey();
            long time = ((Number)entry.getValue()).longValue();

            playtime.put(key, time);
        }

        if (document.containsKey("minuteData")) {
            Document serializedMinuteData = document.get("minuteData", Document.class);
            for (Map.Entry<String, Object> entry : serializedMinuteData.entrySet()) {
                long time = Long.parseLong(entry.getKey());
                Document data = ((Document)entry.getValue());

                minuteData.put(time, new MinuteData(data));
            }
        }
    }

    @Override
    public String getType() {
        return ID;
    }

    public void saveCurrentOnlinePlayers() {
        long time = System.currentTimeMillis();

        Collection<? extends Player> players = Bukkit.getOnlinePlayers();

        this.onlinePlayers = players.size();
        this.bedrockPlayers = players.stream().filter(GeyserUtil::isGeyser).toList().size();
        this.javaPlayers = onlinePlayers - bedrockPlayers;
        this.alts = 0;
        this.afkPlayers = 0;

        Set<String> ips = new HashSet<>();
        for (Player player : players) {
            InetSocketAddress address = player.getAddress();
            if (address != null) {
                String ip = address.getAddress().getHostAddress();
                if (!ips.add(ip)) alts+=1;
            }

            if (player.isAfk()) afkPlayers++;
        }
    }

    public void saveMinuteStatistic() {
        long time = System.currentTimeMillis();
        minuteData.put(time, new MinuteData());
    }

    @Override
    public boolean addPlaytimeSecond(OfflinePlayer target) {
        boolean added = playtime.containsKey(target.getName());
        playtime.put(target.getName(), playtime.getOrDefault(target.getName(), 0L) + 1L);

        return !added;
    }

    @Override
    public void playerJoin(Player player) {
        connectionsCount++;
        if (addPlaytimeSecond(player))
            joinedPlayers++;
        if (!player.hasPlayedBefore())
            newPlayersCount++;
    }

    @Override
    public void onDeath(PlayerDeathEvent event) {
        if (PlayerUtils.isInDuel(event.getPlayer()) || PlayerUtils.isNPC(event.getPlayer())) return;

        playerDeaths+=1;
    }

    @Override
    public Document serialize() {
        return super.serialize()
                .append("connectionsCount", connectionsCount)
                .append("joinedPlayers", joinedPlayers)
                .append("newPlayersCount", newPlayersCount)

                .append("onlinePlayers", onlinePlayers)
                .append("bedrockPlayers", bedrockPlayers)
                .append("javaPlayers", javaPlayers)
                .append("alts", alts)
                .append("afkPlayers", afkPlayers)

                .append("playerDeaths", playerDeaths)

                .append("playtime", new Document(playtime))
                .append("minuteData", new Document(minuteData.entrySet().stream().collect(Collectors.toMap(entry -> entry.getKey().toString(), entry -> entry.getValue().serialize()))));
    }

    @Getter
    public static class MinuteData implements DocumentSerialized {

        private final int onlinePlayers;
        private final int bedrockPlayers;
        private final int javaPlayers;
        private int alts;
        private int afkPlayers;

        public MinuteData() {
            Collection<? extends Player> players = Bukkit.getOnlinePlayers();

            this.onlinePlayers = players.size();
            this.bedrockPlayers = players.stream().filter(GeyserUtil::isGeyser).toList().size();
            this.javaPlayers = onlinePlayers - bedrockPlayers;
            this.alts = 0;
            this.afkPlayers = 0;

            Set<String> ips = new HashSet<>();
            for (Player player : players) {
                InetSocketAddress address = player.getAddress();
                if (address != null) {
                    String ip = address.getAddress().getHostAddress();
                    if (!ips.add(ip)) alts+=1;
                }

                if (player.isAfk()) afkPlayers++;
            }
        }

        public MinuteData(Document document) {
            this.onlinePlayers = document.getInteger("onlinePlayers", 0);
            this.bedrockPlayers = document.getInteger("bedrockPlayers", 0);
            this.javaPlayers = document.getInteger("javaPlayers", 0);
            this.alts = document.getInteger("alts", 0);
            this.afkPlayers = document.getInteger("afkPlayers", 0);
        }

        @Override
        public Document serialize() {
            return new Document()
                    .append("onlinePlayers", onlinePlayers)
                    .append("bedrockPlayers", bedrockPlayers)
                    .append("javaPlayers", javaPlayers)
                    .append("alts", alts)
                    .append("afkPlayers", afkPlayers);
        }
    }
}