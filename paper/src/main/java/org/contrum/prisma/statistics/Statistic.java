package org.contrum.prisma.statistics;

import lombok.Getter;
import lombok.Setter;
import org.bson.Document;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.contrum.prisma.utils.serialize.DocumentSerialized;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;

@Getter
public abstract class Statistic implements DocumentSerialized {
    @Setter
    private int id;
    private final String server;

    protected Statistic(String server, int id) {
        this.id = id;
        this.server = server;
    }

    protected Statistic(Document document) {
        this.id = document.getInteger("id");
        this.server = document.getString("server");
    }

    public abstract String getType();

    public String formatId() {
        String idString = String.format("%010d", id);

        int year = Integer.parseInt(idString.substring(0, 4));
        int day = Integer.parseInt(idString.substring(4, 6));
        int month = Integer.parseInt(idString.substring(6, 8));
        int hour = Integer.parseInt(idString.substring(8, 10));

        LocalDateTime dateTime = LocalDateTime.of(year, month, day, hour, 0);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMMM dd, yyyy, hh:mma", Locale.ENGLISH);
        return dateTime.format(formatter);
    }

    public abstract boolean addPlaytimeSecond(OfflinePlayer target);

    public abstract void playerJoin(Player player);

    public abstract void onDeath(PlayerDeathEvent event);

    @Override
    public Document serialize() {
        return new Document()
                .append("type", this.getType())
                .append("id", id)
                .append("server", server)
                .append("time", this.formatId());
    }
}
