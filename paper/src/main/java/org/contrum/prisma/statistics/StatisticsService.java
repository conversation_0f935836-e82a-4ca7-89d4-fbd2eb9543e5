package org.contrum.prisma.statistics;

import com.mongodb.client.model.Filters;
import com.mongodb.client.model.ReplaceOptions;
import lombok.Getter;
import org.bson.Document;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.scheduler.BukkitRunnable;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.PrismaCoreSetting;
import org.contrum.prisma.mongo.MongoBackend;
import org.contrum.prisma.statistics.impl.HourStatistic;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

@Getter
public class StatisticsService implements Listener {
    private static final String COLLECTION_NAME = "statistics";

    private final PaperServices services;
    private final MongoBackend mongoBackend;

    private int hourID = -1;
    private HourStatistic currentHourStatistic;

    public StatisticsService(PaperServices services) {
        this.services = services;
        this.mongoBackend = services.getMongoBackend();

        this.ensureStatisticLoaded();
        Bukkit.getPluginManager().registerEvents(this, services.getPlugin());

        TaskUtil.runTimer(services.getPlugin(), new BukkitRunnable() {

            private int runs = 0;

            @Override
            public void run() {
                if (!isEnabled()) return;

                runs+=1;

                if (currentHourStatistic == null) {
                    ensureStatisticLoaded();
                    return;
                }

                //update players playtime
                for (Player player : Bukkit.getOnlinePlayers()) {
                    currentHourStatistic.addPlaytimeSecond(player);
                }

                //Check for updates
                checkForStatisticUpdate();

                if (runs % 60 == 0) { //Save minute data
                    currentHourStatistic.saveMinuteStatistic();
                }

                if (runs % 300 == 0) { //Save hourly statistic
                    save(currentHourStatistic);
                }
            }
        }, 20L, 20L);
    }

    public void shutdown() {
        //Save sync
        if (currentHourStatistic == null) return;
        mongoBackend.querySyncForCollection(COLLECTION_NAME, collection -> {
            collection.replaceOne(Filters.and(
                    Filters.eq("id", hourID),
                    Filters.eq("type", HourStatistic.ID),
                    Filters.eq("server", services.getServersService().getCurrentServer().getName())
            ), currentHourStatistic.serialize(), new ReplaceOptions().upsert(true));
            return null;
        });
    }

    public boolean isEnabled() {
        return PrismaCoreSetting.STATISTICS_ENABLED;
    }

    public void checkForStatisticUpdate() {
        this.updateHourId();

        if (currentHourStatistic.getId() != hourID) {
            this.save(currentHourStatistic);
            currentHourStatistic = new HourStatistic(services.getServersService().getCurrentServer().getName(), hourID);
        }
    }

    public void save(Statistic save) {
        mongoBackend.queryForCollection(COLLECTION_NAME, collection -> {
            collection.replaceOne(Filters.and(
                    Filters.eq("id", save.getId()),
                    Filters.eq("type", save.getType()),
                    Filters.eq("server", save.getServer())
            ), save.serialize(), new ReplaceOptions().upsert(true));
            return collection;
        });
    }

    public HourStatistic loadHourStatistic(int hourID) {
        return mongoBackend.queryForCollection(COLLECTION_NAME, collection -> {
            Document first = collection.find(Filters.and(
                    Filters.eq("id", hourID),
                    Filters.eq("type", HourStatistic.ID),
                    Filters.eq("server", services.getServersService().getCurrentServer().getName())
            )).first();
            if (first == null) return null;
            return new HourStatistic(first);
        }).join();
    }

    private void ensureStatisticLoaded() {
        if (hourID < 0) this.updateHourId();

        if (currentHourStatistic == null) {
            this.currentHourStatistic = this.loadHourStatistic(hourID);

            if (currentHourStatistic == null)
                currentHourStatistic = new HourStatistic(services.getServersService().getCurrentServer().getName(), hourID);
        }
    }

    public int getUpdatedHourId() {
        ZoneId estZone = ZoneId.of("America/New_York");
        return Integer.parseInt(ZonedDateTime.now(estZone).format(DateTimeFormatter.ofPattern("uuuuddMMHH")));
    }

    public void updateHourId() {
        this.hourID = this.getUpdatedHourId();
    }

    @EventHandler
    public void onJoin(PlayerJoinEvent event) {
        if (!this.isEnabled()) return;

        if (this.currentHourStatistic == null) {
            this.ensureStatisticLoaded();
            return;
        }

        currentHourStatistic.playerJoin(event.getPlayer());
    }

    @EventHandler(ignoreCancelled = true, priority = EventPriority.MONITOR)
    public void onDeath(PlayerDeathEvent event) {
        if (!this.isEnabled()) return;

        if (this.currentHourStatistic == null) {
            this.ensureStatisticLoaded();
            return;
        }

        currentHourStatistic.onDeath(event);
    }
}