package org.contrum.prisma.commandblocker;

import org.contrum.prisma.PaperServices;

import java.util.List;

public class CommandBlockerService extends AbstractCommandBlockerService {
    private final PaperServices services;

    public CommandBlockerService(PaperServices services) {
        this.services = services;
    }

    @Override
    public List<String> getAllowedCommands() {
        return List.of();
    }
}