package org.contrum.prisma.metadatapurger;

import net.kyori.adventure.text.Component;
import org.bson.Document;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.AsyncPlayerPreLoginEvent;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.jetbrains.annotations.Nullable;

import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

public class MetadataPurgerService implements Listener {
    private final PaperServices services;

    private boolean purging = false;

    public MetadataPurgerService(PaperServices services) {
        this.services = services;
    }

    public void startServerMetadataPurge(@Nullable String metadataName) {
        if (purging) return;
        this.purging = true;

        final String name = metadataName == null ? null : metadataName.replace(".", "_");

        // Kick players
        for (Player player : Bukkit.getOnlinePlayers()) {
            player.kick(Component.text("Server metadata purge started!"));
        }

        System.out.println("Starting metadata purge...");
        services.getProfileService().getMongoBackend().queryForProfile(collection -> {
            AtomicInteger counter = new AtomicInteger();
            AtomicInteger removedCounter = new AtomicInteger();
            long size = collection.countDocuments();

            System.out.println("Found " + size + " profiles");

            collection.find().forEach(document -> {

                if (services.getServersService().getCurrentServer() != null && document != null && document.containsKey("serverMetadata")) {
                    String serverName = services.getServersService().getCurrentServer().getName();
                    Document serverMetadata = document.get("serverMetadata", Document.class);

                    Document currentServerMetadata = (Document) serverMetadata.getOrDefault(serverName, new Document());
                    if (!currentServerMetadata.isEmpty()) {
                        if (name != null) {
                            // Remove specific metadata key
                            Object removed = currentServerMetadata.remove(name);
                            if (removed != null) {
                                removedCounter.incrementAndGet();
                                serverMetadata.put(serverName, currentServerMetadata);
                                collection.updateOne(
                                        new Document("_id", document.get("_id")),
                                        new Document("$set", new Document("serverMetadata", serverMetadata))
                                );
                            }
                        } else { // Remove all server metadata for this server
                            removedCounter.incrementAndGet();
                            serverMetadata.remove(serverName);
                            collection.updateOne(
                                    new Document("_id", document.get("_id")),
                                    new Document("$set", new Document("serverMetadata", serverMetadata))
                            );
                        }
                    }
                }

                int processed = counter.incrementAndGet();
                if (processed % 1000 == 0) {
                    System.out.println("Profiles updated: " + processed + "/" + size + " - ( " + removedCounter.get() + " updated )");
                }
            });

            System.out.println("Finished, rebooting");
            this.purging = false;
            TaskUtil.run(services.getPlugin(), Bukkit::shutdown);
            return null;
        });
    }

    public void startGlobalMetadataPurge(String metadataName) {
        if (purging) return;
        this.purging = true;

        final String key = metadataName == null ? null : metadataName.replace(".", "_");

        for (Player player : Bukkit.getOnlinePlayers()) {
            player.kick(Component.text("Global metadata purge started!"));
        }

        System.out.println("Starting global purge...");
        services.getProfileService().getMongoBackend().queryForProfile(collection -> {
            AtomicInteger counter = new AtomicInteger();
            AtomicInteger removedCounter = new AtomicInteger();

            Document filter = new Document("metadata." + key, new Document("$exists", true));
            long size = collection.countDocuments(filter);

            System.out.println("Found " + size + " profiles with the metadata: " + key);
            collection.find(filter).forEach(document -> {
                if (document != null
                        && document.containsKey("metadata")) {

                    Document metadatas = document.get("metadata", Document.class);

                    if (metadatas.containsKey(key)) {
                        metadatas.remove(key);
                        collection.replaceOne(new Document("_id", document.get("_id")), document);
                        removedCounter.incrementAndGet();
                    }
                }

                int processed = counter.incrementAndGet();
                if (processed % 1000 == 0) {
                    System.out.println("Profiles updated: " + processed + "/" + size
                            + " - ( " + removedCounter.get() + " updated )");
                }
            });

            System.out.println("Finished, rebooting");
            this.purging = false;
            TaskUtil.run(services.getPlugin(), Bukkit::shutdown);
            return null;
        });
    }


    @EventHandler(ignoreCancelled = true, priority = EventPriority.LOW)
    public void onJoin(AsyncPlayerPreLoginEvent event) {
        if (purging) {
            event.disallow(AsyncPlayerPreLoginEvent.Result.KICK_OTHER, Component.text("Server is not available"));
        }
    }
}