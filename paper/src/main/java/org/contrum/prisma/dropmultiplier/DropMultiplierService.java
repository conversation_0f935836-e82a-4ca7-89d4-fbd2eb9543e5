package org.contrum.prisma.dropmultiplier;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.utils.time.TimeUtils;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public class DropMultiplierService implements Listener {
    private final PaperServices services;

    private final Map<UUID, MultiplierOrb> orbs = new HashMap<>();

    public DropMultiplierService(PaperServices services) {
        this.services = services;
        Bukkit.getPluginManager().registerEvents(this, services.getPlugin());
    }

    public void setDropMultiplier(Player player, Duration duration, int multiplier) {
        Profile profile = services.getProfileService().getProfile(player.getUniqueId());
        ProfilePaperMetadata metadata = profile.getServerMetadata(services.getProfileService(), ProfilePaperMetadata.class);

        metadata.getActiveTimers().add(new DropMultiplierTimer(services, duration, multiplier, player.getUniqueId()));
    }

    public MultiplierOrb getOrCreateOrb(UUID uuid) {
        Player player = Bukkit.getPlayer(uuid);
        return player == null ? null : getOrCreateOrb(player);
    }

    public MultiplierOrb getOrb(UUID uuid) {
        return orbs.get(uuid);
    }

    public MultiplierOrb getOrCreateOrb(Player player) {
        return orbs.computeIfAbsent(player.getUniqueId(), u -> new MultiplierOrb(services, player));
    }

    public boolean isShowOrb(UUID uuid) {
        ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(uuid, ProfilePaperMetadata.class);
        if (metadata == null) return false;

        return metadata.isShowMultiplierOrb();
    }

    public void setShowOrb(UUID uuid, boolean show) {
        ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(uuid, ProfilePaperMetadata.class);
        if (metadata == null) return;

        metadata.setShowMultiplierOrb(show);
    }

    public void removeOrb(UUID uuid) {
        MultiplierOrb orb = this.getOrb(uuid);
        if (orb != null) {
            orb.destroy();
            this.orbs.remove(uuid);
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
        if (metadata == null) return;

        DropMultiplierTimer timer = metadata.getTimer(DropMultiplierTimer.class);
        if (timer != null) {
            timer.init(this);
        }
    }

    @EventHandler
    public void onQuit(PlayerQuitEvent event) {
        this.removeOrb(event.getPlayer().getUniqueId());
    }
}