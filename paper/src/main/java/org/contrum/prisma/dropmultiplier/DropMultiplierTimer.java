package org.contrum.prisma.dropmultiplier;

import lombok.Getter;
import org.bson.Document;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.utils.timer.impl.PlayerTimer;

import java.time.Duration;
import java.util.UUID;

@Getter
public class DropMultiplierTimer extends PlayerTimer {

    private DropMultiplierService service;
    private int multiplier = 2;

    public DropMultiplierTimer(PaperServices services, Document document) {
        super(services, document);

        this.multiplier = document.getInteger("multiplier", 2);
    }

    public DropMultiplierTimer(PaperServices services, Duration duration, int multiplier, UUID uuid) {
        super(services, duration, uuid);
        this.init(services.getDropMultiplierService());

        this.multiplier = multiplier;
    }

    public void init(DropMultiplierService service) {
        this.service = service;
        MultiplierOrb orb = service.getOrCreateOrb(super.getUuid());
        if (orb.isDestroyed()) {
            orb.setDestroyed(false);
        }
    }

    public String getOrbDisplayText() {
        return switch (multiplier) {
            case 3 -> "&8* &c&n&lx3&4 &lMULTIPLICADOR &8*";
            default -> "&8* &b&n&lx2&d &lMULTIPLICADOR &8*";
        };
    }

    public String getOrbDisplayName() {
        return switch (multiplier) {
            case 3 -> "&c&lmultiplicador &4&l&nx3";
            default -> "&a&lmultiplicador &a&l&nx2";
        };
    }

    public String getSkin() {
        return switch (multiplier) {
            case 3 -> "http://textures.minecraft.net/texture/4d11109f4ab03aa6c5b76cad129176ffb1fce8c174e69c9e8ba06b9f8061e5ad";
            default -> "https://textures.minecraft.net/texture/d67dce4645349e41a7f35797e2b9279e35a65f5e81a34496885d27268f369139";
        };
    }

    @Override
    public String getName() {
        return "DropMultiplier";
    }

    @Override
    public void onExpire(UUID uuid) {
        service.removeOrb(uuid);
    }

    private int tick = 0;

    @Override
    protected void onTick(UUID uuid) {
        tick++;
        if (service == null) return;

        if (!service.isShowOrb(uuid)) {
            service.removeOrb(uuid);
            return;
        }

        MultiplierOrb orb = service.getOrb(uuid);
        if (orb == null) {
            //Recreate the orb every few ticks to avoid creating multiple orbs.
            if (tick % 100 == 20) {
                orb = service.getOrCreateOrb(super.getUuid());
                if (orb.isDestroyed()) {
                    orb.setDestroyed(false);
                }
            }
            return;
        }

        orb.tick();
    }

    @Override
    public void onRemove(UUID uuid) {
        if (service == null) return;
        MultiplierOrb orb = service.getOrb(uuid);
        if (orb != null) {
            orb.destroy();
            service.removeOrb(uuid);
        }
    }

    @Override
    public boolean tickOnlyWhileOnline() {
        return true;
    }

    @Override
    public String getDisplayName() {
        return "";
    }

    @Override
    public Document serialize() {
        return super.serialize()
                .append("multiplier", multiplier);
    }
}
