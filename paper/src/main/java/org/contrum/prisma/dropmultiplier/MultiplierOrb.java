package org.contrum.prisma.dropmultiplier;

import lombok.Getter;
import lombok.Setter;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.*;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.util.Vector;
import org.contrum.chorpu.TaskUtil;
import org.contrum.chorpu.inventory.ItemBuilder;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.utils.CCP;
import org.contrum.prisma.utils.ItemBuilder1_20;

@Getter
public class MultiplierOrb {
    private final static double FOLLOW_DISTANCE_VERYLOW = 4.0;
    private final static double FOLLOW_DISTANCE_LOW = 10.0;
    private final static double FOLLOW_DISTANCE_MEDIUM = 20.0;
    private final static double FOLLOW_DISTANCE_HIGH = 40.0;
    private final static double FLOATING_DISTANCE = 1.0;
    private final static double FLOATING_AMPLITUDE = 0.5;
    private final static double FLOATING_FREQUENCY = 2.0;

    private final PaperServices services;
    private final Player owner;
    private Entity entity;

    @Setter
    private boolean destroyed = false;

    private boolean spawning = false;

    public MultiplierOrb(PaperServices services, Player owner) {
        this.services = services;
        this.owner = owner;
    }

    private Entity spawn() {
        ArmorStand entity = (ArmorStand) owner.getWorld().spawnEntity(owner.getLocation(), EntityType.ARMOR_STAND);

        entity.setGravity(false);
        entity.setSilent(true);
        entity.setInvulnerable(true);
        entity.setArms(false);
        entity.setSmall(true);
        entity.setMarker(true);
        entity.setVisible(false);
        entity.setBasePlate(false);
        entity.setPersistent(false);
        entity.getEquipment().setHelmet(new ItemBuilder1_20(Material.PLAYER_HEAD).setOwnerUrl("https://textures.minecraft.net/texture/d67dce4645349e41a7f35797e2b9279e35a65f5e81a34496885d27268f369139").build());
        entity.setMetadata("dropMultiplierOrb", new FixedMetadataValue(services.getPlugin(), true));

        entity.setCustomName(CCP.translateAndFront("&a&lmultiplicador &a&l&nx2"));
        entity.setCustomNameVisible(true);

        ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(owner.getUniqueId(), ProfilePaperMetadata.class);
        if (metadata != null) {
            DropMultiplierTimer timer = metadata.getTimer(DropMultiplierTimer.class);
            if (timer != null) {
                entity.setCustomName(CCP.translateAndFront(timer.getOrbDisplayName()));
                entity.getEquipment().setHelmet(new ItemBuilder1_20(Material.PLAYER_HEAD).setOwnerUrl(timer.getSkin()).build());
            }
        }

        Location relativeLocation = owner.getLocation().clone();
        double yOffset = owner.getEyeHeight(true) + 0.5 * Math.sin(2 * System.currentTimeMillis() / 1000.0);
        relativeLocation.add(2, yOffset, 2);
        entity.teleport(relativeLocation);

        return entity;
    }

    public void tick() {
        if (destroyed || spawning) return;

        //Respawn entity
        if (entity == null || entity.isDead()){
            //Mark as destroyed to avoid multiple entity creation
            spawning = true;
            TaskUtil.run(services.getPlugin(), () -> {
                entity = this.spawn();
                spawning = false;
            });
            return;
        }


        //Check if are in different worlds
        if (!owner.getWorld().equals(entity.getWorld())){
            entity.teleportAsync(owner.getLocation());
            return;
        }

        //Move to player
        double distance = owner.getLocation().distance(entity.getLocation());
        Location relativeLocation = owner.getLocation().clone().subtract(0,0.5,0);
        double yOffset = owner.getEyeHeight(true) + FLOATING_AMPLITUDE * Math.sin(FLOATING_FREQUENCY * System.currentTimeMillis() / 1000.0);
        relativeLocation.add(1.3, yOffset, 1.3);
        //  Standing still
        if (relativeLocation.distance(entity.getLocation()) <= FLOATING_DISTANCE) {

            Vector lookDirection = owner.getEyeLocation().toVector().subtract(entity.getLocation().toVector()).normalize();
            double yaw = Math.toDegrees(Math.atan2(lookDirection.getX(), lookDirection.getZ()));
            double pitch = 0;
            Location newLoc = entity.getLocation().clone();
            newLoc.setY(relativeLocation.getY());
            newLoc.setYaw((float) -yaw);
            newLoc.setPitch((float) -pitch);

            entity.teleportAsync(newLoc);
            return;
        }

        //Move to player
        Vector direction = relativeLocation.toVector().subtract(entity.getLocation().toVector()).normalize();
        double speed = (distance > FOLLOW_DISTANCE_VERYLOW) ? 0.2 : 0.08;
        if (distance > FOLLOW_DISTANCE_LOW){
            speed = 0.325;
        }
        if (distance > FOLLOW_DISTANCE_MEDIUM){
            speed = 0.75;
        }
        if (distance > FOLLOW_DISTANCE_HIGH){
            speed = 1.75;
        }
        Vector movement = direction.multiply(speed);
        Location newLocation = entity.getLocation().add(movement);

        Vector lookDirection = owner.getEyeLocation().toVector().subtract(entity.getLocation().toVector()).normalize();
        double yaw = Math.toDegrees(Math.atan2(lookDirection.getX(), lookDirection.getZ()));
        double pitch = Math.toDegrees(Math.asin(lookDirection.getY()));
        newLocation.setYaw((float) -yaw);
        newLocation.setPitch((float) -pitch);

        entity.teleportAsync(newLocation);
    }

    public void destroy() {
        this.destroyed = true;
        if (entity != null) {
            TaskUtil.run(services.getPlugin(), () -> {
                entity.remove();
                entity = null;
            });
        }
    }
}
