package org.contrum.prisma.customitems.ability.items;

import com.destroystokyo.paper.event.player.PlayerElytraBoostEvent;
import com.destroystokyo.paper.event.player.PlayerLaunchProjectileEvent;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Firework;
import org.bukkit.entity.Player;
import org.bukkit.entity.Projectile;
import org.bukkit.event.EventHandler;
import org.bukkit.event.entity.EntityToggleGlideEvent;
import org.bukkit.event.entity.ProjectileHitEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.projectiles.ProjectileSource;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.clients.PlayerClient;
import org.contrum.prisma.customitems.ability.CooldownAbility;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.utils.Cooldown;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.prisma.utils.UClansUtils;
import org.contrum.prisma.utils.time.TimeUtils;

import java.time.Duration;
import java.util.UUID;

public class ElytraDisablerAbility extends CooldownAbility {
    private String duration = this.getConfigSection().getString("DURATION", "8s");

    public ElytraDisablerAbility(PaperServices services) {
        super(services);
    }

    @Override
    public String displayName(Player player) {
        return "ElytraDisabler";
    }

    @Override
    public void onReload() {
        this.duration = this.getConfigSection().getString("DURATION", "8s");
    }

    @Override
    public String getID() {
        return "ELYTRA_DISABLER";
    }

    public Duration getRemainingTime(UUID uuid) {
        ProfilePaperMetadata metadata = getServices().getProfileService().getServerMetadata(uuid, ProfilePaperMetadata.class);
        Cooldown cooldown = metadata.getCooldown("ElytraCooldown");
        if (cooldown == null || cooldown.hasExpired()) return null;
        return cooldown.getTimeLeftAsDuration();
    }

    public boolean isDisabled(UUID uuid) {
        return getServices().getProfileService().getServerMetadata(uuid, ProfilePaperMetadata.class).hasCooldown("ElytraCooldown");
    }

    @Override
    public void onThrow(Player thrower, ItemStack item, Projectile projectile, PlayerLaunchProjectileEvent event) {
        if (!canUseMessage(thrower)){
            event.setCancelled(true);
            return;
        }

        //Check for cooldown
        if (this.hasCooldown(thrower)){
            this.sendCooldownMessage(thrower);
            event.setCancelled(true);
            return;
        }

        if (!this.tryUse(thrower, item)) event.setCancelled(true);
    }

    @Override
    public void onProjectileImpact(ProjectileSource sender, Projectile projectile, ProjectileHitEvent event) {
        if (!(sender instanceof Player thrower)) return;

        Location location = projectile.getLocation();
        if (event.getHitEntity() != null) location = event.getHitEntity().getLocation();

        location.getWorld().spawnParticle(Particle.LARGE_SMOKE, location,  30, 0.25, 2, 1, 2);
        location.getWorld().playSound(location, Sound.ENTITY_ZOMBIE_VILLAGER_CURE, 0.6F, 2);
        for (Player player : location.getNearbyPlayers(8)){

            //Check if player is in the same clan of thrower
            if (UClansUtils.areSameClan(getServices(), player, thrower) || thrower.getUniqueId().equals(player.getUniqueId())) continue;

            Duration time = TimeUtils.parseDuration(duration);

            player.sendMessage(this.getLangMessage(player, "ELYTRAS_DISABLED_BY", thrower, time));
            player.setGliding(false);

            ProfilePaperMetadata metadata = getServices().getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
            metadata.setCooldown("ElytraCooldown", time);
            //Destroy all current rockets owned by player
            for (Firework firework : player.getWorld().getEntitiesByClass(Firework.class)) {
                if (firework.getAttachedTo() != null && firework.getAttachedTo().getUniqueId().equals(player.getUniqueId()))
                    firework.remove();
            }

            //Send rocket item cooldown
            long ticks = 20L * time.toSeconds();
            player.setCooldown(Material.FIREWORK_ROCKET, (int) ticks);

            PlayerClient client = this.getServices().getClientsService().getClient(player);
            client.applyItemCooldown(player, "Elytras", Material.ELYTRA, time);

            //Set gliding to true on effect finish to avoid client-side glitches
            TaskUtil.runLater(getPlugin(), ()->{
                player.setGliding(true);
            }, 20L * time.toSeconds());
        }
    }

    @Override
    public boolean allowSafeZoneUse() {
        return false;
    }

    @EventHandler
    public void elytraToggle(EntityToggleGlideEvent event) {
        if (!(event.getEntity() instanceof Player player)) return;
        Duration cooldown = this.getRemainingTime(player.getUniqueId());
        if (cooldown != null && event.isGliding()) {
            event.setCancelled(true);
            player.sendMessage(this.getLangMessage(player, "ELYTRAS_DISABLED", player, cooldown));
            WorldUtils.playSound(Sound.ENTITY_IRON_GOLEM_REPAIR, player, 1, 1.25F);
        }
    }

    @EventHandler
    public void onBoost(PlayerElytraBoostEvent event) {
        Player player = event.getPlayer();
        Duration cooldown = this.getRemainingTime(player.getUniqueId());
        if (cooldown != null) {
            event.setCancelled(true);
            player.sendMessage(this.getLangMessage(player, "ELYTRAS_DISABLED", player, cooldown));
            WorldUtils.playSound(Sound.ENTITY_IRON_GOLEM_REPAIR, player, 1, 1.25F);
        }
    }
}