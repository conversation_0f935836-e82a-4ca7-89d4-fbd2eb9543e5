package org.contrum.prisma.customitems.ability.items;


import com.github.retrooper.packetevents.PacketEvents;
import com.github.retrooper.packetevents.PacketEventsAPI;
import com.github.retrooper.packetevents.event.PacketListener;
import com.github.retrooper.packetevents.event.PacketListenerPriority;
import com.github.retrooper.packetevents.event.PacketSendEvent;
import com.github.retrooper.packetevents.manager.player.PlayerManager;
import com.github.retrooper.packetevents.protocol.packettype.PacketType;
import com.github.retrooper.packetevents.protocol.player.Equipment;
import com.github.retrooper.packetevents.protocol.player.EquipmentSlot;
import com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerEntityEquipment;
import io.github.retrooper.packetevents.util.SpigotConversionUtil;
import io.github.retrooper.packetevents.util.SpigotReflectionUtil;
import io.papermc.paper.event.player.PlayerTrackEntityEvent;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.Sound;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customitems.ability.CooldownAbility;

import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public class InvisibilityAbility extends CooldownAbility implements Listener {
    private Map<UUID, InvisibilityProfile> activeUsers = new ConcurrentHashMap<>();
    private int duration = this.getConfigSection().getInt("DURATION", 30);

    public InvisibilityAbility(PaperServices services) {
        super(services);
        playerEquipArmorPacket();
    }

    @Override
    public String displayName(Player player) {
        return "Invisibilidad";
    }

    @Override
    public void onReload() {
        this.duration = this.getConfigSection().getInt("DURATION", 30);
    }

    @Override
    public String getID() {
        return "INVISIBILITY";
    }

    @Override
    public void onClick(Player player, ItemStack item, boolean isRight, PlayerInteractEvent event) {
        if (!isRight) return;
        if (this.hasCooldown(player)){
            this.sendCooldownMessage(player);
            event.setCancelled(true);
            return;
        }
        if (!this.tryUse(player, item)) return;

        //Use item
        item.setAmount(item.getAmount()-1);
        player.sendMessage(this.getLangMessage(player, "USE"));
        player.playSound(player.getLocation(), Sound.ENTITY_ARROW_HIT_PLAYER, 1, 0.8F);
        applyEffect(player);
    }

    private void applyEffect(Player player){
        int id = new Random().nextInt();
        activeUsers.put(player.getUniqueId(), new InvisibilityProfile(false, id));
        player.addPotionEffect(new PotionEffect(PotionEffectType.INVISIBILITY, 20 * duration, 1, false, false));
        player.getTrackedPlayers().forEach(p -> tryUpdate(player, p));

        TaskUtil.runLater(getPlugin(), ()->{
            InvisibilityProfile p = activeUsers.get(player.getUniqueId());
            if (p != null && p.getId() == id){
                disableEffect(player);
            }
        }, 20L * duration);
    }

    public boolean isInvis(Player player){
        return this.activeUsers.containsKey(player.getUniqueId());
    }

    public void tryUpdate(Player player, Player to){
        if (activeUsers.containsKey(player.getUniqueId())){
            WrapperPlayServerEntityEquipment unequipPacket = new WrapperPlayServerEntityEquipment(player.getEntityId(), List.of(
                    new Equipment(EquipmentSlot.OFF_HAND, com.github.retrooper.packetevents.protocol.item.ItemStack.EMPTY),
                    new Equipment(EquipmentSlot.BOOTS, com.github.retrooper.packetevents.protocol.item.ItemStack.EMPTY),
                    new Equipment(EquipmentSlot.LEGGINGS, com.github.retrooper.packetevents.protocol.item.ItemStack.EMPTY),
                    new Equipment(EquipmentSlot.CHEST_PLATE, com.github.retrooper.packetevents.protocol.item.ItemStack.EMPTY),
                    new Equipment(EquipmentSlot.HELMET, com.github.retrooper.packetevents.protocol.item.ItemStack.EMPTY)
            ));

            PacketEvents.getAPI().getPlayerManager().sendPacketSilently(to, unequipPacket);

            //Set player ID as updated
            InvisibilityProfile p = activeUsers.get(player.getUniqueId());
            if (!p.isUpdated) {
                p.setUpdated(true);
                activeUsers.put(player.getUniqueId(), p);
            }
        }
    }

    public void disableEffect(Player player){
        if (activeUsers.containsKey(player.getUniqueId())){
            activeUsers.remove(player.getUniqueId());
            //Send message
            player.sendMessage(this.getLangMessage(player, "EXPIRED"));
            player.playSound(player.getLocation(), Sound.ENTITY_CAT_HURT, 1, 0.8F);

            player.removePotionEffect(PotionEffectType.INVISIBILITY);

            //Update player armor
            WrapperPlayServerEntityEquipment updatePacket = new WrapperPlayServerEntityEquipment(player.getEntityId(), List.of(
                    new Equipment(EquipmentSlot.MAIN_HAND, SpigotConversionUtil.fromBukkitItemStack(player.getInventory().getItemInMainHand())),
                    new Equipment(EquipmentSlot.OFF_HAND, SpigotConversionUtil.fromBukkitItemStack(player.getInventory().getItemInOffHand())),
                    new Equipment(EquipmentSlot.BOOTS, SpigotConversionUtil.fromBukkitItemStack(player.getInventory().getBoots())),
                    new Equipment(EquipmentSlot.LEGGINGS, SpigotConversionUtil.fromBukkitItemStack(player.getInventory().getLeggings())),
                    new Equipment(EquipmentSlot.CHEST_PLATE, SpigotConversionUtil.fromBukkitItemStack(player.getInventory().getChestplate())),
                    new Equipment(EquipmentSlot.HELMET, SpigotConversionUtil.fromBukkitItemStack(player.getInventory().getHelmet()))
            ));
            PlayerManager playerManager = PacketEvents.getAPI().getPlayerManager();

            for (Player receiver : player.getTrackedPlayers()) {
                playerManager.sendPacketSilently(receiver, updatePacket);
            }
        }
    }

    @EventHandler
    public void playerLoad(PlayerTrackEntityEvent event){
        if (event.getEntity() instanceof Player loaded){
            TaskUtil.runAsync(getPlugin(), ()-> tryUpdate(loaded, event.getPlayer()));
        }
    }

    @EventHandler(ignoreCancelled = true)
    public void playerDamagedByPlayer(EntityDamageByEntityEvent event){
        if (event.getDamager() instanceof Player && event.getEntity() instanceof Player player){
            disableEffect(player);
        }
    }

    public void playerEquipArmorPacket(){
        PacketEventsAPI<?> api = PacketEvents.getAPI();
        api.getEventManager().registerListener(new PacketListener() {
            @Override
            public void onPacketSend(PacketSendEvent event) {
                if (event.getPacketType() != PacketType.Play.Server.ENTITY_EQUIPMENT) return;

                Player player = event.getPlayer();
                WrapperPlayServerEntityEquipment packet = new WrapperPlayServerEntityEquipment(event);

                Entity entity = SpigotReflectionUtil.getEntityById(player.getWorld(), packet.getEntityId());
                if (!(entity instanceof Player target) || !isInvis(target)) return;

                com.github.retrooper.packetevents.protocol.item.ItemStack empty = com.github.retrooper.packetevents.protocol.item.ItemStack.EMPTY;
                List<Equipment> equipment = packet.getEquipment();
                for (Equipment e : equipment) {
                    if (e.getSlot().equals(EquipmentSlot.MAIN_HAND)) continue;
                    e.setItem(com.github.retrooper.packetevents.protocol.item.ItemStack.EMPTY);
                }
            }
        }, PacketListenerPriority.HIGHEST);
    }

    @Override
    public boolean allowSafeZoneUse() {
        return false;
    }

    @Getter
    @Setter
    @AllArgsConstructor
    private class InvisibilityProfile {
        private boolean isUpdated = false;
        private final int id;

        public InvisibilityProfile(int id){
            this.id = id;
        }
    }
}
