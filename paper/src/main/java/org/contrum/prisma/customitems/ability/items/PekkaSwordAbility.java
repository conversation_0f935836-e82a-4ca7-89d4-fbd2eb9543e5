package org.contrum.prisma.customitems.ability.items;

import com.github.retrooper.packetevents.PacketEvents;
import com.github.retrooper.packetevents.protocol.potion.PotionTypes;
import com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerEntityEffect;
import com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerRemoveEntityEffect;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.metadata.FixedMetadataValue;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.clients.PlayerClient;
import org.contrum.prisma.customitems.ability.CooldownAbility;
import org.contrum.prisma.customitems.ability.LivedCooldownAbility;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.utils.PlayerUtils;
import org.contrum.prisma.utils.UClansUtils;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.prisma.utils.time.TimeUtils;

import java.time.Duration;
import java.time.Instant;
import java.util.*;

public class PekkaSwordAbility extends LivedCooldownAbility {

    public static final String CORRUPTED_METADATA = "PEKKA_CORRUPTED";

    private double damage = this.getConfigSection().getDouble("DAMAGE", 12);
    private Duration corruption_duration = TimeUtils.parseDuration(this.getConfigSection().getString("CORRUPTION_DURATION", "6s"));
    private double corruption_thorns_percentage = this.getConfigSection().getLong("CORRUPTION_THORNS_PERCENTAGE", 20);
    private long hit_heal = this.getConfigSection().getLong("HIT_HEAL", 8);
    private long sword_protection_time = this.getConfigSection().getLong("SWORD_PROTECTION_TIME_MILLIS", 10000);

    private final HashMap<UUID, Instant> lastUseMap = new HashMap<>();

    public PekkaSwordAbility(PaperServices services) {
        super(services);
    }

    @Override
    public void onReload() {
        damage = this.getConfigSection().getDouble("DAMAGE", 12);
        corruption_duration = TimeUtils.parseDuration(this.getConfigSection().getString("CORRUPTION_DURATION", "6s"));
        corruption_thorns_percentage = this.getConfigSection().getLong("CORRUPTION_THORNS_PERCENTAGE", 20);
        hit_heal = this.getConfigSection().getLong("HIT_HEAL", 8);
        sword_protection_time = this.getConfigSection().getLong("SWORD_PROTECTION_TIME_MILLIS", 10000);
    }

    @Override
    public boolean shouldDestroy() {
        return true;
    }

    @Override
    public boolean allowSafeZoneUse() {
        return false;
    }

    @Override
    public String displayName(Player player) {
        return "Espada PEKKA";
    }

    @Override
    public String getID() {
        return "PEKKA_SWORD";
    }

    @Override
    public void hitToPlayer(Player damager, Player target, ItemStack item, EntityDamageByEntityEvent event) {
        if (UClansUtils.areSameClan(getServices(), damager, target)) {
            event.setCancelled(true);
            return;
        }

        if (this.hasCooldown(damager)) {
            this.sendCooldownMessage(damager);
            event.setCancelled(true);
            return;
        }

        //Last use cooldown
        Duration lastUseTime = this.getLastUseTime(target);
        if (!lastUseTime.isNegative() && lastUseTime.toMillis() <= sword_protection_time) {
            WorldUtils.playSound(Sound.ENTITY_IRON_GOLEM_REPAIR, damager, 1, 0.8F);
            damager.sendMessage(this.getLangMessage(damager, "SWORD_COOLDOWN"));
            event.setCancelled(true);
            return;
        }

        if (this.tryUse(damager, item, false)) {
            this.setLastUse(target);

            event.setDamage(EntityDamageEvent.DamageModifier.BASE, damage);
            this.setCorrupted(target);
            target.sendMessage(this.getLangMessage(target, "CORRUPTED", damager));

            PlayerUtils.heal(getServices(), damager, hit_heal, true);
            this.resetCooldowns(damager);
            damager.sendMessage(this.getLangMessage(damager, "USE", target));


            //Play sounds
            WorldUtils.playSound(Sound.BLOCK_PISTON_CONTRACT, damager.getLocation(), 2,0);
            TaskUtil.runLater(super.getPlugin(), () -> {
                WorldUtils.playSound(Sound.BLOCK_ANVIL_PLACE, damager.getLocation(), 0.5F, 0.3F);
                WorldUtils.broadcastParticle(getServices(), Particle.WITCH, target.getLocation().clone().add(0,1,0), 20, 1,1,1, 1);
            }, 8L);
        } else {
            event.setCancelled(true);
        }
    }

    public void setLastUse(Player player) {
        this.lastUseMap.put(player.getUniqueId(), Instant.now());
    }

    public void setCorrupted(Player player) {
        WrapperPlayServerEntityEffect effect = new WrapperPlayServerEntityEffect(player.getEntityId(), PotionTypes.WITHER, 0, (int) (corruption_duration.toSeconds() * 20), (byte) 0);
        PacketEvents.getAPI().getPlayerManager().sendPacket(player, effect);

        player.setMetadata(CORRUPTED_METADATA, new FixedMetadataValue(getServices().getPlugin(), true));

        TaskUtil.runLater(getPlugin(), () -> {
            player.removeMetadata(CORRUPTED_METADATA, getPlugin());
            WrapperPlayServerRemoveEntityEffect removeEffect = new WrapperPlayServerRemoveEntityEffect(player.getEntityId(), PotionTypes.WITHER);
            PacketEvents.getAPI().getPlayerManager().sendPacket(player, removeEffect);
        }, corruption_duration.toSeconds() * 20);
    }

    public boolean isCorrupted(Player player) {
        return player.hasMetadata(CORRUPTED_METADATA);
    }

    public Duration getLastUseTime(Player player) {
        Instant lastUse = this.lastUseMap.get(player.getUniqueId());
        if (lastUse == null) return Duration.ofSeconds(-1);
        return Duration.between(lastUse, Instant.now());
    }

    public void resetCooldowns(Player player) {
        ProfilePaperMetadata metadata = this.getServices().getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);

        //Remove cooldown for all abilities
        for (CooldownAbility item : super.getServices().getCustomItemsService().getItemsByClass(CooldownAbility.class)) {
            if (item instanceof LivedCooldownAbility) continue;
            Material material = item.getMaterial();
            metadata.removeCooldown(item.getCooldownID());
            player.setCooldown(material, 0);

            PlayerClient client = getServices().getClientsService().getClient(player);
            client.removeItemCooldown(player, material);
        }
    }

    @EventHandler(ignoreCancelled = true, priority = EventPriority.MONITOR)
    public void damage(EntityDamageByEntityEvent event) {
        if (event.getDamager() instanceof Player damager && event.getEntity() instanceof Player) {
            if (this.isCorrupted(damager)) {
                double damage = event.getFinalDamage();

                double returned_damage = damage * (corruption_thorns_percentage / 100);
                WorldUtils.playSound(Sound.ENTITY_WITHER_SKELETON_STEP, damager.getLocation(), 0.4F, 0F);
                WorldUtils.broadcastParticle(getServices(), Particle.SOUL, damager.getLocation(), 4, 0.5, 1, 0.5, 0);
                PlayerUtils.damage(damager, returned_damage, false, false, false, false);
            }
        }
    }
}
