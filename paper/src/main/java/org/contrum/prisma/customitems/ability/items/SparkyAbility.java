package org.contrum.prisma.customitems.ability.items;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customitems.ability.LivedCooldownAbility;
import org.contrum.prisma.systems.impl.BlockPlacementSystem;
import org.contrum.prisma.utils.PlayerUtils;
import org.contrum.prisma.utils.UClansUtils;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.prisma.utils.time.TimeUtils;

import java.time.Duration;

public class SparkyAbility extends LivedCooldownAbility {

    private double self_damage = this.getConfigSection().getDouble("SELF_DAMAGE", 4);
    private double target_damage = this.getConfigSection().getDouble("TARGET_DAMAGE", 12);
    private double explosion_min_power = this.getConfigSection().getDouble("EXPLOSION_POWER_MIN", 1.5);
    private double explosion_max_power = this.getConfigSection().getDouble("EXPLOSION_POWER_MAX", 4);
    private double explosion_vertical_force = this.getConfigSection().getDouble("EXPLOSION_VERTICAL_FORCE", 1);
    private double explosion_radius = this.getConfigSection().getDouble("EXPLOSION_RADIUS", 6);
    private Duration ability_cooldown = TimeUtils.parseDuration(this.getConfigSection().getString("ABILITY_COOLDOWN", "8s"));

    public SparkyAbility(PaperServices services) {
        super(services);
    }

    @Override
    public void onReload() {
        self_damage = this.getConfigSection().getDouble("SELF_DAMAGE", 4);
        target_damage = this.getConfigSection().getDouble("TARGET_DAMAGE", 12);
        explosion_min_power = this.getConfigSection().getDouble("EXPLOSION_POWER_MIN", 1.5);
        explosion_max_power = this.getConfigSection().getDouble("EXPLOSION_POWER_MAX", 4);
        explosion_vertical_force = this.getConfigSection().getDouble("EXPLOSION_VERTICAL_FORCE", 1);
        explosion_radius = this.getConfigSection().getDouble("EXPLOSION_RADIUS", 6);
        ability_cooldown = TimeUtils.parseDuration(this.getConfigSection().getString("ABILITY_COOLDOWN", "8s"));
    }

    @Override
    public boolean shouldDestroy() {
        return true;
    }

    @Override
    public boolean allowSafeZoneUse() {
        return false;
    }

    @Override
    public String displayName(Player player) {
        return "Chispitas";
    }

    @Override
    public String getID() {
        return "SPARKY";
    }

    @Override
    public void onClick(Player player, ItemStack item, boolean isRight, PlayerInteractEvent event) {
        if (isRight) {
            if (this.hasCooldown(player)) {
                this.sendCooldownMessage(player);
                event.setCancelled(true);
                return;
            }

            if (this.tryUse(player, item)) {
                //Remove nearby cobwebs
                BlockPlacementSystem system = getServices().getSystemService().getSystem(BlockPlacementSystem.class);
                Location location = player.getLocation();
                ItemStack shears = new ItemStack(Material.SHEARS);
                WorldUtils.processBlocks(location, 10, 4, 10, block -> {
                    if (block.getType() == Material.COBWEB && system.isPlacedByPlayer(block)) {
                        block.breakNaturally(shears);
                    }
                });

                WorldUtils.playSound(Sound.BLOCK_NOTE_BLOCK_BELL, player.getLocation(), 1, 0);
                //Apply explosion later
                TaskUtil.runLater(getServices().getPlugin(), () -> {
                    WorldUtils.playSound(Sound.ENTITY_GENERIC_EXPLODE, player.getLocation(), 1, 1);
                    WorldUtils.applyExplosionKnockback(player.getLocation(), explosion_radius, explosion_min_power, explosion_max_power, explosion_vertical_force, (p) -> !UClansUtils.areSameClan(getServices(), player, p));
                    PlayerUtils.damage(player, self_damage, false, true, false);
                    for (Player target : player.getLocation().getNearbyPlayers(explosion_radius)) {
                        if (UClansUtils.areSameClan(super.getServices(), player, target)) {
                            continue;
                        }

                        this.applyGlobalAbilityCooldown(target, ability_cooldown);
                        PlayerUtils.damage(target, target_damage, false, true, false);
                    }
                }, 5L);
            }
        }
    }
}
