package org.contrum.prisma.customitems.ability.items;

import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.player.PlayerCommandPreprocessEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customitems.ability.LivedCooldownAbility;
import org.contrum.prisma.events.PlayerAbilityEffectEvent;
import org.contrum.prisma.systems.impl.CustomEffectSystem;
import org.contrum.prisma.utils.UClansUtils;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.prisma.utils.time.TimeUtils;

import java.time.Duration;

public class GloryOfGolemAbility extends LivedCooldownAbility {

    public static final String METADATA_IMMUNITY = "glory_of_golem_immunity";

    private Duration immunity_duration = TimeUtils.parseDuration(this.getConfigSection().getString("IMMUNITY_DURATION", "15s"));
    private int weakness_amplifier = this.getConfigSection().getInt("WEAKNESS_AMPLIFIER", 2);
    private int slown_amplifier = this.getConfigSection().getInt("SLOW_AMPLIFIER", 1);

    private int allies_search_range = this.getConfigSection().getInt("ALLIES_SEARCH_RANGE", 10);
    private Duration ally_resistance_duration = TimeUtils.parseDuration(this.getConfigSection().getString("ALLY_RESISTANCE_DURATION", "15s"));
    private int ally_resistance_amplifier = this.getConfigSection().getInt("ALLY_RESISTANCE_AMPLIFIER", 1);

    public GloryOfGolemAbility(PaperServices services) {
        super(services);
    }

    @Override
    public String displayName(Player player) {
        return "Gloria del Gólem";
    }

    @Override
    public String getID() {
        return "GLORY_OF_GOLEM";
    }

    @Override
    public boolean allowSafeZoneUse() {
        return false;
    }

    @Override
    public boolean shouldDestroy() {
        return true;
    }

    @Override
    public void onReload() {
    }

    public boolean isImmune(Player player) {
        return player.hasMetadata(METADATA_IMMUNITY);
    }

    public void applyEffect(Player target) {
        target.setMetadata(METADATA_IMMUNITY, new FixedMetadataValue(this.getServices().getPlugin(), true));

        TaskUtil.runLater(super.getPlugin(), () -> {
            target.removeMetadata(METADATA_IMMUNITY, super.getServices().getPlugin());
            WorldUtils.playSound(Sound.ENTITY_CAT_AMBIENT, target.getLocation(), 1, 1);
            sendLangMessage(target, "IMMUNITY_ENDED");
        }, immunity_duration.toMillis() / 50);
    }

    @Override
    public void onClick(Player player, ItemStack item, boolean isRight, PlayerInteractEvent event) {
        if (!isRight) return;
        if (this.hasCooldown(player)) {
            this.sendCooldownMessage(player);
            event.setCancelled(true);
            return;
        }
        if (!this.tryUse(player, item)) {
            event.setCancelled(true);
            return;
        };

        WorldUtils.playSound(Sound.ENTITY_IRON_GOLEM_DEATH, player.getLocation(), 1, 0.5F);
        WorldUtils.playSound(Sound.ENTITY_ILLUSIONER_CAST_SPELL, player.getLocation(), 1, 0);
        WorldUtils.broadcastParticle(getServices(), Particle.FALLING_DUST, player.getLocation().add(0,1,0), 15, 0.25, 1, 0.25, Material.IRON_BLOCK.createBlockData());
        this.applyEffect(player);

        super.sendLangMessage(player, "USE", immunity_duration);

        // apply nerf
        CustomEffectSystem customEffectSystem = super.getServices().getSystemService().getSystem(CustomEffectSystem.class);
        customEffectSystem.addFakeWeaknessEffect(player, (int) immunity_duration.toSeconds(), weakness_amplifier);
        player.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, (int) (immunity_duration.toMillis() / 50), slown_amplifier));

        // Buff nearby allies
        for (Player nearbyPlayer : player.getLocation().getNearbyPlayers(allies_search_range)) {
            if (!nearbyPlayer.equals(player) && UClansUtils.areSameClan(getServices(), player, nearbyPlayer)) {
                if (!super.callAbilityEffectEvent(player, nearbyPlayer, false))
                    continue;

                nearbyPlayer.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, (int) (ally_resistance_duration.toMillis() / 50), ally_resistance_amplifier));
                super.sendLangMessage(nearbyPlayer, "BUFFED_BY_ALLY", player);
                WorldUtils.broadcastParticle(getServices(), Particle.WAX_ON, nearbyPlayer.getLocation().add(0,1,0), 10, 0.9, 0.2, 0.9, 1);
            }
        }
    }

    @EventHandler(priority = EventPriority.HIGHEST)
    public void entityDamage(EntityDamageByEntityEvent event) {
        if (event.getEntity() instanceof Player player && event.getDamager() instanceof Player damager) {
            if (this.isImmune(player)) {
                event.setCancelled(true);
                WorldUtils.broadcastParticle(getServices(), Particle.FALLING_DUST, player.getLocation().add(0,1,0), 15, 0.25, 1, 0.25, Material.IRON_BLOCK.createBlockData());
                WorldUtils.playSound(Sound.ENTITY_IRON_GOLEM_REPAIR, player.getLocation(), 1, 0.5F);

                super.sendLangMessage(damager, "TARGET_IS_IMMUNE", player);
            }
        }
    }

    @EventHandler
    public void ability(PlayerAbilityEffectEvent event) {
        if (event.isNegative() && this.isImmune(event.getTarget())) {
            event.setCancelled(true);

            if (event.getDamager() != null) {
                super.sendLangMessage(event.getDamager(), "TARGET_IS_IMMUNE", event.getTarget());
            }
        }
    }

    @EventHandler
    public void command(PlayerCommandPreprocessEvent event) {
        Player player = event.getPlayer();
        if (this.isImmune(player)) {
            event.setCancelled(true);
            super.sendLangMessage(player, "CANNOT_USE_COMMAND_WHILE_IMMUNE");
        }
    }
}
