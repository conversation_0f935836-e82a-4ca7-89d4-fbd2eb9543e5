package org.contrum.prisma.customitems.ability.items;

import com.destroystokyo.paper.event.player.PlayerLaunchProjectileEvent;
import com.sk89q.worldguard.protection.flags.Flags;
import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.entity.Projectile;
import org.bukkit.event.EventHandler;
import org.bukkit.event.entity.ProjectileHitEvent;
import org.bukkit.event.player.PlayerEggThrowEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.projectiles.ProjectileSource;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customitems.ability.CooldownAbility;
import org.contrum.prisma.utils.PlayerUtils;
import org.contrum.prisma.utils.WorldGuardUtils;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

public class SwitcherAbility extends CooldownAbility {
    private double maxDistance = this.getConfigSection().getInt("MAX_DISTANCE", 15);

    public SwitcherAbility(PaperServices services) {
        super(services);
    }

    @Override
    public void onReload() {
        this.maxDistance = this.getConfigSection().getInt("MAX_DISTANCE", 15);
    }

    @Override
    public String displayName(Player player) {
        return "Switcher";
    }

    @Override
    public String getID() {
        return "SWITCHER";
    }

    @Override
    public boolean allowSafeZoneUse() {
        return false;
    }

    @Override
    public void onThrow(Player thrower, ItemStack item, Projectile projectile, PlayerLaunchProjectileEvent event) {
        //Check for cooldown
        if (this.hasCooldown(thrower)){
            this.sendCooldownMessage(thrower);
            event.setCancelled(true);
            return;
        }

        if (!this.tryUse(thrower, item)) {
            event.setCancelled(true);
            return;
        }

        event.getProjectile().setVelocity(event.getPlayer().getLocation().getDirection().normalize().multiply(3));
    }

    @Override
    public void onProjectileImpact(ProjectileSource source, Projectile projectile, ProjectileHitEvent event) {
        if (!(source instanceof Player thrower)) return;
        event.setCancelled(true);
        if (event.getHitEntity() != null && event.getHitEntity() instanceof Player player) {
            if (PlayerUtils.isNPC(player)) return;

            Location playerLocation = player.getLocation();
            Location throwerLocation = thrower.getLocation();

            if (WorldGuardUtils.isDeny(playerLocation, Flags.PVP) || WorldGuardUtils.isDeny(throwerLocation, Flags.PVP)) return;

            //Check max distance
            if (throwerLocation.distance(playerLocation) > maxDistance){
                thrower.sendMessage(this.getLangMessage(thrower, "DISTANCE_EXCEEDED", LocalPlaceholders.builder().add("<distance>", String.valueOf((int) maxDistance))));
                return;
            }

            player.teleportAsync(throwerLocation);
            thrower.teleportAsync(playerLocation);

            player.sendMessage(this.getLangMessage(player, "SWITCHED_MESSAGE", thrower));
            thrower.sendMessage(this.getLangMessage(thrower, "SWITCHED_MESSAGE", player));
            WorldUtils.playSound(Sound.ENTITY_ENDERMAN_TELEPORT, thrower.getLocation());
            WorldUtils.playSound(Sound.ENTITY_ENDERMAN_TELEPORT, player.getLocation());

            projectile.remove();
        }
    }

    @EventHandler
    public void preventChickenSpawn(PlayerEggThrowEvent event){
        ItemStack item = event.getEgg().getItem();
        if (this.isItem(item)){
            event.setHatching(false);
        }
    }
}
