package org.contrum.prisma.customitems.ability.items;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customitems.ability.CooldownAbility;

public class StrengthAbility extends CooldownAbility {

    private final int strength_level = this.getConfigSection().getInt("STRENGTH_LEVEL", 2);
    private final int strength_duration = this.getConfigSection().getInt("STRENGTH_DURATION", 15);

    public StrengthAbility(PaperServices services) {
        super(services);
    }

    @Override
    public void onClick(Player player, ItemStack item, boolean isRight, PlayerInteractEvent event) {
        if (isRight) {
            if (this.hasCooldown(player)){
                this.sendCooldownMessage(player);
                event.setCancelled(true);
                return;
            }

            if (this.tryUse(player, item)) {
                //Give effect
                PotionEffect effect = new PotionEffect(PotionEffectType.STRENGTH, 20 * strength_duration, strength_level);

                player.addPotionEffect(effect);
            }
        }
    }

    @Override
    public boolean allowSafeZoneUse() {
        return false;
    }

    @Override
    public String displayName(Player player) {
        return "Fuerza";
    }

    @Override
    public String getID() {
        return "STRENGTH";
    }
}
