
package org.contrum.prisma.customitems.ability.items;

import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customitems.ability.CooldownAbility;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.utils.WorldUtils;

import java.util.List;

public class BatBiteAbility extends CooldownAbility {

    public static final String METADATA_LIFE_STEALER = "bat_bite_life_stealer";

    private double health_steal_amount = this.getConfigSection().getDouble("HEALTH_STEAL_AMOUNT", 20);
    private double health_steal_per_tick_amount = this.getConfigSection().getDouble("HEALTH_STEAL_PER_TICK_AMOUNT", 0.5);
    private int health_steal_interval = this.getConfigSection().getInt("HEALTH_STEAL_INTERVAL", 1);
    private List<String> steal_effects = this.getConfigSection().getStringList("STEAL_EFFECTS");

    public BatBiteAbility(PaperServices services) {
        super(services);
    }

    @Override
    public boolean allowSafeZoneUse() {
        return false;
    }

    @Override
    public String displayName(Player player) {
        return "Mordedura Letal";
    }

    @Override
    public String getID() {
        return "BAT_BITE";
    }

    @Override
    public void onReload() {
        this.health_steal_amount = this.getConfigSection().getDouble("HEALTH_STEAL_AMOUNT", 20);
        this.health_steal_per_tick_amount = this.getConfigSection().getDouble("HEALTH_STEAL_PER_TICK_AMOUNT", 0.5);
        this.health_steal_interval = this.getConfigSection().getInt("HEALTH_STEAL_INTERVAL", 1);
        this.steal_effects = this.getConfigSection().getStringList("STEAL_EFFECTS");
    }

    public boolean isStealing(Player player) {
        return player.hasMetadata(METADATA_LIFE_STEALER);
    }

    @Override
    public void hitToPlayer(Player damager, Player player, ItemStack item, EntityDamageByEntityEvent event) {
        if (this.isStealing(player))
            return;

        if (this.hasCooldown(damager)) {
            this.sendCooldownMessage(damager);
            event.setCancelled(true);
            return;
        }

        if (!super.callAbilityEffectEvent(damager, player, true)) {
            return;
        }

        if (!tryUse(damager, item)) return;
        item.subtract();

        //Steal effects
        for (PotionEffect effect : player.getActivePotionEffects()) {
            if (steal_effects.contains(effect.getType().getName())) {
                player.removePotionEffect(effect.getType());
                damager.addPotionEffect(effect);
            }
        }

         this.sendLangMessage(damager, "BITTEN_TO", player);
        this.sendLangMessage(player, "BITTEN_BY", damager);
        WorldUtils.playSound(Sound.ENTITY_GENERIC_DRINK, player.getLocation(), 1, 0);
        WorldUtils.broadcastParticle(getServices(), Particle.FALLING_DUST, player.getLocation().add(0,1,0), 15, 1, 1, 1, Material.REDSTONE_BLOCK.createBlockData());

        player.setMetadata(METADATA_LIFE_STEALER, new FixedMetadataValue(getPlugin(), true));
        ProfilePaperMetadata metadata = getServices().getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
        // Slowly steal health
        TaskUtil.runTimer(getPlugin(), new BukkitRunnable() {

            double remainingHealth = health_steal_amount;

            @Override
            public void run() {
                if (!player.isOnline() || !isStealing(player) || remainingHealth <= 0) {
                    player.removeMetadata(METADATA_LIFE_STEALER, getPlugin());
                    cancel();
                    return;
                }

                // Check for totem protection
                if (metadata.getTimeSinceLastTotem().toSeconds() < 3) {
                    remainingHealth -= health_steal_per_tick_amount / 2;
                    return;
                }

                double to_steal = Math.min(remainingHealth, health_steal_per_tick_amount);

                // First steal absorption
                double absorptionAmount = player.getAbsorptionAmount();
                if (absorptionAmount > 0) {
                    double stolenFromAbsorption = Math.min(to_steal, absorptionAmount);
                    double newAbsorption = absorptionAmount - stolenFromAbsorption;
                    player.setAbsorptionAmount(newAbsorption);
                    if (newAbsorption <= 0) {
                        player.removePotionEffect(PotionEffectType.ABSORPTION);
                    }
                    to_steal-=stolenFromAbsorption;
                }


                // Steal health
                if (to_steal >= 0) {
                    double currentHealth = player.getHealth();
                    double amountStolen = Math.min(to_steal, currentHealth - 1);
                    double newHealth = currentHealth - amountStolen;
                    player.setHealth(newHealth);

                    to_steal -= amountStolen;
                }

                double toHeal = health_steal_per_tick_amount - to_steal;
                if (toHeal > 0) {
                    double currentHealth = damager.getHealth();
                    double maxHealth = damager.getMaxHealth();
                    double healedAmount = Math.min(currentHealth + toHeal, maxHealth) - currentHealth;
                    double overflow = toHeal - healedAmount;
                    damager.setHealth(currentHealth + healedAmount);

                    if (overflow > 0) {
                        if (damager.getAbsorptionAmount() < 20) {
                            double newAbsorption = damager.getAbsorptionAmount() + overflow;
                            damager.setAbsorptionAmount(Math.min(newAbsorption, 20));
                        }
                    }
                }

                remainingHealth -= health_steal_per_tick_amount;
            }
        }, 0, health_steal_interval);
    }

    @EventHandler
    public void onDeath(PlayerDeathEvent event) {
        if (this.isStealing(event.getPlayer())) {
            event.getPlayer().removeMetadata(METADATA_LIFE_STEALER, getPlugin());
        }
    }
}