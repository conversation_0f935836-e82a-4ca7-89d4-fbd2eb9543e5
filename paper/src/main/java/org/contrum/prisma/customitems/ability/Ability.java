/*
 *  This file is part of the Apple Core project.
 *  Copyright (c) 2022-2024. Contrum Services
 *  Created by izLoki on 09/06/2024
 *  Website: contrum.org
 */

package org.contrum.prisma.customitems.ability;

import com.destroystokyo.paper.event.player.PlayerLaunchProjectileEvent;
import com.sk89q.worldguard.protection.flags.Flags;
import lombok.Getter;
import net.kyori.adventure.text.Component;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.entity.Projectile;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.ProjectileHitEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.projectiles.ProjectileSource;
import org.contrum.chorpu.xseries.XMaterial;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customitems.ItemCustomListener;
import org.contrum.prisma.events.PlayerAbilityEffectEvent;
import org.contrum.prisma.events.PlayerTryUseAbilityEvent;
import org.contrum.prisma.events.PlayerUseAbilityEvent;
import org.contrum.prisma.utils.ItemBuilder1_20;
import org.contrum.prisma.utils.NBTUtil;
import org.contrum.prisma.utils.WorldGuardUtils;

import java.util.List;

@Getter
public abstract class Ability extends ItemCustomListener implements Listener {
    private final PaperServices services;

    public Ability(PaperServices services) {
        super(services);
        this.services = services;
    }

    @Override
    public boolean isItem(ItemStack item) {
        return item != null && item.hasItemMeta() && NBTUtil.getNBT(item.getItemMeta(), this.getDisplayID(), PersistentDataType.BOOLEAN, false);
    }

    @Override
    public ItemStack getItemStack() {
        ConfigurationSection section = this.getConfigSection();

        ItemBuilder1_20 builder = new ItemBuilder1_20(XMaterial.matchXMaterial(section.getString("MATERIAL", "STONE")).get().parseMaterial());
        builder.name(services.getLanguageHandler().getPlaceholderManager().apply(section.getString("DISPLAY_NAME", "none")));
        builder.lore(section.getStringList("LORE").stream().map(s -> services.getLanguageHandler().getPlaceholderManager().apply(s)).toList());
        ConfigurationSection enchantsSection = section.getConfigurationSection("ENCHANTMENTS");
        if (enchantsSection != null){
            for (String id : enchantsSection.getKeys(false)) {
                Enchantment enchantment = Enchantment.getByName(id);
                int level = enchantsSection.getInt(id, 1);
                if (enchantment == null) {
                    Bukkit.getLogger().warning("Enchantment '" + id + "' doesn't exist!");
                    continue;
                }

                builder.addUnsafeEnchantment(enchantment, level);
            }
        }
        List<String> tags = section.getStringList("TAGS");
        tags.add(this.getDisplayID());
        tags.add("CustomItem");
        for (String tag : tags) {
            builder.addNBT(tag, PersistentDataType.BOOLEAN, true);
        }
        builder.unbreakable(section.getBoolean("UNBREAKABLE", true));
        builder.hideInformation();
        return builder.build();
    }

    public abstract String displayName(Player player);

    public abstract boolean allowSafeZoneUse();

    public Material getMaterial() {
        return XMaterial.matchXMaterial(this.getConfigSection().getString("MATERIAL", "STONE")).get().parseMaterial();
    }

    public void sendLangMessage(CommandSender target, String text, Object... replacements) {
        services.getTranslator().send(target, "ABILITY." + this.getID() + "." + text, replacements);
    }

    public Component getLangMessage(CommandSender player, String text, Object... replacements) {
        return services.getTranslator().getAsComponent(player, "ABILITY." + this.getID() + "." + text, replacements);
    }

    public FileConfiguration getConfigurationFile() {
        return services.getCustomItemsService().getConfig().getConfig();
    }

    public ConfigurationSection getConfigSection() {
        return this.getConfigurationFile().getConfigurationSection(this.getID());
    }

    public boolean isEnabled() {
        return this.getConfigSection().getBoolean("ENABLED", true);
    }

    public boolean canUse(Player player, ItemStack item){
        if (!this.allowSafeZoneUse() && WorldGuardUtils.isDeny(player.getLocation(), Flags.PVP)) {
            return false;
        }
        PlayerTryUseAbilityEvent event = new PlayerTryUseAbilityEvent(player, this, item);
        Bukkit.getPluginManager().callEvent(event);
        return !event.isCancelled();
    }

    public boolean canUseMessage(Player player) {
        if (!this.isEnabled()) {
            services.getTranslator().send(player, "ERRORS.ABILITY_DISABLED");
            return false;
        }
        if (!this.allowSafeZoneUse() && WorldGuardUtils.isDeny(player.getLocation(), Flags.PVP)) {
            services.getTranslator().send(player, "ERRORS.SAFE_ZONE");
            return false;
        }
        return true;
    }

    public boolean tryUse(Player player, ItemStack item) {
        if (!this.canUseMessage(player)) return false;
        PlayerUseAbilityEvent event = new PlayerUseAbilityEvent(player, this, item);
        return event.callEvent();
    }

    public boolean callAbilityEffectEvent(Player player, Player target, boolean negative) {
        PlayerAbilityEffectEvent event = new PlayerAbilityEffectEvent(player, target, this, negative);
        Bukkit.getPluginManager().callEvent(event);
        return !event.isCancelled();
    }

    public void onClick(Player player, ItemStack item, boolean isRight, PlayerInteractEvent event){}

    public void hitToPlayer(Player damager, Player player, ItemStack item, EntityDamageByEntityEvent event){}

    public void onThrow(Player thrower, ItemStack item, Projectile projectile, PlayerLaunchProjectileEvent event){}

    public void onProjectileImpact(ProjectileSource thrower, Projectile projectile, ProjectileHitEvent event){}

    @EventHandler
    public final void playerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        if (item != null && this.isItem(item)) {
            this.onClick(player, item, event.getAction().isRightClick(), event);
        }
    }

    @EventHandler(ignoreCancelled = true, priority = EventPriority.HIGHEST)
    public final void entityDamageByEntity(EntityDamageByEntityEvent event) {
        if (event.getDamager() instanceof Player damager && event.getEntity() instanceof Player player) {
            ItemStack item = damager.getInventory().getItemInMainHand();
            if (isItem(item)) this.hitToPlayer(damager, player, item, event);
        }
    }

    @EventHandler(ignoreCancelled = true)
    public final void throwItem(PlayerLaunchProjectileEvent event) {
        Player player = event.getPlayer();
        if (this.isItem(event.getItemStack())) {
            Projectile projectile = event.getProjectile();
            projectile.setMetadata(this.getDisplayID(), new FixedMetadataValue(services.getPlugin(), true));
            this.onThrow(player, event.getItemStack(), projectile, event);
        }
    }

    @EventHandler
    public final void projectileImpact(ProjectileHitEvent event) {
        Projectile projectile = event.getEntity();
        if (!projectile.hasMetadata(this.getDisplayID())) return;
        this.onProjectileImpact(projectile.getShooter(), projectile, event);
    }
}