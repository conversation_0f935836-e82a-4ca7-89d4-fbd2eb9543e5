
package org.contrum.prisma.customitems.ability.items;

import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customitems.ability.CooldownAbility;
import org.contrum.prisma.utils.UClansUtils;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

import java.util.List;

public class HealerAbility extends CooldownAbility {

    private int duration = this.getConfigSection().getInt("DURATION", 15);
    private int allies_search_range = this.getConfigSection().getInt("ALLIES_SEARCH_RANGE", 10);

    public HealerAbility(PaperServices services) {
        super(services);
    }

    @Override
    public boolean allowSafeZoneUse() {
        return false;
    }

    @Override
    public String displayName(Player player) {
        return "Curación";
    }

    @Override
    public void onReload() {
        this.duration = this.getConfigSection().getInt("DURATION", 15);
        this.allies_search_range = this.getConfigSection().getInt("ALLIES_SEARCH_RANGE", 10);
    }

    @Override
    public String getID() {
        return "HEALER";
    }

    @Override
    public void onClick(Player player, ItemStack item, boolean isRight, PlayerInteractEvent event) {
        if (!isRight) return;
        if (this.hasCooldown(player)){
            this.sendCooldownMessage(player);
            event.setCancelled(true);
            return;
        }
        if (!this.tryUse(player, item)) return;

        //Use item
        item.subtract();
        List<Player> clanPlayers = UClansUtils.getNearbyClanPlayers(getServices(), player, allies_search_range);
        World world = player.getWorld();

        player.sendMessage(this.getLangMessage(player, "USE", LocalPlaceholders.builder().add("<allies>", clanPlayers.size() + "")));
        WorldUtils.playSound(Sound.ITEM_TRIDENT_RETURN, player.getLocation(), 1, 2);
        world.spawnParticle(Particle.WAX_ON, player.getLocation().add(0,1,0), 15, 0.9, 0.2, 0.9, 1);

        player.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, 10*duration, 2));
        player.addPotionEffect(new PotionEffect(PotionEffectType.ABSORPTION, 20*duration, 2));
        player.addPotionEffect(new PotionEffect(PotionEffectType.REGENERATION, 20*duration, 2));
        player.setFoodLevel(20);
        player.setSaturation(5);
        for (Player clanPlayer : clanPlayers) {
            if (!super.callAbilityEffectEvent(player, clanPlayer, false))
                continue;

            clanPlayer.sendMessage(this.getLangMessage(clanPlayer, "RECEIVED_BY_ALLY"));
            WorldUtils.playSound(Sound.ENTITY_PLAYER_LEVELUP, clanPlayer, 1, 1);
            world.spawnParticle(Particle.WAX_ON, clanPlayer.getLocation().add(0,1,0), 10, 0.9, 0.2, 0.9, 1);
            clanPlayer.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, 10*duration, 1));
            clanPlayer.addPotionEffect(new PotionEffect(PotionEffectType.REGENERATION, 20*duration, 2));
            clanPlayer.setFoodLevel(20);
            clanPlayer.setSaturation(5);
        }
    }
}
