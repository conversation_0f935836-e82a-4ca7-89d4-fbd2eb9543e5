package org.contrum.prisma.customitems;

import lombok.Getter;
import org.bukkit.Bukkit;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.plugin.java.JavaPlugin;
import org.contrum.prisma.PaperServices;

@Getter
public abstract class ItemCustomListener implements ItemCustom, Listener {
    private final PaperServices services;
    private final JavaPlugin plugin;

    public ItemCustomListener(PaperServices services) {
        this.services = services;
        this.plugin = services.getPlugin();
        Bukkit.getPluginManager().registerEvents(this, services.getPlugin());
    }

    @Override
    public void onReload() {

    }

    public void unload() {
        HandlerList.unregisterAll(this);
    }
}
