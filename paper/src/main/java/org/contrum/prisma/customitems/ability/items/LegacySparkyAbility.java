package org.contrum.prisma.customitems.ability.items;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.GameMode;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customitems.ability.LivedCooldownAbility;
import org.contrum.prisma.utils.PlayerUtils;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.prisma.utils.UClansUtils;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class LegacySparkyAbility extends LivedCooldownAbility {

    private int stun_duration = this.getConfigSection().getInt("STUN_DURATION", 20);
    private double stun_damage = this.getConfigSection().getDouble("STUN_DAMAGE", 15);
    private double stun_radius = this.getConfigSection().getDouble("STUN_RADIUS", 8);

    public LegacySparkyAbility(PaperServices services) {
        super(services);
    }

    @Override
    public void onReload() {
        this.stun_duration = this.getConfigSection().getInt("STUN_DURATION", 20);
        this.stun_damage = this.getConfigSection().getDouble("STUN_DAMAGE", 15);
        this.stun_radius = this.getConfigSection().getDouble("STUN_RADIUS", 8);
    }

    @Override
    public boolean shouldDestroy() {
        return true;
    }

    @Override
    public boolean allowSafeZoneUse() {
        return false;
    }

    @Override
    public String displayName(Player player) {
        return "Chispitas";
    }

    @Override
    public String getID() {
        return "SPARKY";
    }

    @Override
    public String getDisplayID() {
        return "LEGACY_SPARKY";
    }

    @Override
    public void onClick(Player player, ItemStack item, boolean isRight, PlayerInteractEvent event) {
        if (isRight) {
            if (this.hasCooldown(player)) {
                this.sendCooldownMessage(player);
                event.setCancelled(true);
                return;
            }

            if (this.canUseMessage(player))
                this.hold(player, item);
        }
    }

    private static final long REQUIRED_HOLD_TIME = 3000;
    private static final long RESET_THRESHOLD = 500;

    private final Map<UUID, HoldStatus> holdMap = new HashMap<>();

    public void hold(Player player, ItemStack item) {
        UUID playerId = player.getUniqueId();
        long currentTime = System.currentTimeMillis();

        HoldStatus status = holdMap.computeIfAbsent(playerId, f -> new HoldStatus(4, currentTime, currentTime));

        if (currentTime - status.getLastClick() > RESET_THRESHOLD) {
            holdMap.remove(playerId);
            player.resetTitle();
            return;
        }

        long elapsedTime = currentTime - status.getStartTime();
        long remainingTime = REQUIRED_HOLD_TIME - elapsedTime;
        status.setLastClick(currentTime);

        if (elapsedTime >= REQUIRED_HOLD_TIME) {
            if (this.tryUse(player, item)) {
                this.strike(player);
            }

            holdMap.remove(playerId);
            return;
        }

        int secondsLeft = (int) (remainingTime / 1000) + 1;
        if (secondsLeft != status.getRemaining() && secondsLeft <= status.getRemaining()) {
            player.sendTitle("", secondsLeft + "s", 0, 30, 0);
            WorldUtils.playSound(Sound.BLOCK_NOTE_BLOCK_BELL, player.getLocation(), 1, 0);
            status.setRemaining(secondsLeft);
        }
    }

    public void strike(Player owner) {
        WorldUtils.playSound(Sound.ITEM_TRIDENT_THUNDER, owner.getLocation(), 1, 0.25F);

        List<Player> enemies = UClansUtils.getNearbyEnemyPlayers(super.getServices(), owner, stun_radius);

        double health = 0;

        World world = owner.getWorld();
        for (Player enemy : enemies) {
            //Set stun
            //TODO: Permanent particles
            if (PlayerUtils.damage(enemy, this.stun_damage, false, false, true)) {
                WorldUtils.strikeLightningEffect(getServices(), enemy.getLocation());
                health+=stun_damage;
                
                enemy.sendMessage(this.getLangMessage(enemy, "STUNNED", owner));
                enemy.setMetadata("STRIKE_STUNNED", new FixedMetadataValue(super.getPlugin(), true));

                TaskUtil.runLater(super.getPlugin(), () -> {
                    enemy.removeMetadata("STRIKE_STUNNED", this.getPlugin());
                    if (enemy.isOnline()) {
                        enemy.sendMessage(this.getLangMessage(enemy, "STUN_FINISHED"));
                        WorldUtils.playSound(Sound.ENTITY_CAT_AMBIENT, enemy, 1, 1.5F);
                    }
                }, 20L * stun_duration);
            }
        }

        owner.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, 20*10, 2, false, false));
        PlayerUtils.heal(owner, health / 2);

        owner.sendMessage(this.getLangMessage(owner, "USE", LocalPlaceholders.builder().add("<affected_players>", enemies.size() + "")));
    }

    public boolean isStunned(Player player) {
        return player.hasMetadata("STRIKE_STUNNED");
    }

    @EventHandler(priority = EventPriority.HIGHEST, ignoreCancelled = true)
    public void blockPlace(BlockPlaceEvent event) {
        Player player = event.getPlayer();
        if (player.getGameMode().equals(GameMode.CREATIVE)) return;

        if (this.isStunned(player)) {
            player.sendMessage(this.getLangMessage(player, "CANNOT_PLACE_STUNNED"));
            event.setCancelled(true);
        }
    }

    @EventHandler(priority = EventPriority.HIGHEST, ignoreCancelled = true)
    public void blockBreak(BlockBreakEvent event) {
        Player player = event.getPlayer();
        if (player.getGameMode().equals(GameMode.CREATIVE)) return;

        if (this.isStunned(player)) {
            player.sendMessage(this.getLangMessage(player, "CANNOT_BREAK_STUNNED"));
            event.setCancelled(true);
        }
    }

    @Getter @Setter
    @AllArgsConstructor
    public static class HoldStatus {
        private int remaining;
        private long startTime;
        private long lastClick;
    }
}
