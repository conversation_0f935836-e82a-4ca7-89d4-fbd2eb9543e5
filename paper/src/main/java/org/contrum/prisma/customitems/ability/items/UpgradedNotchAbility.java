package org.contrum.prisma.customitems.ability.items;

import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityResurrectEvent;
import org.bukkit.event.player.PlayerItemConsumeEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customitems.ability.Ability;

public class UpgradedNotchAbility extends Ability implements Listener {

    public UpgradedNotchAbility(PaperServices services) {
        super(services);
    }

    @Override
    public String displayName(Player player) {
        return "Manzana Mejorada";
    }

    @Override
    public String getID() {
        return "UPGRADED_NOTCH";
    }

    @Override
    public boolean allowSafeZoneUse() {
        return true;
    }

    @Override
    public Material getMaterial() {
        return Material.ENCHANTED_GOLDEN_APPLE;
    }

    @EventHandler
    public void EatNotch(PlayerItemConsumeEvent event){
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        if (this.isItem(item)){
            if (!this.tryUse(player, item)) return;

            player.addPotionEffect(new PotionEffect(PotionEffectType.REGENERATION, 20 * 25, 2));
            player.addPotionEffect(new PotionEffect(PotionEffectType.ABSORPTION, 20 * 60 * 3, 4));
            player.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, 20 * 60 * 5, 0));
            player.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, 20 * 60, 1));
        }
    }
}
