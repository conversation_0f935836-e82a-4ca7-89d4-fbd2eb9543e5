package org.contrum.prisma.customitems.ability.items;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customitems.ability.CooldownAbility;
import org.contrum.prisma.customitems.ability.LivedCooldownAbility;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.systems.impl.CustomEffectSystem;
import org.contrum.prisma.utils.PlayerUtils;
import org.contrum.prisma.utils.UClansUtils;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

public class WardenAbility extends CooldownAbility {

    // Configuration variables
    private int timer_duration = this.getConfigSection().getInt("TIMER_DURATION", 15);
    private int hits_per_wave = this.getConfigSection().getInt("HITS_PER_WAVE", 3);
    private int max_waves = this.getConfigSection().getInt("MAX_WAVES", 4);
    private double wave_damage = this.getConfigSection().getDouble("WAVE_DAMAGE", 5.0);
    private double wave_radius = this.getConfigSection().getDouble("WAVE_RADIUS", 6.0);
    private double ally_search_radius = this.getConfigSection().getDouble("ALLY_SEARCH_RADIUS", 8.0);
    private int strength_duration_per_wave = this.getConfigSection().getInt("STRENGTH_DURATION_PER_WAVE", 5);

    // Active ability states tracking
    private final Map<UUID, WardenState> activeStates = new HashMap<>();

    public WardenAbility(PaperServices services) {
        super(services);
    }

    @Override
    public void onReload() {
        this.timer_duration = this.getConfigSection().getInt("TIMER_DURATION", 15);
        this.hits_per_wave = this.getConfigSection().getInt("HITS_PER_WAVE", 3);
        this.max_waves = this.getConfigSection().getInt("MAX_WAVES", 4);
        this.wave_damage = this.getConfigSection().getDouble("WAVE_DAMAGE", 5.0);
        this.wave_radius = this.getConfigSection().getDouble("WAVE_RADIUS", 6.0);
        this.ally_search_radius = this.getConfigSection().getDouble("ALLY_SEARCH_RADIUS", 8.0);
        this.strength_duration_per_wave = this.getConfigSection().getInt("STRENGTH_DURATION_PER_WAVE", 5);
    }

    @Override
    public boolean allowSafeZoneUse() {
        return false;
    }

    @Override
    public String displayName(Player player) {
        return "Warden Sonic Waves";
    }

    @Override
    public String getID() {
        return "WARDEN";
    }

    @Override
    public void onClick(Player player, ItemStack item, boolean isRight, PlayerInteractEvent event) {
        if (!isRight) return;
        if (this.hasCooldown(player)) {
            this.sendCooldownMessage(player);
            event.setCancelled(true);
            return;
        }

        if (this.activeStates.containsKey(player.getUniqueId())) {
            super.sendLangMessage(player, "ALREADY_ACTIVE");
            return;
        }

        if (!this.tryUse(player, item)) return;
        item.subtract();

        WardenState state = activeStates.put(player.getUniqueId(), new WardenState(Instant.now().plusSeconds(timer_duration)));
        player.sendMessage(this.getLangMessage(player, "TIMER_STARTED",
                LocalPlaceholders.builder().add("<duration>", timer_duration + "")));
        WorldUtils.playSound(Sound.BLOCK_NOTE_BLOCK_BELL, player, 1, 1.5F);

        TaskUtil.runLater(getPlugin(), () -> {
            if (activeStates.containsKey(player.getUniqueId())) {
                this.completeAbilitySequence(player);
            }
        }, 20L * timer_duration);
    }

    @EventHandler(ignoreCancelled = true, priority = EventPriority.HIGHEST)
    public void hitPlayer(EntityDamageByEntityEvent event) {
        if (!(event.getDamager() instanceof Player damager) || !(event.getEntity() instanceof Player target)) {
            return;
        }

        if (UClansUtils.areSameClan(getServices(), damager, target)) {
            return;
        }

        if (!isInActiveState(damager)) {
            return;
        }

        WardenState state = activeStates.get(damager.getUniqueId());
        if (state == null) {
            return;
        }

        if (state.hasExpired()) {
            this.completeAbilitySequence(damager);
            return;
        }

        if (state.getWavesGenerated() >= max_waves) {
            return;
        }

        state.incrementHit();
        if (state.getHitCount() % hits_per_wave == 0) {
            ProfilePaperMetadata metadata = super.getServices().getProfileService().getServerMetadata(target.getUniqueId(), ProfilePaperMetadata.class);
            if (metadata.getTimeSinceLastTotem().toSeconds() <= 3) {
                WorldUtils.playSound(Sound.ENTITY_IRON_GOLEM_REPAIR, damager, 1F, 1);
                return;
            }

            this.generateSoundWave(damager, target, state);
            event.setDamage(0);
        }
        WorldUtils.playSound(Sound.ENTITY_WARDEN_HEARTBEAT, damager, 0.5F, 1.2F);
    }

    private void generateSoundWave(Player damager, Player target, WardenState state) {
        state.incrementWave();

        // Notify wave generation
        damager.sendMessage(this.getLangMessage(damager, "WAVE_GENERATED",
                LocalPlaceholders.builder()
                        .add("<wave_number>", state.getWavesGenerated() + "")
                        .add("<max_waves>", max_waves + "")));

        target.sendMessage(this.getLangMessage(target, "HIT_BY_WAVE", damager));
        WorldUtils.playSound(Sound.ENTITY_WARDEN_SONIC_BOOM, damager.getLocation(), 0.25F, 0.8F);
        WorldUtils.playSound(Sound.ENTITY_WARDEN_SONIC_CHARGE, target.getLocation(), 0.25F, 1.2F);

        // Generate particle animation
        this.createSonicWaveAnimation(damager.getLocation(), target);

        // Apply wave damage to target
        PlayerUtils.damage(super.getServices(), target, wave_damage, false, true, false, false, true);

        // Apply stun effect to enemies around target
        this.applyWaveStunEffect(target.getLocation(), damager);

        // Apply ally benefits around damager
        this.applyAllyBenefits(damager);
    }

    private void createSonicWaveAnimation(Location from, Entity targetEntity) {
        // Validate that the target entity is still valid
        if (targetEntity == null || !targetEntity.isValid()) {
            return;
        }

        AtomicInteger waveCount = new AtomicInteger(0);
        final Location originalFrom = from.clone();

        TaskUtil.runTimer(getPlugin(), new BukkitRunnable() {
            @Override
            public void run() {
                if (waveCount.get() >= 8) {
                    this.cancel();
                    return;
                }

                if (!targetEntity.isValid() || (targetEntity instanceof Player player && !player.isOnline())) {
                    this.cancel();
                    return;
                }

                Location currentTargetLocation = targetEntity.getLocation().add(0, 1, 0);

                Vector dynamicDirection = currentTargetLocation.toVector()
                        .subtract(originalFrom.toVector())
                        .normalize();
                double currentDistance = originalFrom.distance(currentTargetLocation);

                double progress = waveCount.getAndIncrement() / 8.0;

                Location particleLocation = originalFrom.clone()
                        .add(dynamicDirection.clone().multiply(currentDistance * progress));

                if (particleLocation.getWorld() == null) {
                    this.cancel();
                    return;
                }

                double intensity = Math.max(0.3, 1.0 - (currentDistance / 20.0));
                double spread = 0.1 + (progress * 0.15);

                particleLocation.getWorld().spawnParticle(
                        Particle.SONIC_BOOM,
                        particleLocation,
                        Math.max(1, (int)(2 * intensity)),
                        spread, spread, spread,
                        0
                );

                particleLocation.getWorld().spawnParticle(
                        Particle.EXPLOSION,
                        particleLocation,
                        Math.max(2, (int)(4 * intensity)),
                        0.2 + (progress * 0.2), 0.2 + (progress * 0.2), 0.2 + (progress * 0.2),
                        0.05 + (progress * 0.1)
                );

                if (progress > 0.25) {
                    particleLocation.getWorld().spawnParticle(
                            Particle.WITCH,
                            particleLocation,
                            1,
                            0.1, 0.1, 0.1,
                            0.02
                    );
                }

                if (progress < 0.9) { // Don't play on the very last wave to avoid sound overlap
                    float volume = Math.min(0.5F, (float)(0.1 + (progress * 0.4))); // Gradually increase volume
                    float pitch = (float)(0.9 + (progress * 0.3)); // Gradually increase pitch for urgency
                    WorldUtils.playSound(Sound.ENTITY_WARDEN_SONIC_CHARGE, particleLocation, volume, pitch);
                }

                // Special final wave effects
                if (progress > 0.85) {
                    // Add critical hit particles for dramatic final approach
                    particleLocation.getWorld().spawnParticle(
                            Particle.CRIT,
                            particleLocation,
                            (int)(progress * 8),
                            0.3, 0.3, 0.3,
                            0.2
                    );

                    // Final impact sound preparation
                    if (waveCount.get() == 8) {
                        WorldUtils.playSound(Sound.ENTITY_WARDEN_HURT, currentTargetLocation, 0.2F, 0.7F);
                        WorldUtils.playSound(Sound.ENTITY_GENERIC_EXPLODE, currentTargetLocation, 0.2F, 1.2F);

                        // Create final impact particle burst at target's current location
                        currentTargetLocation.getWorld().spawnParticle(
                                Particle.SONIC_BOOM,
                                currentTargetLocation,
                                5,
                                0.5, 0.5, 0.5,
                                0
                        );

                        currentTargetLocation.getWorld().spawnParticle(
                                Particle.EXPLOSION,
                                currentTargetLocation,
                                2,
                                0.3, 0.3, 0.3,
                                0
                        );
                    }
                }
            }
        }, 0L, 2L);
    }

    private void applyWaveStunEffect(Location epicenter, Player damager) {
        for (Player nearbyPlayer : epicenter.getNearbyPlayers(wave_radius)) {
            // Skip allies and the damager
            if (UClansUtils.areSameClan(getServices(), damager, nearbyPlayer) ||
                    nearbyPlayer.getUniqueId().equals(damager.getUniqueId())) {
                continue;
            }

            // Apply brief stun effect (slowness)
            nearbyPlayer.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 60, 2, false, true));
            nearbyPlayer.addPotionEffect(new PotionEffect(PotionEffectType.MINING_FATIGUE, 60, 1, false, true));
            super.applyGlobalAbilityCooldown(nearbyPlayer);

            WorldUtils.playSound(Sound.ENTITY_WARDEN_HURT, nearbyPlayer, 0.8F, 0.7F);
        }
    }

    private void applyAllyBenefits(Player damager) {
        List<Player> allies = UClansUtils.getNearbyClanPlayers(getServices(), damager, ally_search_radius);

        for (Player ally : allies) {
            // Apply configurable effects
            this.applyAllyEffect(ally, "REGENERATION", PotionEffectType.REGENERATION);
            this.applyAllyEffect(ally, "FAST_DIGGING", PotionEffectType.HASTE);
            this.applyAllyEffect(ally, "SPEED", PotionEffectType.SPEED);

            ally.sendMessage(this.getLangMessage(ally, "RECEIVED_ALLY_BENEFITS", damager));
            WorldUtils.playSound(Sound.ENTITY_EXPERIENCE_ORB_PICKUP, ally, 1, 1.5F);

            // Visual effect for allies
            WorldUtils.broadcastParticle(getServices(), Particle.HAPPY_VILLAGER,
                    ally.getLocation().add(0, 1, 0), 10, 0.5, 0.5, 0.5, 0);
        }
    }

    private void applyAllyEffect(Player player, String effectName, PotionEffectType effectType) {
        var effectSection = this.getConfigSection().getConfigurationSection("ALLY_EFFECTS." + effectName);
        if (effectSection == null) return;

        int level = effectSection.getInt("LEVEL", 0);
        int duration = effectSection.getInt("DURATION", 5);

        player.addPotionEffect(new PotionEffect(effectType, 20 * duration, level, false, true));
    }

    private void completeAbilitySequence(Player damager) {
        WardenState state = activeStates.remove(damager.getUniqueId());
        if (state == null) return;

        // Calculate total strength duration based on waves generated
        int totalStrengthDuration = state.getWavesGenerated() * strength_duration_per_wave;

        if (totalStrengthDuration > 0) {
            // Apply Strength III using the custom effect system
            CustomEffectSystem effectSystem = getServices().getSystemService().getSystem(CustomEffectSystem.class);
            effectSystem.addFakeStrengthEffect(damager, totalStrengthDuration);

            damager.sendMessage(this.getLangMessage(damager, "SEQUENCE_COMPLETED",
                    Duration.ofSeconds(totalStrengthDuration),
                    LocalPlaceholders.builder()
                            .add("<waves_generated>", state.getWavesGenerated() + "")
                            .add("<strength_duration>", totalStrengthDuration + "")));
        } else {
            damager.sendMessage(this.getLangMessage(damager, "NO_WAVES_GENERATED"));
        }

        this.applyCooldown(damager, true);

        // Final sound effect
        WorldUtils.playSound(Sound.ENTITY_WARDEN_ROAR, damager, 1, 0.9F);
        WorldUtils.broadcastParticle(getServices(), Particle.SONIC_BOOM,
                damager.getLocation().add(0, 1, 0), 5, 1, 1, 1, 0);
    }

    public boolean isInActiveState(Player player) {
        WardenState state = activeStates.get(player.getUniqueId());
        return state != null && !state.hasExpired();
    }

    @Getter
    @Setter
    @AllArgsConstructor
    private static class WardenState {
        private int hitCount = 0;
        private int wavesGenerated = 0;
        private final Instant expirationTime;

        public WardenState(Instant expirationTime) {
            this.expirationTime = expirationTime;
        }

        public void incrementHit() {
            this.hitCount++;
        }

        public void incrementWave() {
            this.wavesGenerated++;
        }

        public boolean hasExpired() {
            return Instant.now().isAfter(expirationTime);
        }
    }
}