package org.contrum.prisma.customitems.ability;

import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.persistence.PersistentDataType;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.utils.NBTUtil;
import org.contrum.prisma.utils.PlayerUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public abstract class LivedAbility extends Ability {

    public LivedAbility(PaperServices services) {
        super(services);
    }

    public int getMaxLives(){
        return this.getConfigSection().getInt("MAX_LIVES", 3);
    }

    public abstract boolean shouldDestroy();

    public void handleDeath(PlayerDeathEvent event) {
        if (PlayerUtils.isInDuel(event.getPlayer())) return;

        List<ItemStack> removedItems = new ArrayList<>();
        List<ItemStack> preserveItems = new ArrayList<>();
        for (ItemStack item : event.getDrops()){
            if (this.isItem(item)){
                //Get lives
                int lives = NBTUtil.getNBT(item.getItemMeta(), "lives", PersistentDataType.INTEGER, this.getMaxLives());
                int newLives = lives-1;

                removedItems.add(item);
                if (newLives >= 0){
                    ItemStack saved = item.clone();
                    List<String> lore = saved.getLore();

                    if (lore != null){
                        Collections.reverse(lore);
                        for (int i = 0; i < lore.size(); i++) {
                            String s = lore.get(i);
                            if (s.contains(String.valueOf(lives))){
                                s = s.replaceAll(String.valueOf(lives), String.valueOf(newLives));
                                lore.set(i, s);
                                break;
                            }
                        }

                        Collections.reverse(lore);
                        saved.setLore(lore);
                    }
                    NBTUtil.setNBT(saved, "lives", PersistentDataType.INTEGER, newLives);
                    preserveItems.add(saved);
                } else {
                    if (!this.shouldDestroy()) {
                        removedItems.remove(item);
                    }
                }
            }
        }

        event.getDrops().removeAll(removedItems);
        event.getItemsToKeep().addAll(preserveItems);
    }

    @EventHandler(priority = EventPriority.HIGHEST)
    public void death(PlayerDeathEvent event){
        this.handleDeath(event);
    }
}
