/*
 *  This file is part of the Apple Core project.
 *  Copyright (c) 2022-2024. Contrum Services
 *  Created by izLoki on 09/06/2024
 *  Website: contrum.org
 */

package org.contrum.prisma.customitems;

import lombok.Getter;
import org.bukkit.inventory.ItemStack;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customitems.ability.items.*;
import org.contrum.prisma.customitems.ability.items.legendarypickaxe.LegendaryPickaxeAbility;
import org.contrum.prisma.customitems.items.HackStick;
import org.contrum.prisma.utils.config.ConfigFile;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Getter
public class CustomItemsService {
    private final PaperServices services;

    private ConfigFile config;

    private final Map<String, ItemCustom> items = new ConcurrentHashMap<>();

    public CustomItemsService(PaperServices services) {
        this.services = services;

        this.config = new ConfigFile(services.getPlugin(), "items.yml");
    }

    public void loadItems() {
        //Load items
        List<ItemCustom> items = List.of(
                new ElytraDisablerAbility(services),
                new LifeStealerAbility(services),
                new InvisibilityAbility(services),
                new SwitcherAbility(services),
                new RageAbility(services),
                new HealerAbility(services),
                new MageAbility(services),
                new UpgradedNotchAbility(services),
                new UpgradedTotemAbility(services),
                new LegacySparkyAbility(services),
                new SparkyAbility(services),
                new LegacyPekkaSwordAbility(services),
                new PekkaSwordAbility(services),
                new AntiRunAbility(services),
                new LegendaryPickaxeAbility(services),
                new ScytheAbility(services),
                new MaceAbility(services),
                new EvokerAbility(services),
                new BatBiteAbility(services),
                new GloryOfGolemAbility(services),
                new WardenAbility(services),
                new HackStick(services)
        );

        items.forEach(this::registerItem);
    }

    public void registerItem(ItemCustom item) {
        this.items.put(item.getDisplayID(), item);
    }

    public void unregisterItem(ItemCustom item) {
        this.items.remove(item.getID());

        if (item instanceof ItemCustomListener a) a.unload();
    }

    public void unload() {
        for (ItemCustom i : this.items.values()) {
            if (i instanceof ItemCustomListener a) a.unload();
        }
    }

    public void reload() {
        this.config = new ConfigFile(services.getPlugin(), "items.yml");

        for (ItemCustom item : this.items.values()) {
            item.onReload();
        }
    }

    public <T extends ItemCustom> Set<T> getItemsByClass(Class<T> clazz) {
        return items.values().stream().filter(clazz::isInstance).map(clazz::cast).collect(Collectors.toSet());
    }

    public ItemStack getItemStack(String ID) {
        ItemCustom item = items.get(ID);

        if (item == null)
            return null;

        return item.getItemStack();
    }

    public ItemCustom getCustomItem(String ID) {
        return items.get(ID);
    }

    public <T extends ItemCustom> T getCustomItem(Class<T> clazz) {
        for (ItemCustom value : items.values()) {
            if (clazz.isInstance(value)) return clazz.cast(value);
        }

        return null;
    }

    public boolean isCustomItem(ItemStack item) {
        for (ItemCustom value : items.values()) {
            if (value.isItem(item)) return true;
        }

        return false;
    }

    public ItemCustom getItem(ItemStack item) {
        for (ItemCustom value : items.values()) {
            if (value.isItem(item)) return value;
        }

        return null;
    }
}
