package org.contrum.prisma.customitems.ability.items;

import com.github.retrooper.packetevents.PacketEvents;
import com.github.retrooper.packetevents.manager.player.PlayerManager;
import com.github.retrooper.packetevents.util.Vector3i;
import com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerBlockBreakAnimation;
import org.bukkit.*;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customitems.ability.LivedCooldownAbility;
import org.contrum.prisma.utils.WorldGuardUtils;
import org.contrum.prisma.utils.WorldUtils;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;

public class AntiRunAbility extends LivedCooldownAbility {
    private final List<Block> currentModifiedBlocks = new ArrayList<>();

    public AntiRunAbility(PaperServices services) {
        super(services);
    }

    @Override
    public void unload(){
        currentModifiedBlocks.forEach(b -> b.setType(Material.AIR));
    }

    @Override
    public String displayName(Player player) {
        return "AntiRun";
    }

    @Override
    public String getID() {
        return "ANTI_RUN";
    }

    @Override
    public Duration getCooldownDuration(Player player) {
        boolean isOnLowerMines = this.isOnLowerMines(player.getLocation());
        return Duration.ofSeconds(isOnLowerMines ? this.getConfigSection().getInt("LOW_MINES_COOLDOWN", 90) : this.getConfigSection().getInt("COOLDOWN", 60));
    }

    @Override
    public boolean shouldDestroy() {
        return true;
    }

    public boolean isOnLowerMines(Location location){
        return WorldGuardUtils.getRegionsName(location).stream().anyMatch(this.getConfigSection().getStringList("LOW_MINES_REGIONS")::contains);
    }

    @Override
    public void onClick(Player player, ItemStack item, boolean isRight, PlayerInteractEvent event) {
        if (!isRight) return;

        if (this.hasCooldown(player)){
            this.sendCooldownMessage(player);
            event.setCancelled(true);
            return;
        }

        List<String> prohibitedRegions = this.getConfigSection().getStringList("PROHIBITED_REGIONS");
        if (WorldGuardUtils.getRegionsName(player.getLocation()).stream().anyMatch(prohibitedRegions::contains)) {
            player.sendMessage(super.getLangMessage(player, "PROHIBITED_REGION"));
            WorldUtils.playBlockedSound(player);
            event.setCancelled(true);
            return;
        }

        if (!this.tryUse(player, item)) return;

        WorldUtils.playSound(Sound.BLOCK_IRON_DOOR_OPEN, player.getLocation(), 1, 0.8F);
        this.generateTemporalHollowSphere(player.getLocation(), 3, new Material[]{Material.PACKED_ICE, Material.LIGHT_BLUE_STAINED_GLASS, Material.BLUE_STAINED_GLASS});
    }

    public void generateTemporalHollowSphere(Location center, int radius, Material[] material) {
        List<Block> modifiedBlocks = new ArrayList<>();
        World world = center.getWorld();
        int centerX = center.getBlockX();
        int centerY = center.getBlockY();
        int centerZ = center.getBlockZ();

        for (int x = centerX - radius; x <= centerX + radius; x++) {
            for (int y = centerY - radius; y <= centerY + radius; y++) {
                for (int z = centerZ - radius; z <= centerZ + radius; z++) {
                    double distanceSquared = (x - centerX) * (x - centerX) + (y - centerY) * (y - centerY) + (z - centerZ) * (z - centerZ);

                    if (distanceSquared >= (radius - 0.5) * (radius - 0.5) && distanceSquared <= (radius + 0.5) * (radius + 0.5)) {
                        Location blockLoc = new Location(world, x, y, z);
                        Block block = blockLoc.getBlock();
                        if (!block.getType().isAir()) continue;
                        modifiedBlocks.add(block);

                        //Chose random material
                        block.setType(material[ThreadLocalRandom.current().nextInt(material.length)]);
                    }
                }
            }
        }

        this.currentModifiedBlocks.addAll(modifiedBlocks);

        new BukkitRunnable() {
            int runs;
            final int duration = isOnLowerMines(center) ? getConfigSection().getInt("LOW_MINES_DURATION", 8) : getConfigSection().getInt("DURATION", 10);
            double progress;

            @Override
            public void run() {
                if (runs++ >= duration){
                    modifiedBlocks.forEach(b ->{
                        b.getWorld().spawnParticle(Particle.BLOCK, b.getLocation(), 4, b.getBlockData());
                        b.getWorld().playSound(b.getLocation(), Sound.BLOCK_GLASS_BREAK, 0.4F, 1F);
                        b.setType(Material.AIR);
                    });
                    currentModifiedBlocks.removeAll(modifiedBlocks);
                    this.cancel();
                    return;
                }

                PlayerManager playerManager = PacketEvents.getAPI().getPlayerManager();
                modifiedBlocks.forEach(b ->{
                    WrapperPlayServerBlockBreakAnimation packet = new WrapperPlayServerBlockBreakAnimation(new Random().nextInt(2000), new Vector3i(b.getX(), b.getY(), b.getZ()), (byte) progress);
                    for (Player player : b.getLocation().getNearbyPlayers(30)) {
                        playerManager.sendPacket(player, packet);
                    }
                });

                progress+=((double) 10 /duration);
            }
        }.runTaskTimer(getPlugin(), 0L, 20L);
    }

    @Override
    public boolean allowSafeZoneUse() {
        return false;
    }
}