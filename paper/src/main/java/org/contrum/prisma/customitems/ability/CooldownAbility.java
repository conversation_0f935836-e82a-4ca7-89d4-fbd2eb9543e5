/*
 *  This file is part of the Apple Core project.
 *  Copyright (c) 2022-2024. Contrum Services
 *  Created by izLoki on 09/06/2024
 *  Website: contrum.org
 */

package org.contrum.prisma.customitems.ability;

import lombok.Getter;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.Listener;
import org.bukkit.inventory.ItemStack;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.PrismaCoreSetting;
import org.contrum.prisma.clients.PlayerClient;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.utils.Cooldown;
import org.contrum.prisma.utils.time.TimeUtils;

import java.time.Duration;

@Getter
public abstract class CooldownAbility extends Ability implements Listener {
    public CooldownAbility(PaperServices services) {
        super(services);
    }

    public Duration getCooldownDuration(Player player){
        return Duration.ofSeconds(this.getConfigSection().getInt("COOLDOWN", 60));
    }

    public String getCooldownID(){
        return "Ability-" + this.getDisplayID();
    }

    public boolean tryUse(Player player, ItemStack item, boolean globalCooldown) {
        boolean result = super.tryUse(player, item);
        if (result)
            this.applyCooldown(player, globalCooldown);
        return result;
    }

    @Override
    public boolean tryUse(Player player, ItemStack item) {
        boolean result = super.tryUse(player, item);
        if (result)
            this.applyCooldown(player, true);
        return result;
    }

    public void applyCooldown(Player player, boolean globalCooldown){
        ProfilePaperMetadata metadata = this.getServices().getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
        Duration cooldown = this.getCooldownDuration(player);

        if (!cooldown.isNegative()) {
            metadata.setCooldown(this.getCooldownID(), cooldown);

            //Apply item cooldown
            Material material = this.getMaterial();

            player.setCooldown(material, (int) (20L*cooldown.toSeconds()));

            PlayerClient client = this.getServices().getClientsService().getClient(player);
            client.applyItemCooldown(player, this.displayName(player), material, cooldown);
        }
        if (globalCooldown){
            this.applyGlobalAbilityCooldown(player);
        }
    }

    public void applyGlobalAbilityCooldown(Player player) {
        Duration duration = TimeUtils.parseDuration(PrismaCoreSetting.GLOBAL_ABILITY_COOLDOWN);
        this.applyGlobalAbilityCooldown(player, duration);
    }

    public void applyGlobalAbilityCooldown(Player player, Duration duration) {
        if (duration != null && duration.toSeconds() > 0) {
            ProfilePaperMetadata metadata = this.getServices().getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
            metadata.setCooldown("PA-Ability", duration);

            //Apply cooldown for all abilities
            for (CooldownAbility item : super.getServices().getCustomItemsService().getItemsByClass(CooldownAbility.class)) {
                Material material = item.getMaterial();
                int ticks = (int) (20L * duration.toSeconds());
                if (player.hasCooldown(material)) {
                    if (player.getCooldown(material) > ticks) continue;
                };

                player.setCooldown(material, ticks);
            }
        }
    }

    public boolean hasCooldown(Player player){
        ProfilePaperMetadata metadata = this.getServices().getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
        return metadata.hasCooldown(this.getCooldownID()) || metadata.hasCooldown("PA-Ability");
    }

    public void sendCooldownMessage(Player target){
        Cooldown globalCD = this.getGlobalCooldown(target);
        if (globalCD != null && !globalCD.hasExpired()){
            getServices().getTranslator().send(target, "ABILITY.GLOBAL_COOLDOWN", globalCD.getTimeLeftAsDuration());
            return;
        }

        Cooldown cooldown = this.getCooldownActive(target);
        if (cooldown != null && !cooldown.hasExpired()){
            getServices().getTranslator().send(target, "ABILITY.ABILITY_COOLDOWN", cooldown.getTimeLeftAsDuration());
        }
    }

    public Cooldown getGlobalCooldown(Player player){
        ProfilePaperMetadata metadata = this.getServices().getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
        return metadata.getCooldown("PA-Ability");
    }

    public Cooldown getCooldownActive(Player player){
        ProfilePaperMetadata metadata = this.getServices().getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
        return metadata.getCooldown(this.getCooldownID());
    }

    public String getCooldownFormatted(Player player, boolean detailed){
        ProfilePaperMetadata metadata = this.getServices().getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
        Cooldown cooldown = metadata.getCooldown(this.getCooldownID());
        return cooldown == null ? "0s" : cooldown.getTimeLeft(detailed);
    }
}
