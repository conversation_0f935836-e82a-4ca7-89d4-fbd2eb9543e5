package org.contrum.prisma.customitems;

import org.bukkit.event.Listener;
import org.bukkit.inventory.ItemStack;

public interface ItemCustom extends Listener {

    /**
     * Gets the unique identifier name of the item.
     *
     * @return the unique identifier name of the item
     */
    String getID();

    /**
     * Gets the display identifier of the item.
     *
     * @return the display identifier of the item
     */
    default String getDisplayID() {
        return this.getID();
    }

    /**
     * Gets the ItemStack representing the item.
     *
     * @return the ItemStack representing the item
     */
    ItemStack getItemStack();

    /**
     * Checks if the given ItemStack is this custom item.
     *
     * @param item the ItemStack to check
     * @return true if the given ItemStack is this custom item, false otherwise
     */
    boolean isItem(ItemStack item);

    void onReload();
}