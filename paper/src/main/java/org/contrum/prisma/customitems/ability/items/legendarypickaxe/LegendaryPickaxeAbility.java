package org.contrum.prisma.customitems.ability.items.legendarypickaxe;

import io.papermc.paper.event.entity.EntityEquipmentChangedEvent;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.event.player.PlayerDropItemEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.inventory.EquipmentSlot;
import org.bukkit.inventory.ItemStack;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.contrum.chorpu.TaskUtil;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customitems.CustomItemsService;
import org.contrum.prisma.customitems.ability.LivedAbility;
import org.contrum.prisma.events.BlockDropEvent;
import org.contrum.prisma.events.CurrencyAddEvent;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.currency.Currency;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.utils.NBTUtil;
import org.contrum.prisma.utils.PlayerUtils;
import org.contrum.prisma.utils.SafeTask;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class LegendaryPickaxeAbility extends LivedAbility {

    private final Set<UUID> pendingToGetPickaxe = ConcurrentHashMap.newKeySet();

    private final CoinsExcavatorBookItem coinsExcavatorBookItem;

    public LegendaryPickaxeAbility(PaperServices services) {
        super(services);

        this.coinsExcavatorBookItem = new CoinsExcavatorBookItem(this);
        services.getCustomItemsService().registerItem(coinsExcavatorBookItem);

        TaskUtil.runAsyncTimer(getPlugin(), () -> {
            //Check for pending items
            for (UUID uuid : pendingToGetPickaxe) {
                Player player = Bukkit.getPlayer(uuid);
                if (player == null) continue;

                ProfilePaperMetadata metadata = super.getServices().getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
                if (player.getInventory().firstEmpty() > -1 && !PlayerUtils.isInDuel(player) && metadata.isPendingLegendaryPickaxe()) {
                    //Give pickaxe
                    player.getInventory().addItem(this.getItemStack(uuid));
                    metadata.setPendingLegendaryPickaxe(false);
                    pendingToGetPickaxe.remove(uuid);
                    break;
                }
            }

            //Check for players
            for (Player player : Bukkit.getOnlinePlayers()) {
                if (player.hasPermission("core.legendarypickaxe.bypass")) continue;

                ItemStack[] contents = player.getInventory().getContents();

                for (int i = 0; i < contents.length; i++) {
                    ItemStack item = contents[i];

                    if (item == null) continue;

                    if (this.isItem(item)) {
                        UUID ownerUUID = this.getOwner(item);
                        //Check of legacy Legendary Pickaxes
                        if (ownerUUID == null) {
                            int slot = i;
                            TaskUtil.run(getPlugin(), () -> {
                                if (item.isSimilar(player.getInventory().getContents()[slot])) {
                                    this.setOwner(player, item);
                                }
                            });
                            continue;
                        };

                        //Check if player is the item owner
                        if (!player.getUniqueId().equals(ownerUUID)) {
                            int slot = i;
                            Profile profile = services.getProfileService().getOrLoadProfile(ownerUUID);

                            TaskUtil.run(getPlugin(), () -> {

                                if (item.isSimilar(player.getInventory().getContents()[slot])) {
                                    //Remove pickaxe from player's inventory
                                    player.getInventory().setItem(slot, null);

                                    //Give item back to owner
                                    Player owner = Bukkit.getPlayer(ownerUUID);
                                    //Save cache into owner's profile
                                    if (owner == null || owner.getInventory().firstEmpty() == -1) {
                                        ProfilePaperMetadata metadata = profile.getServerMetadata(services, ProfilePaperMetadata.class);
                                        metadata.setPendingLegendaryPickaxe(true);
                                        if (owner != null) {
                                            this.pendingToGetPickaxe.add(ownerUUID);
                                        }

                                        services.getProfileService().saveProfile(metadata.getProfile());
                                    } else {
                                        owner.getInventory().addItem(this.getItemStack(ownerUUID));
                                    }
                                }
                            });
                        }
                    }
                }
            }
        }, 20L, 40L);
    }

    @Override
    public void onReload() {
        super.onReload();
        coinsExcavatorBookItem.onReload();
    }

    @Override
    public void unload() {
        super.unload();
        coinsExcavatorBookItem.unload();
        super.getServices().getCustomItemsService().unregisterItem(coinsExcavatorBookItem);
    }

    public static ItemStack createNew(UUID owner, CustomItemsService services) {
        LegendaryPickaxeAbility instance = services.getCustomItem(LegendaryPickaxeAbility.class);
        return instance.getItemStack(owner);
    }

    private UUID getOwner(ItemStack item) {
        String owner = NBTUtil.getNBT(item.getItemMeta(), "owner", PersistentDataType.STRING, "");
        if (owner.isEmpty()) return null;
        return UUID.fromString(owner);
    }

    private void setOwner(Player player, ItemStack item) {
        NBTUtil.setNBT(item, "owner", PersistentDataType.STRING, player.getUniqueId().toString());
        this.updateItemLore(item);
    }

    public ItemStack getItemStack(UUID uuid) {
        ItemStack item = super.getItemStack();
        NBTUtil.setNBT(item, "owner", PersistentDataType.STRING, uuid.toString());
        NBTUtil.setNBT(item, "coinsExcavator", PersistentDataType.INTEGER, 0);

        this.updateItemLore(item);
        return item;
    }

    @Override
    public ItemStack getItemStack() {
        ItemStack item = super.getItemStack();
        NBTUtil.setNBT(item, "coinsExcavator", PersistentDataType.INTEGER, 0);

        this.updateItemLore(item);
        return item;
    }

    public void updateItemLore(ItemStack item) {
        int coinsExcavator = NBTUtil.getNBT(item.getItemMeta(), "coinsExcavator", PersistentDataType.INTEGER, 0);
        UUID owner = this.getOwner(item);

        LocalPlaceholders placeholders = LocalPlaceholders.builder()
                .add("<owner_name>", owner != null ? Bukkit.getOfflinePlayer(owner).getName() : "desconocido")
                .add("<coins_excavator>", coinsExcavator + "");

        ConfigurationSection section = this.getConfigSection();
        List<String> lore = section.getStringList("LORE").stream().map(s -> CC.translate(super.getServices().getLanguageHandler().getPlaceholderManager().apply(s, placeholders))).toList();
        item.setLore(lore);
        item.setType(Material.NETHERITE_PICKAXE);
    }

    public static int getCoinsExcavatorLevel(ItemStack item) {
        return NBTUtil.getNBT(item.getItemMeta(), "coinsExcavator", PersistentDataType.INTEGER, 0);
    }

    public void setCoinsExcavatorLevel(ItemStack item, int level) {
        NBTUtil.setNBT(item, "coinsExcavator", PersistentDataType.INTEGER, level);
        this.updateItemLore(item);
    }

    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    private void blockBreak(BlockDropEvent event){
        Player player = event.getPlayer();

        ItemStack item = player.getInventory().getItemInMainHand();
        if (this.isItem(item)){
            player.getWorld().playSound(player.getLocation(), Sound.BLOCK_AMETHYST_CLUSTER_HIT, 1, 1);
            event.setAmount(event.getAmount()*2);

            int coinsExcavatorLevel = getCoinsExcavatorLevel(item);
            if (coinsExcavatorLevel > 0) {
                // Sound
                int soundLevel = Math.min(5, coinsExcavatorLevel);
                float pitch = 1.0f - (float) (soundLevel - 1) / (5-1);
                player.getWorld().playSound(player.getLocation(), Sound.BLOCK_AMETHYST_BLOCK_CHIME, 1, pitch);

                ProfilePaperMetadata metadata = super.getServices().getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
                metadata.depositCurrency(super.getPlugin(), player, Currency.COINS, CurrencyAddEvent.Cause.COINS_EXCAVATOR, coinsExcavatorLevel);

            }
        }
    }

    @EventHandler
    public void coinsAdd(CurrencyAddEvent event){
        if (event.getCause().equals(CurrencyAddEvent.Cause.BLOCK_BREAK)){
            Player player = event.getPlayer();
            if (player == null) return;

            ItemStack item = player.getInventory().getItemInMainHand();
            if (this.isItem(item)){
                player.getWorld().playSound(player.getLocation(), Sound.BLOCK_AMETHYST_CLUSTER_HIT, 1, 1);
                int coinsExcavatorLevel = getCoinsExcavatorLevel(item);
                event.setAmount((event.getAmount()*2) + coinsExcavatorLevel);

                if (coinsExcavatorLevel > 0) {
                    // Sound
                    int soundLevel = Math.min(5, coinsExcavatorLevel);
                    float pitch = 1.0f - (float) (soundLevel - 1) / (5-1);
                    player.getWorld().playSound(player.getLocation(), Sound.BLOCK_AMETHYST_BLOCK_CHIME, 1, pitch);
                }
            }
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        ProfilePaperMetadata metadata = super.getServices().getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);

        if (metadata.isPendingLegendaryPickaxe() && !this.pendingToGetPickaxe.contains(player.getUniqueId())) {
            if (player.getInventory().firstEmpty() > -1 && !PlayerUtils.isInDuel(player)) {
                //Give pickaxe
                player.getInventory().addItem(this.getItemStack(player.getUniqueId()));
                metadata.setPendingLegendaryPickaxe(false);
            } else {
                this.pendingToGetPickaxe.add(player.getUniqueId());
            }
        }
    }

    @EventHandler
    public void dropItem(PlayerDropItemEvent event){
        if (this.isItem(event.getItemDrop().getItemStack())){
            event.setCancelled(true);
        }
    }

    @EventHandler
    public void equipmentChange(EntityEquipmentChangedEvent event) {
        if (event.getEntity() instanceof Player player) {

            for (Map.Entry<EquipmentSlot, EntityEquipmentChangedEvent.EquipmentChange> entry : event.getEquipmentChanges().entrySet()) {
                EquipmentSlot slot = entry.getKey();

                if (slot == EquipmentSlot.HAND) {
                    EntityEquipmentChangedEvent.EquipmentChange change = entry.getValue();
                    ItemStack previousItem = change.oldItem();
                    ItemStack newItem = change.newItem();
                    if (newItem != null && newItem.getType() == Material.NETHERITE_PICKAXE && this.isItem(newItem)) {
                        SafeTask.ensureSync(super.getPlugin(), () -> player.addPotionEffect(new PotionEffect(PotionEffectType.HASTE, Integer.MAX_VALUE, 3, false, false)));
                    } else if (previousItem != null && previousItem.getType() == Material.NETHERITE_PICKAXE && this.isItem(previousItem)) {
                        SafeTask.ensureSync(super.getPlugin(), () -> player.removePotionEffect(PotionEffectType.HASTE));
                    }
                }

            }
        }
    }

    @Override
    public void handleDeath(PlayerDeathEvent event) {
        List<ItemStack> pickaxes = new ArrayList<>();

        for (ItemStack item : event.getDrops()){
            if (this.isItem(item)){
                pickaxes.add(item);
            }
        }

        event.getDrops().removeAll(pickaxes);
        event.getItemsToKeep().addAll(pickaxes);
    }

    @Override
    public int getMaxLives() {
        return 100000;
    }

    @Override
    public boolean shouldDestroy() {
        return true;
    }

    @Override
    public boolean allowSafeZoneUse() {
        return false;
    }

    @Override
    public String displayName(Player player) {
        return "Pico Legendario";
    }

    @Override
    public String getID() {
        return "LEGENDARY_PICKAXE";
    }
}
