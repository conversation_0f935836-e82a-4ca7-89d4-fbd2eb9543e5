package org.contrum.prisma.customitems.ability.items;

import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customitems.ability.CooldownAbility;
import org.contrum.prisma.utils.WorldUtils;

import java.util.List;

public class LifeStealerAbility extends CooldownAbility {

    public double bleeding_damage = this.getConfigSection().getDouble("BLEEDING_DAMAGE", 5);
    public double bleeding_heal = this.getConfigSection().getDouble("BLEEDING_HEAL", 4);
    public List<String> effects = this.getConfigSection().getStringList("EFFECTS");

    public LifeStealerAbility(PaperServices services) {
        super(services);
    }

    @Override
    public boolean allowSafeZoneUse() {
        return false;
    }

    @Override
    public String displayName(Player player) {
        return "Robador De Vida";
    }

    @Override
    public String getID() {
        return "LIFE_STEALER";
    }

    @Override
    public void onReload() {
        effects = this.getConfigSection().getStringList("EFFECTS");
        bleeding_damage = this.getConfigSection().getDouble("BLEEDING_DAMAGE", 4);
    }

    @Override
    public void hitToPlayer(Player damager, Player player, ItemStack item, EntityDamageByEntityEvent event) {
        if (player.hasMetadata("lifeStealer")) {
            damager.sendMessage(this.getLangMessage(damager, "ALREADY_MARKED", player));
            event.setCancelled(true);
            return;
        }

        if (this.hasCooldown(damager)) {
            this.sendCooldownMessage(damager);
            event.setCancelled(true);
            return;
        }

        if (!super.callAbilityEffectEvent(damager, player, true)) {
            return;
        }

        if (!tryUse(damager, item)) return;
        item.subtract();

        //Steal effects
        for (PotionEffect effect : player.getActivePotionEffects()) {
            if (effects.contains(effect.getType().getName())) {
                player.removePotionEffect(effect.getType());
                damager.addPotionEffect(effect);
            }
        }

        damager.sendMessage(this.getLangMessage(damager, "TAGGED_TO", player));
        player.sendMessage(this.getLangMessage(player, "TAGGED_BY", damager));
        WorldUtils.playSound(Sound.ENTITY_GENERIC_DRINK, player.getLocation(), 1, 0);
        WorldUtils.broadcastParticle(getServices(), Particle.FALLING_DUST, player.getLocation().add(0,1,0), 15, 1, 1, 1, Material.REDSTONE_BLOCK.createBlockData());

        player.setMetadata("lifeStealer", new FixedMetadataValue(getPlugin(), true));
        TaskUtil.runTimer(getPlugin(), new BukkitRunnable() {

            int runs = 0;

            @Override
            public void run() {
                if (runs++ >= 6 || !player.isOnline() || !player.hasMetadata("lifeStealer")) {
                    cancel();
                    player.removeMetadata("lifeStealer", getPlugin());
                    return;
                }
                player.damage(bleeding_damage);
                damager.setHealth(Math.min(damager.getMaxHealth(), damager.getHealth()+bleeding_heal));
                WorldUtils.broadcastParticle(getServices(), Particle.FALLING_DUST, player.getLocation().add(0,1,0), 15, 1, 1, 1, Material.REDSTONE_BLOCK.createBlockData());
                WorldUtils.broadcastParticle(getServices(), Particle.HEART, damager.getLocation(), 3, 1, 1, 1, 1);
            }
        }, 0, 25L);
    }

    @EventHandler
    public void onDeath(PlayerDeathEvent event) {
        if (event.getPlayer().hasMetadata("lifeStealer"))
            event.getPlayer().removeMetadata("lifeStealer", getPlugin());
    }
}