package org.contrum.prisma.customitems.items;

import com.github.retrooper.packetevents.PacketEvents;
import com.github.retrooper.packetevents.event.PacketListener;
import com.github.retrooper.packetevents.event.PacketListenerPriority;
import com.github.retrooper.packetevents.event.PacketSendEvent;
import com.github.retrooper.packetevents.protocol.packettype.PacketType;
import com.github.retrooper.packetevents.protocol.world.states.WrappedBlockState;
import com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerBlockChange;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import io.github.retrooper.packetevents.util.SpigotConversionUtil;
import io.papermc.paper.event.player.PlayerTrackEntityEvent;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.*;
import org.bukkit.block.data.BlockData;
import org.bukkit.block.data.Waterlogged;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.contrum.chorpu.TaskUtil;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.actionbar.bars.impl.GlobalDynamicActionBar;
import org.contrum.prisma.customitems.ItemCustomListener;
import org.contrum.prisma.systems.impl.playerattributes.PlayerAttributesSystem;
import org.contrum.prisma.utils.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public class HackStick extends ItemCustomListener implements Listener {

    private static double REACH_MODE_REACH = 8;
    private static double REACH_MODE_PITCH = 10;
    private static double REACH_MODE_YAW = 10;

    private Map<UUID, List<Location>> cachedCobwebBlocks = new ConcurrentHashMap<>();

    public HackStick(PaperServices services) {
        super(services);

        //Register packet events listener
        PacketEvents.getAPI().getEventManager().registerListener(this.listener(), PacketListenerPriority.NORMAL);

        //Register actionbar
        services.getActionBarService().registerGlobalActionBar(new GlobalDynamicActionBar("HACK_STICK_ACTIONBAR", (p) -> {
            StickData stickData = this.getStickData(p);
            if (stickData == null) return null;

            String mode = stickData.isActive() ? stickData.getMode().getName() : "Desactivado";
            return CC.translate("&cModo de &4h&4&ka&4ck: &d&n" + mode);
        }, 12));
    }

    @Override
    public String getID() {
        return "HACK_STICK";
    }

    @Override
    public ItemStack getItemStack() {
        ItemBuilder1_20 builder = new ItemBuilder1_20(Material.STICK);
        builder.addNBT(this.getID(), PersistentDataType.BOOLEAN, true);
        this.setData(builder.build(), new StickData(Mode.REACH));
        return builder.build();
    }

    @Override
    public boolean isItem(ItemStack item) {
        return item != null && item.getType().equals(Material.STICK) && item.hasItemMeta() && NBTUtil.getNBT(item.getItemMeta(), this.getID(), PersistentDataType.BOOLEAN, false);
    }

    private void setData(ItemStack item, StickData data) {
        if (item == null) return;

        //Update item lore
        ItemBuilder1_20 builder = new ItemBuilder1_20(item);
        if (data.isActive()) {
            builder.name("&eModo: &c" + data.getMode().getName());
            builder.setLore(data.getMode().getDescription());
        } else {
            builder.name("&eModo: &cDesactivado");
            builder.setLore("&7Desactivado!");
        }

        NBTUtil.setNBT(item, "data", PersistentDataType.STRING, data.serialize());
    }

    public StickData getStickData(ItemStack item) {
        return new StickData(NBTUtil.getNBT(item.getItemMeta(), "data", PersistentDataType.STRING, ""));
    }

    public StickData getStickData(Player player) {
        if (!player.getInventory().contains(Material.STICK)) return null;

        StickData stick = null;
        for (ItemStack i : player.getInventory()) {
            //Remove stick from inventory if it already exists on player's inventory or if player doesn't have permissions to use it.
            if ((stick != null || !player.hasPermission("core.superstick")) && this.isItem(i)) {
                player.getInventory().removeItem(i);
                break;
            }

            if (this.isItem(i)) {
                stick = getStickData(i);
            }
        }
        return stick;
    }

    public boolean isDeadCauseOfStick(Player player, Player killer) {
        if (getStickData(killer) != null) return true;

        for (Player other : player.getLocation().getNearbyPlayers(8)) {
            if (getStickData(other) != null) return true;
        }

        return false;
    }

    public void toggleMode(Player player, ItemStack item) {
        StickData stickData = this.getStickData(item);

        this.onDisable(player, stickData.getMode());

        int ordinal = stickData.getMode().ordinal() + 1;
        stickData.mode = Mode.values().length <= ordinal ? Mode.values()[0] : Mode.values()[ordinal];

        this.onEnable(player, stickData.getMode());
        WorldUtils.playSound(Sound.BLOCK_NOTE_BLOCK_PLING, player);
        setData(item, stickData);
    }

    public void toggleStick(Player player, ItemStack item) {
        StickData stickData = this.getStickData(item);

        if (stickData.isActive()) {
            stickData.setActive(false);
            this.onDisable(player, stickData.getMode());
        } else {
            stickData.setActive(true);
            this.onEnable(player, stickData.getMode());
        }
        setData(item, stickData);
    }

    public void onEnable(Player player, Mode mode) {
        if (mode.equals(Mode.ANTI_COBWEB) || mode.isGod()) {
            //Replace nearby cobwebs with corals async
            player.setMetadata("anticobweb", new FixedMetadataValue(getPlugin(), true));
            TaskUtil.runAsync(getPlugin(), () -> {
                LocationUtils.runInNearbyBlocksByType(
                        player.getLocation(),
                        80,
                        Material.COBWEB, (cobweb) -> {
                            //Send coral packet
                            if (!player.hasMetadata("anticobweb")) return;
                            WrapperPlayServerBlockChange change = new WrapperPlayServerBlockChange(SpigotConversionUtil.fromBukkitLocation(cobweb.getLocation()).getPosition().toVector3i(), SpigotConversionUtil.fromBukkitBlockData(getFakeCobwebBlockData()).getGlobalId());
                            PacketEvents.getAPI().getPlayerManager().sendPacketSilently(player, change);
                            List<Location> locations = cachedCobwebBlocks.computeIfAbsent(player.getUniqueId(), c -> new ArrayList<>());
                            locations.add(cobweb.getLocation());
                        });
            });
        }

        if (mode.equals(Mode.INVISIBILITY)) {
            player.addPotionEffect(new PotionEffect(PotionEffectType.GLOWING, Integer.MAX_VALUE, 0, false, false));
            player.setMetadata("stick-invisibility", new FixedMetadataValue(getPlugin(), true));
            for (Player target : Bukkit.getOnlinePlayers()) {
                target.hidePlayer(getPlugin(), player);
            }
        }
    }

    public void onDisable(Player player, Mode mode) {
        if (mode.equals(Mode.ANTI_COBWEB) || mode.isGod()) {
            //Replace cached corals with cobwebs async
            player.removeMetadata("anticobweb", getPlugin());
            TaskUtil.runAsync(getPlugin(), () -> {
                List<Location> locations = cachedCobwebBlocks.remove(player.getUniqueId());
                if (locations == null) return;

                for (Location location : locations) {
                    //Send cobweb packet
                    WrapperPlayServerBlockChange change = new WrapperPlayServerBlockChange(SpigotConversionUtil.fromBukkitLocation(location).getPosition().toVector3i(), SpigotConversionUtil.fromBukkitBlockData(Material.COBWEB.createBlockData()).getGlobalId());
                    PacketEvents.getAPI().getPlayerManager().sendPacketSilently(player, change);
                }
            });
        }

        if (mode.equals(Mode.INVISIBILITY)) {
            player.removePotionEffect(PotionEffectType.GLOWING);
            player.removeMetadata("stick-invisibility", getPlugin());
            for (Player target : Bukkit.getOnlinePlayers()) {
                target.showPlayer(getPlugin(), player);
            }
        }
    }

    @EventHandler
    public void stickToggle(PlayerInteractEvent event) {
        //Toggle stick
        Player player = event.getPlayer();
        if (event.getAction().isLeftClick()) {

            ItemStack item = event.getItem();
            if (item == null || !this.isItem(item)) {
                return;
            }

            StickData data = this.getStickData(item);

            if (player.isSneaking())
                this.toggleStick(player, item);
            else if (data.isActive())
                this.toggleMode(player, item);
            else {
                WorldUtils.playSound(Sound.ENTITY_VILLAGER_NO, player);
                player.sendMessage(CC.translate("&cEl item está desactivado"));
            }
        }
    }

    public PacketListener listener() {
        return new PacketListener() {

            @Override
            public void onPacketSend(PacketSendEvent event) {
                if (event.getPacketType() != PacketType.Play.Server.BLOCK_CHANGE)
                    return;

                WrapperPlayServerBlockChange packet = new WrapperPlayServerBlockChange(event);
                Player player = (Player) event.getPlayer();

                if (player != null && (packet.getBlockState().getGlobalId() == SpigotConversionUtil.fromBukkitBlockData(Material.COBWEB.createBlockData()).getGlobalId() || packet.getBlockId() == SpigotConversionUtil.fromBukkitBlockData(Material.COBWEB.createBlockData()).getGlobalId())) {
                    StickData stickData = getStickData(player);
                    if (stickData == null || !stickData.isActive()|| (!stickData.getMode().equals(Mode.ANTI_COBWEB) && !stickData.getMode().isGod())) return;

                    cachedCobwebBlocks.computeIfAbsent(player.getUniqueId(), c -> new ArrayList<>()).add(new Location(player.getWorld(), packet.getBlockPosition().getX(), packet.getBlockPosition().getY(), packet.getBlockPosition().getZ()));
                    WrappedBlockState blockState = SpigotConversionUtil.fromBukkitBlockData(getFakeCobwebBlockData());
                    packet.setBlockID(blockState.getGlobalId());
                    packet.setBlockState(blockState);
                }
            }
        };
    }

    @EventHandler
        public void reachMode(PlayerInteractEvent event) {
        //Reach mode
        Player player = event.getPlayer();
        if (event.getAction().isLeftClick()) {
            ItemStack item = event.getItem();
            if (this.isItem(item)) {
                return;
            }

            StickData stickData = this.getStickData(player);
            if (stickData == null || !stickData.isActive() || (!stickData.getMode().equals(Mode.REACH) && !stickData.getMode().isGod())) return;

            //Apply reach and aim assist :D
            super.getServices().getSystemService().getSystem(PlayerAttributesSystem.class).applyReachAndAssist(event, REACH_MODE_REACH, REACH_MODE_PITCH, REACH_MODE_YAW, true, false);
        }
    }

    @EventHandler
    public void comboMode(EntityDamageByEntityEvent event) {
        //Combo mode
        if (event.getEntity() instanceof Player player && event.getDamager() instanceof Player damager) {

            StickData stickData = this.getStickData(damager);
            if (stickData == null || !stickData.isActive() || (!stickData.getMode().equals(Mode.COMBO) && !stickData.getMode().isGod())) {
                //Reset maximum no-damage ticks
                if (player.getNoDamageTicks() == 0)
                    player.setMaximumNoDamageTicks(20);
                return;
            }

            player.setMaximumNoDamageTicks(0);
            player.setNoDamageTicks(0);
        }
    }

    @EventHandler
    public void onTrack(PlayerTrackEntityEvent event) {
        if (event.getEntity() instanceof Player tracked && tracked.hasMetadata("stick-invisibility")) {
            event.setCancelled(true);
        }
    }

    @EventHandler
    public void onJoin(PlayerJoinEvent event) {
        for (Player p : Bukkit.getOnlinePlayers()) {
            if (p.hasMetadata("stick-invisibility")) {
                event.getPlayer().hidePlayer(getPlugin(), p);
            }
        }
    }

    @EventHandler
    public void playerDamage(EntityDamageByEntityEvent event) {
        if (event.getEntity() instanceof Player player) {
            if (player.hasMetadata("explosion")) {
                event.setCancelled(true);
                return;
            }

            if (event.getDamager() instanceof Player damager) {
                StickData data = this.getStickData(damager);
                if (data == null || !data.isActive() || !data.getMode().equals(Mode.EXPLOSION) || PlayerUtils.isInDuel(damager)) return;

                if (PlayerUtils.isNPC(player)) return;

                event.setCancelled(true);
                player.setMetadata("explosion", new FixedMetadataValue(getPlugin(), true));
                WorldUtils.playSound(Sound.ENTITY_TNT_PRIMED, player.getLocation());

                //Explosion animation runnable
                player.setGravity(false);
                player.addPotionEffect(new PotionEffect(PotionEffectType.GLOWING, Integer.MAX_VALUE, 0, false, false));
                player.setHealth(player.getMaxHealth());
                TaskUtil.runTimer(getPlugin(), new BukkitRunnable() {

                    int tick = 0;

                    @Override
                    public void run() {
                        if (!player.isOnline()) {
                            cancel();
                            return;
                        }

                        if (tick++ >= 20*7) {
                            Location loc = player.getLocation();
                            WorldUtils.playSound(Sound.ENTITY_GENERIC_EXPLODE, loc);
                            loc.getWorld().spawnParticle(Particle.EXPLOSION, loc, 8);
                            loc.getWorld().spawnParticle(Particle.EXPLOSION, loc, 8);
                            loc.getWorld().spawnParticle(Particle.EXPLOSION, loc, 8);

                            //Teleport to spawn
                            player.setGravity(true);
                            player.removePotionEffect(PotionEffectType.GLOWING);
                            player.teleport(getServices().getSpawnService().getSpawn("world"));
                            player.removeMetadata("explosion", getPlugin());
                            player.sendTitle(CC.translate("&eFuiste trolleado hasta morir. &a¡No perdiste nada!"), CC.translate("&c¡Pronto saldrás en el video! ;3"), 10, 120, 20);
                            player.sendMessage(CC.translate("&7&l&m-------------------------------------------"));
                            player.sendMessage(CC.translate("&eFuiste &4asesinado&e por un Partner&a por un palito TROLL, no te preocupes <3 &c¡&c&nNo perdiste nada&c! &eTus estadisticas no fueron modificadas ¡Pronto saldrás en el video!"));
                            player.sendMessage(CC.translate("&7&l&m-------------------------------------------"));
                            cancel();
                            return;
                        }

                        Location loc = player.getLocation();

                        loc.add(0,0.03,0);
                        loc.setYaw(loc.getYaw()+5);
                        loc.getWorld().spawnParticle(Particle.END_ROD, loc, 5, 0,0,0,0.05);
                        if (player.getWorld().getBlockAt(loc.clone().add(0,1,0)).getType() == Material.AIR)
                            player.teleport(loc);
                    }
                }, 0,1);
            }
        }
    }

    @EventHandler
    public void move(PlayerMoveEvent event) {
        if (event.getPlayer().hasMetadata("explosion")) {
            event.setCancelled(true);
        }
    }

    @EventHandler
    public void onQuit(PlayerQuitEvent event) {
        if (event.getPlayer().hasMetadata("explosion")) {
            event.getPlayer().setGravity(true);
            event.getPlayer().removePotionEffect(PotionEffectType.GLOWING);
            event.getPlayer().removeMetadata("explosion", getPlugin());
        }
    }

    @EventHandler(priority = EventPriority.HIGHEST)
    public void death(PlayerDeathEvent event) { //Return inventory to players killed by hack stick
        Player player = event.getPlayer();
        Player killer = player.getKiller();
        if (killer != null) {
            if (!this.isDeadCauseOfStick(player, killer)) return;

            if (!PlayerUtils.isInDuel(killer)) {
                event.setKeepInventory(true);
                event.setKeepLevel(true);

                player.setStatistic(Statistic.DEATHS, Math.max(killer.getStatistic(Statistic.DEATHS)-1,0));
                killer.setStatistic(Statistic.PLAYER_KILLS, Math.max(killer.getStatistic(Statistic.PLAYER_KILLS)-1,0));
                event.getDrops().clear();
            }
            TaskUtil.runLater(getPlugin(), () -> {
                player.sendTitle(CC.translate("&eFuiste trolleado hasta morir. &a¡No perdiste nada!"), CC.translate("&c¡Pronto saldrás en el video! ;3"), 10, 120, 20);
                player.sendMessage(CC.translate("&7&l&m-------------------------------------------"));
                player.sendMessage(CC.translate("&eFuiste &4asesinado&e por un Partner&a por un palito TROLL, no te preocupes <3 &c¡&c&nNo perdiste nada&c! &eTus estadisticas no fueron modificadas ¡Pronto saldrás en el video!"));
                player.sendMessage(CC.translate("&7&l&m-------------------------------------------"));
            }, 20L);
        }
    }

    private BlockData getFakeCobwebBlockData() {
        Waterlogged data = (Waterlogged) Material.DEAD_FIRE_CORAL.createBlockData();
        data.setWaterlogged(false);

        return data;
    }

    @Getter
    @Setter
    public static class StickData {
        private Mode mode;
        private boolean active = true;

        public StickData(Mode mode) {
            this.mode = mode;
        }

        public StickData(String string) {
            JsonObject jsonObject = JsonParser.parseString(string).getAsJsonObject();

            this.mode = Mode.valueOf(jsonObject.get("mode").getAsString());
            this.active = jsonObject.get("active").getAsBoolean();
        }

        public String serialize() {
            JsonObject jsonObject = new JsonObject();

            jsonObject.addProperty("mode", mode.name());
            jsonObject.addProperty("active", active);

            return jsonObject.toString();
        }
    }

    @Getter
    public enum Mode {
        REACH("Reach", List.of("&7Aumenta el alcance y la amplitud", "&7de tus ataques, permitiéndote golpear", "&7enemigos desde más lejos y en una zona", "&7más amplia!")),
        COMBO("Combo", List.of("&7Desata una serie rápida de golpes", "&7consecutivos, aumentando la velocidad", "&7de tu ataque!")),
        ANTI_COBWEB("AntiCobweb", List.of("&7Convierte las telarañas cercanas", "&7en corales solo visibles para ti,", "&7permitiéndote atravesarlas sin", "&7restricciones!")),
        EXPLOSION("Explosion", List.of("&7Golpea a tus enemigos y conviertelos", "&7en una tnt flotante,", "&7causando su muerte instantanea!")),
        INVISIBILITY("Invisibilidad", List.of("&7Vuélvete invisible al instante", "&7para escapar del campo de visión", "&7de tus enemigos!")),
        GOD("God", List.of("&7Accede a una combinación de", "&7todas las habilidades al mismo tiempo!"), true);

        private final String name;
        private final List<String> description;
        private boolean god = false;

        private Mode(String name, List<String> description) {
            this.name = name;
            this.description = description;
        }

        private Mode(String name, List<String> description, boolean god) {
            this.name = name;
            this.description = description;
            this.god = god;
        }
    }
}