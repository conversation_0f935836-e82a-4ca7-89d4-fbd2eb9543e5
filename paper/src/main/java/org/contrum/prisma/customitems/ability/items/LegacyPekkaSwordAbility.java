package org.contrum.prisma.customitems.ability.items;

import lombok.Getter;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.clients.PlayerClient;
import org.contrum.prisma.customitems.ability.CooldownAbility;
import org.contrum.prisma.customitems.ability.LivedCooldownAbility;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.systems.impl.CustomEffectSystem;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.prisma.utils.UClansUtils;

import java.time.Duration;
import java.time.Instant;
import java.util.*;

public class LegacyPekkaSwordAbility extends LivedCooldownAbility {

    private double search_range = this.getConfigSection().getDouble("SEARCH_RANGE", 6);
    private double damage = this.getConfigSection().getDouble("DAMAGE", 12);
    private double enemy_effect_duration_reduction = this.getConfigSection().getDouble("ENEMY_EFFECT_DURATION_REDUCTION", 0.7);
    private long totem_protection_time = this.getConfigSection().getLong("TOTEM_TIME_PROTECTION_MILLIS", 3000);
    private long sword_protection_time = this.getConfigSection().getLong("SWORD_PROTECTION_TIME_MILLIS", 10000);
    private List<LimitedEffect> effects;

    private HashMap<UUID, Instant> lastUseMap = new HashMap<>();

    public LegacyPekkaSwordAbility(PaperServices services) {
        super(services);
        this.onReload();
    }

    @Override
    public void onReload() {
        this.search_range = this.getConfigSection().getDouble("SEARCH_RANGE", 6);
        this.damage = this.getConfigSection().getDouble("DAMAGE", 12);
        this.enemy_effect_duration_reduction = this.getConfigSection().getDouble("ENEMY_EFFECT_DURATION_REDUCTION", 0.7);
        this.totem_protection_time = this.getConfigSection().getLong("TOTEM_TIME_PROTECTION_MILLIS", 3000);
        this.sword_protection_time = this.getConfigSection().getLong("SWORD_PROTECTION_TIME_MILLIS", 10000);

        ConfigurationSection section = this.getConfigSection().getConfigurationSection("GOOD_EFFECTS");
        if (section == null) return;
        this.effects = section.getValues(false)
                .keySet().stream()
                .map(k -> super.getConfigSection().getConfigurationSection("GOOD_EFFECTS").getConfigurationSection(k))
                .filter(Objects::nonNull)
                .map(LimitedEffect::new)
                .toList();
    }

    @Override
    public boolean shouldDestroy() {
        return true;
    }

    @Override
    public boolean allowSafeZoneUse() {
        return false;
    }

    @Override
    public String displayName(Player player) {
        return "Espada PEKKA";
    }

    @Override
    public String getID() {
        return "PEKKA_SWORD";
    }

    @Override
    public String getDisplayID() {
        return "LEGACY_PEKKA_SWORD";
    }

    @Override
    public void hitToPlayer(Player damager, Player target, ItemStack item, EntityDamageByEntityEvent event) {
        if (UClansUtils.areSameClan(getServices(), damager, target)) {
            event.setCancelled(true);
            return;
        }

        if (this.hasCooldown(damager)) {
            this.sendCooldownMessage(damager);
            event.setCancelled(true);
            return;
        }

        ProfilePaperMetadata targetMetadata = getServices().getProfileService().getServerMetadata(target.getUniqueId(), ProfilePaperMetadata.class);
        if (targetMetadata.getTimeSinceLastTotem().toMillis() <= totem_protection_time) {
            WorldUtils.playSound(Sound.ENTITY_IRON_GOLEM_REPAIR, damager, 1, 0.8F);
            damager.sendMessage(this.getLangMessage(damager, "TOTEM_COOLDOWN"));
            event.setCancelled(true);
            return;
        }

        //Last use cooldown
        Duration lastUseTime = this.getLastUseTime(target);
        if (!lastUseTime.isNegative() && lastUseTime.toMillis() <= sword_protection_time) {
            WorldUtils.playSound(Sound.ENTITY_IRON_GOLEM_REPAIR, damager, 1, 0.8F);
            damager.sendMessage(this.getLangMessage(damager, "SWORD_COOLDOWN"));
            event.setCancelled(true);
            return;
        }

        if (this.tryUse(damager, item, false)) {
            //Steal effects
            for (PotionEffect effect : target.getActivePotionEffects()) {
                LimitedEffect effectLimits = this.getEffectLimits(effect.getType());
                if (effectLimits == null)
                    continue;

                //Steal strength III effect
                if (effect.getType().equals(PotionEffectType.STRENGTH)) {
                    CustomEffectSystem system = super.getServices().getSystemService().getSystem(CustomEffectSystem.class);
                    Duration remaining = system.getStrengthEffectRemainingTime(target);
                    if (!remaining.isNegative()) {
                        system.addFakeStrengthEffect(damager, this.parseDuration((int) remaining.toSeconds(), effectLimits.getMaxDuration(2), 2));
                        continue;
                    }
                }

                int max = effectLimits.getMaxAmplifier();
                int newAmplifier = Math.min(effect.getAmplifier() + 1, max);
                PotionEffect clonedEffect = new PotionEffect(effect.getType(), this.parseDuration(effect.getDuration(), effectLimits.getMaxDuration(newAmplifier), 2), newAmplifier);
                damager.addPotionEffect(effect);
                damager.addPotionEffect(clonedEffect);
            }

            //Reduce nearby enemies effects
            for (Player enemy : UClansUtils.getNearbyEnemyPlayers(super.getServices(), damager, search_range)) {
                for (PotionEffect effect : enemy.getActivePotionEffects()) {
                    if (this.getEffectLimits(effect.getType()) == null)
                        continue;

                    //Reduce strength III effect
                    if (effect.getType().equals(PotionEffectType.STRENGTH)) {
                        CustomEffectSystem system = super.getServices().getSystemService().getSystem(CustomEffectSystem.class);
                        Duration remaining = system.getStrengthEffectRemainingTime(enemy);
                        if (!remaining.isNegative()) {
                            enemy.removePotionEffect(effect.getType());
                            system.addFakeStrengthEffect(enemy, (int) (remaining.toSeconds() * enemy_effect_duration_reduction));
                            continue;
                        }
                    }

                    enemy.removePotionEffect(effect.getType());
                    PotionEffect clonedEffect = new PotionEffect(effect.getType(), (int) (effect.getDuration() * enemy_effect_duration_reduction), effect.getAmplifier());
                    enemy.addPotionEffect(clonedEffect);
                }
            }

            /*
            for (Player ally : UClansUtils.getNearbyClanPlayers(super.getServices(), damager, search_range)) {
                for (PotionEffect effect : ally.getActivePotionEffects()) {
                    LimitedEffect effectLimits = this.getEffectLimits(effect.getType());
                    if (effectLimits == null)
                        continue;

                    //Increase strength III effect
                    if (effect.getType().equals(PotionEffectType.STRENGTH)) {
                        CustomEffectSystem system = super.getServices().getSystemService().getSystem(CustomEffectSystem.class);
                        Duration remaining = system.getStrengthEffectRemainingTime(ally);
                        if (!remaining.isNegative()) {
                            system.addFakeStrengthEffect(ally, this.parseDuration((int) remaining.toSeconds(), effectLimits.getMaxDuration(2), 1.20));
                            continue;
                        }
                    }

                    PotionEffect clonedEffect = new PotionEffect(effect.getType(), this.parseDuration(effect.getDuration(), effectLimits.getMaxDuration(effect.getAmplifier()), 1.20), effect.getAmplifier());
                    ally.addPotionEffect(clonedEffect);
                }
            }

             */
            this.setLastUse(target);

            //Set static damage
            event.setDamage(EntityDamageEvent.DamageModifier.BASE, damage);

            //Apply and remove global cooldowns
            this.applyGlobalAbilityCooldown(target);
            this.resetCooldowns(damager);

            //Play sounds
            WorldUtils.playSound(Sound.BLOCK_PISTON_CONTRACT, damager.getLocation(), 2,0);
            TaskUtil.runLater(super.getPlugin(), () -> {
                WorldUtils.playSound(Sound.BLOCK_ANVIL_PLACE, damager.getLocation(), 0.5F, 0.3F);
                WorldUtils.broadcastParticle(getServices(), Particle.WITCH, target.getLocation().clone().add(0,1,0), 20, 1,1,1, 1);
            }, 8L);
        } else {
            event.setCancelled(true);
        }
    }

    public void setLastUse(Player player) {
        this.lastUseMap.put(player.getUniqueId(), Instant.now());
    }

    public Duration getLastUseTime(Player player) {
        Instant lastUse = this.lastUseMap.get(player.getUniqueId());
        if (lastUse == null) return Duration.ofSeconds(-1);
        return Duration.between(lastUse, Instant.now());
    }

    public void resetCooldowns(Player player) {
        ProfilePaperMetadata metadata = this.getServices().getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);

        //Remove cooldown for all abilities
        for (CooldownAbility item : super.getServices().getCustomItemsService().getItemsByClass(CooldownAbility.class)) {
            if (item instanceof LivedCooldownAbility) continue;
            Material material = item.getMaterial();
            metadata.removeCooldown(item.getCooldownID());
            player.setCooldown(material, 0);

            PlayerClient client = getServices().getClientsService().getClient(player);
            client.removeItemCooldown(player, material);
        }
    }

    private int parseDuration(int original, int maxDuration, double multiplier) {
        return Math.min((int) (original*multiplier), maxDuration);
    }

    private LimitedEffect getEffectLimits(PotionEffectType type) {
        for (LimitedEffect effect : this.effects) {
            if (effect.getType().equals(type)) return effect;
        }

        return null;
    }

    private static class LimitedEffect {
        @Getter
        private final PotionEffectType type;
        private final HashMap<Integer, Integer> amplifierLimitMap = new HashMap<>();
        private int max_amplifier = 0;

        public LimitedEffect(ConfigurationSection section) {
            type = PotionEffectType.getByName(section.getName());

            ConfigurationSection levels = section.getConfigurationSection("LEVELS");
            if (levels == null) {
                return;
            }

            for (Map.Entry<String, Object> entry : levels.getValues(false).entrySet()) {
                String levelString = entry.getKey();
                Object maxDurationObject = entry.getValue();

                try {
                    int level = Integer.parseInt(levelString);
                    int max_duration = (int) maxDurationObject;

                    if (level > max_amplifier) max_amplifier = level;

                    amplifierLimitMap.put(level, max_duration);
                } catch (Exception e) {
                    e.printStackTrace();
                    continue;
                }
            }
        }

        public int getMaxAmplifier() {
            return max_amplifier;
        }

        public int getMaxDuration(int level) {
            return amplifierLimitMap.getOrDefault(level, 0);
        }
    }
}
