package org.contrum.prisma.customitems.ability.items;

import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.AreaEffectCloud;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customitems.ability.CooldownAbility;
import org.contrum.prisma.systems.impl.CustomEffectSystem;
import org.contrum.prisma.utils.PlayerUtils;
import org.contrum.prisma.utils.UClansUtils;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class RageAbility extends CooldownAbility {

    private int duration = this.getConfigSection().getInt("DURATION", 15);
    private int allies_search_range = this.getConfigSection().getInt("ALLIES_SEARCH_RANGE", 10);
    private int cloud_damage_radius = this.getConfigSection().getInt("CLOUD_DAMAGE_RADIUS", 6);
    private double cloud_damage = this.getConfigSection().getDouble("CLOUD_DAMAGE", 1.5);
    private long cloud_damage_delay = this.getConfigSection().getLong("CLOUD_DAMAGE_DELAY", 2000);
    private double radius_reduce_per_hit = this.getConfigSection().getDouble("RADIUS_REDUCE_PER_HIT", 0.25);

    private final Map<UUID, Instant> damageTime = new HashMap<>();

    public RageAbility(PaperServices services) {
        super(services);
    }

    @Override
    public void onReload() {
        this.duration = this.getConfigSection().getInt("DURATION", 15);
        this.allies_search_range = this.getConfigSection().getInt("ALLIES_SEARCH_RANGE", 10);
        this.cloud_damage_radius = this.getConfigSection().getInt("CLOUD_DAMAGE_RADIUS", 6);
        this.cloud_damage = this.getConfigSection().getDouble("CLOUD_DAMAGE", 1.5);
        this.cloud_damage_delay = this.getConfigSection().getLong("CLOUD_DAMAGE_DELAY", 2000);
        this.radius_reduce_per_hit = this.getConfigSection().getDouble("RADIUS_REDUCE_PER_HIT", 0.25);
    }

    @Override
    public boolean allowSafeZoneUse() {
        return false;
    }

    @Override
    public String displayName(Player player) {
        return "Furia";
    }

    @Override
    public String getID() {
        return "RAGE";
    }

    @Override
    public void onClick(Player player, ItemStack item, boolean isRight, PlayerInteractEvent event) {
        if (!isRight) return;
        if (this.hasCooldown(player)){
            this.sendCooldownMessage(player);
            event.setCancelled(true);
            return;
        }
        if (!this.tryUse(player, item)) return;

        //Use item
        item.subtract();
        CustomEffectSystem system = getServices().getSystemService().getSystem(CustomEffectSystem.class);
        List<Player> clanPlayers = UClansUtils.getNearbyClanPlayers(getServices(), player, allies_search_range);

        player.sendMessage(this.getLangMessage(player, "USE", LocalPlaceholders.builder().add("<allies>", clanPlayers.size() + "")));
        //Play sound
        TaskUtil.runTimer(getPlugin(), new BukkitRunnable() {

            int runs = 0;

            @Override
            public void run() {
                if (runs++ == 3) {
                    WorldUtils.playSound(Sound.ENTITY_GENERIC_DRINK, player.getLocation(), 1, 0.9F);
                    cancel();
                    return;
                }

                WorldUtils.playSound(Sound.ENTITY_GENERIC_DRINK, player.getLocation(), 1, 0.8F);
            }
        }, 0, 5L);
        system.addFakeStrengthEffect(player, duration);
        for (Player clanPlayer : clanPlayers) {
            clanPlayer.sendMessage(this.getLangMessage(clanPlayer, "RECEIVED_BY_ALLY"));
            WorldUtils.playSound(Sound.ENTITY_PLAYER_LEVELUP, clanPlayer, 1, 0.8F);
            system.addFakeStrengthEffect(clanPlayer, duration);
        }

        //Spawn visual damage cloud
        AreaEffectCloud entity = (AreaEffectCloud) player.getWorld().spawnEntity(player.getLocation(), EntityType.AREA_EFFECT_CLOUD);
        entity.setMetadata(this.getID(), new FixedMetadataValue(getPlugin(), player.getUniqueId().toString()));
        entity.setRadius(cloud_damage_radius);
        entity.setDuration(20*duration);
        entity.addCustomEffect(PotionEffectType.INSTANT_DAMAGE.createEffect(1,2), true);
        entity.setParticle(Particle.WITCH);
        entity.setReapplicationDelay(10);
        entity.setRadiusPerTick(0);
        entity.setRadiusOnUse(0);
    }

    @EventHandler(ignoreCancelled = true)
    public void damage(EntityDamageByEntityEvent event) {
        if (event.getDamager() instanceof AreaEffectCloud cloud && cloud.hasMetadata(this.getID())) {
            if (!(event.getEntity() instanceof Player player)) {
                event.setCancelled(true);
                return;
            }

            UUID uuid = UUID.fromString(cloud.getMetadata(this.getID()).getFirst().asString());
            event.setCancelled(true);
            if (!player.getUniqueId().equals(uuid) && !UClansUtils.areSameClan(getServices(), uuid, player.getUniqueId())) {
                Instant instant = damageTime.computeIfAbsent(player.getUniqueId(), f -> Instant.ofEpochMilli(0));
                if (Duration.between(instant, Instant.now()).toMillis() >= cloud_damage_delay) {
                    cloud.setRadius((float) (cloud.getRadius()-radius_reduce_per_hit));
                    PlayerUtils.damage(player, cloud_damage, false, false, true, false);
                    damageTime.put(player.getUniqueId(), Instant.now());
                }
            }
        }
    }
}
