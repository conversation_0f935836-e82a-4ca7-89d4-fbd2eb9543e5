package org.contrum.prisma.customitems.ability.items;

import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customitems.ability.LivedCooldownAbility;
import org.contrum.prisma.systems.impl.CustomEffectSystem;
import org.contrum.prisma.utils.PlayerUtils;
import org.contrum.prisma.utils.WorldUtils;

public class ScytheAbility extends LivedCooldownAbility {

    private double base_damage = this.getConfigSection().getDouble("BASE_DAMAGE", 12);
    private double healing_amount = this.getConfigSection().getDouble("HEALING_AMOUNT", 5);
    private int debuff_duration = this.getConfigSection().getInt("DEBUFF_DURATION", 5);

    public ScytheAbility(PaperServices services) {
        super(services);
    }

    @Override
    public void onReload() {
        base_damage = this.getConfigSection().getDouble("BASE_DAMAGE", 12);
        healing_amount = this.getConfigSection().getDouble("HEALING_AMOUNT", 5);
        debuff_duration = this.getConfigSection().getInt("DEBUFF_DURATION", 5);
    }

    @Override
    public boolean shouldDestroy() {
        return true;
    }

    @Override
    public boolean allowSafeZoneUse() {
        return false;
    }

    @Override
    public String displayName(Player player) {
        return "Guadaña";
    }

    @Override
    public String getID() {
        return "SCYTHE";
    }

    @Override
    public void hitToPlayer(Player player, Player target, ItemStack item, EntityDamageByEntityEvent event) {
        if (this.hasCooldown(player)) {
            this.sendCooldownMessage(player);
            event.setCancelled(true);
            return;
        }

        if (this.tryUse(player, item, false)) {
            WorldUtils.playSound(Sound.ENTITY_ALLAY_DEATH, player.getLocation(), 0.8F, 0);
            WorldUtils.broadcastParticle(getServices(), Particle.WITCH, player.getLocation(), 10, 1, 1, 1, 1);

            PlayerUtils.heal(player, healing_amount);
            event.setDamage(this.base_damage);
            getServices().getSystemService().getSystem(CustomEffectSystem.class).addFakeWeaknessEffect(target, debuff_duration, 0);
            target.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 20 * debuff_duration, 1));
        }
    }
}