package org.contrum.prisma.customitems.ability.items;

import org.bukkit.*;
import org.bukkit.entity.Player;
import org.bukkit.entity.Projectile;
import org.bukkit.event.entity.ProjectileHitEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.projectiles.ProjectileSource;
import org.bukkit.util.Vector;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customitems.ability.CooldownAbility;
import org.contrum.prisma.utils.PlayerUtils;
import org.contrum.prisma.utils.UClansUtils;
import org.contrum.prisma.utils.WorldUtils;

import java.util.UUID;

public class MageAbility extends CooldownAbility {

    private int explosion_drag_range = this.getConfigSection().getInt("EXPLOSION_DRAG_RANGE", 10);
    private int explosion_drag_force = this.getConfigSection().getInt("EXPLOSION_DRAG_FORCE", 5);
    private double explosion_damage_min = this.getConfigSection().getDouble("EXPLOSION_DAMAGE_MIN", 4);
    private double explosion_damage_max = this.getConfigSection().getDouble("EXPLOSION_DAMAGE_MAX", 12);
    private int fire_duration = this.getConfigSection().getInt("FIRE_DURATION", 4);
    private int fire_damage_wait = this.getConfigSection().getInt("FIRE_DAMAGE_WAIT", 1);
    private double fire_damage = this.getConfigSection().getDouble("FIRE_DAMAGE", 4);

    public MageAbility(PaperServices services) {
        super(services);
    }

    @Override
    public void onReload() {
        explosion_drag_range = this.getConfigSection().getInt("EXPLOSION_DRAG_RANGE", 10);
        explosion_drag_force = this.getConfigSection().getInt("EXPLOSION_DRAG_FORCE", 5);
        explosion_damage_min = this.getConfigSection().getDouble("EXPLOSION_DAMAGE_MIN", 4);
        explosion_damage_max = this.getConfigSection().getDouble("EXPLOSION_DAMAGE_MAX", 12);
        fire_duration = this.getConfigSection().getInt("FIRE_DURATION", 4);
        fire_damage_wait = this.getConfigSection().getInt("FIRE_DAMAGE_WAIT", 20);
        fire_damage = this.getConfigSection().getDouble("FIRE_DAMAGE", 4);
    }

    @Override
    public boolean allowSafeZoneUse() {
        return false;
    }

    @Override
    public String displayName(Player player) {
        return "Mago";
    }

    @Override
    public String getID() {
        return "MAGE";
    }

    @Override
    public void onClick(Player player, ItemStack item, boolean isRight, PlayerInteractEvent event) {
        if (super.hasCooldown(player)) {
            super.sendCooldownMessage(player);
            event.setCancelled(true);
            return;
        }

        if (super.tryUse(player, item)) {
            item.subtract();

            WorldUtils.playSound(Sound.ITEM_FIRECHARGE_USE, player.getLocation());
            player.launchProjectile(org.bukkit.entity.Snowball.class, player.getLocation().getDirection().multiply(1.5), (snowball -> {
                snowball.setMetadata(this.getID(), new FixedMetadataValue(getPlugin(), null));

                snowball.setItem(new ItemStack(Material.FIRE_CHARGE));
            }));

            event.setCancelled(true);
        }
    }

    @Override
    public void onProjectileImpact(ProjectileSource thrower, Projectile projectile, ProjectileHitEvent event) {
        Location location = projectile.getLocation();
        Player throwerPlayer = thrower instanceof Player ? (Player) thrower : null;
        UUID throwerUUID = throwerPlayer != null ? throwerPlayer.getUniqueId() : UUID.randomUUID();

        Bukkit.getOnlinePlayers().forEach(player -> {
            if (UClansUtils.areSameClan(getServices(), player.getUniqueId(), throwerUUID) || !PlayerUtils.isDamageable(player)) return;

            if (!super.callAbilityEffectEvent(throwerPlayer, player, true)) {
                return;
            }

            Location playerLocation = player.getLocation();
            if (player.getWorld() != location.getWorld()) return;
            double distance = playerLocation.distance(location);
            if (distance > explosion_drag_range) return;

            double distanceFactor = distance / explosion_drag_range;
            double force = explosion_drag_force * distanceFactor;

            Vector direction = location.toVector().subtract(playerLocation.toVector());
            Vector velocity = direction.normalize().multiply(force);

            double maxVertical = 1.0;
            velocity.setY(Math.min(maxVertical, Math.max(-maxVertical, velocity.getY())));

            if (player.isGliding()) player.setGliding(false);
            player.setVelocity(velocity);

            PlayerUtils.setCustomFire(getServices(), player, 20 * fire_duration, fire_damage_wait, fire_damage, false);
            double damage = Math.max(explosion_damage_min, explosion_damage_max * (1 - distanceFactor));
            PlayerUtils.damage(player, damage, false, true, false);
        });

        WorldUtils.playSound(Sound.ENTITY_GENERIC_EXPLODE, location);
        WorldUtils.broadcastParticle(getServices(), Particle.EXPLOSION, location, 8, 0.5, 0.5, 0.5, 0);
    }
}