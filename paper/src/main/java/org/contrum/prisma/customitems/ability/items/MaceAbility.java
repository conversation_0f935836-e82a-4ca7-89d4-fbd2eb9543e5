package org.contrum.prisma.customitems.ability.items;

import org.bukkit.*;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customitems.ability.LivedCooldownAbility;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.utils.PlayerUtils;
import org.contrum.prisma.utils.UClansUtils;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.prisma.utils.time.TimeUtils;

import java.time.Duration;

public class MaceAbility extends LivedCooldownAbility {
    public static final String METADATA_BLEEDING = "mace_bleeding";
    public static final String METADATA_BUFFED = "mace_buffed";

    private Duration stun_duration = TimeUtils.parseDuration(this.getConfigSection().getString("STUN_DURATION", "5s"));
    private Duration buff_duration = TimeUtils.parseDuration(this.getConfigSection().getString("BUFF_DURATION", "10s"));
    private Duration bleeding_interval = TimeUtils.parseDuration(this.getConfigSection().getString("BLEEDING_INTERVAL", "2s"));
    private Duration bleeding_start_delay = TimeUtils.parseDuration(this.getConfigSection().getString("BLEEDING_START_DELAY", "3s"));
    private int bleeding_ticks = this.getConfigSection().getInt("BLEEDING_TICKS", 4);
    private double buffCriticalMultiplier = this.getConfigSection().getDouble("BUFF_CRITICAL_MULTIPLIER", 1.1);
    private double buffDamageMultiplier = this.getConfigSection().getDouble("BUFF_DAMAGE_MULTIPLIER", 1.05);
    private double bleedingDamage = this.getConfigSection().getDouble("BLEEDING_DAMAGE", 8);
    private double passiveDamage = this.getConfigSection().getDouble("PASSIVE_DAMAGE", 4);

    public MaceAbility(PaperServices services) {
        super(services);
    }

    @Override
    public String displayName(Player player) {
        return "Martillo Bruto";
    }

    @Override
    public String getID() {
        return "MARTILLO";
    }

    @Override
    public boolean allowSafeZoneUse() {
        return false;
    }

    @Override
    public boolean shouldDestroy() {
        return true;
    }

    @Override
    public void onReload() {
        this.stun_duration = TimeUtils.parseDuration(this.getConfigSection().getString("STUN_DURATION", "5s"));
        this.buff_duration = TimeUtils.parseDuration(this.getConfigSection().getString("BUFF_DURATION", "10s"));
        this.bleeding_interval = TimeUtils.parseDuration(this.getConfigSection().getString("BLEEDING_INTERVAL", "2s"));
        this.bleeding_start_delay = TimeUtils.parseDuration(this.getConfigSection().getString("BLEEDING_START_DELAY", "3s"));
        this.bleeding_ticks = this.getConfigSection().getInt("BLEEDING_TICKS", 4);
        this.buffCriticalMultiplier = this.getConfigSection().getDouble("BUFF_CRITICAL_MULTIPLIER", 1.1);
        this.buffDamageMultiplier = this.getConfigSection().getDouble("BUFF_DAMAGE_MULTIPLIER", 1.05);
        this.bleedingDamage = this.getConfigSection().getDouble("BLEEDING_DAMAGE", 8);
        this.passiveDamage = this.getConfigSection().getDouble("PASSIVE_DAMAGE", 4);
    }

    public void buffPlayer(Player player, boolean buff) {
        if (!buff) {
            player.removeMetadata(METADATA_BUFFED, super.getPlugin());
            return;
        }

        player.setMetadata(METADATA_BUFFED, new FixedMetadataValue(super.getPlugin(), true));
        // Animation
        new BukkitRunnable() {
            @Override
            public void run() {
                if (!isBuffed(player)) {
                    cancel();
                    return;
                }

                Location location = player.getLocation().add(0,0.25,0);
                WorldUtils.broadcastParticle(MaceAbility.super.getServices(), Particle.DUST, location, 30, 2, 0, 2, 0, new Particle.DustOptions(Color.fromRGB(204, 102, 0), 1F));

                for (Player nearbyPlayer : location.getNearbyPlayers(2)) {
                    if (nearbyPlayer.equals(player) || UClansUtils.areSameClan(getServices(), nearbyPlayer.getUniqueId(), player.getUniqueId())) continue;

                    PlayerUtils.damage(nearbyPlayer, passiveDamage, false, false, true, false);
                }
            }
        }.runTaskTimer(super.getPlugin(), 10, 30);
    }

    public boolean isBuffed(Player player) {
        return player.hasMetadata(METADATA_BUFFED);
    }

    public void setBleeding(Player player, boolean bleeding) {
        if (bleeding)
            player.setMetadata(METADATA_BLEEDING, new FixedMetadataValue(super.getPlugin(), true));
        else
            player.removeMetadata(METADATA_BLEEDING, super.getPlugin());
    }

    public boolean isBleeding(Player player) {
        return player.isOnline() && player.hasMetadata(METADATA_BLEEDING);
    }

    @Override
    public void hitToPlayer(Player damager, Player target, ItemStack item, EntityDamageByEntityEvent event) {
        if (UClansUtils.areSameClan(getServices(), damager, target)) {
            event.setCancelled(true);
            return;
        }

        if (this.hasCooldown(damager)) {
            this.sendCooldownMessage(damager);
            event.setCancelled(true);
            return;
        }

        if (!super.callAbilityEffectEvent(damager, target, true)) {
            return;
        }

        ProfilePaperMetadata targetMetadata = super.getServices().getProfileService().getServerMetadata(target.getUniqueId(), ProfilePaperMetadata.class);
        if (targetMetadata.getTimeSinceLastTotem().toSeconds() < 3) {
            WorldUtils.playSound(Sound.ENTITY_IRON_GOLEM_REPAIR, damager, 1F, 1);
            return;
        }

        if (!this.tryUse(damager, item)) return;

        event.setDamage(0);
        WorldUtils.playSound(Sound.ENTITY_IRON_GOLEM_REPAIR, target.getLocation(), 4F, 0.5F);
        WorldUtils.playSound(Sound.BLOCK_ANVIL_FALL, target.getLocation(), 2F, 0.1F);
        WorldUtils.strikeLightningEffect(getServices(), target.getLocation());
        target.addPotionEffect(new PotionEffect(PotionEffectType.NAUSEA, 20 * 8, 0));
        super.applyGlobalAbilityCooldown(target, stun_duration);

        super.sendLangMessage(target, "STUNNED_BY", damager);
        super.sendLangMessage(damager, "STUNNED_TARGET", target);

        // Buff
        this.buffPlayer(damager, true);
        TaskUtil.runLater(super.getPlugin(), () -> {
            this.buffPlayer(damager, false);
        }, buff_duration.toMillis() / 50);

        // Bleeding effect
        this.setBleeding(target, true);
        new BukkitRunnable() {
            int remainingHits = bleeding_ticks;

            @Override
            public void run() {
                if (remainingHits-- < 0 || !isBleeding(target)) {
                    cancel();
                    setBleeding(target, false);
                    return;
                }

                PlayerUtils.damage(target, bleedingDamage, false, false, true, false);
                WorldUtils.broadcastParticle(MaceAbility.super.getServices(), Particle.BLOCK, target.getEyeLocation(), 20, Material.GOLD_BLOCK.createBlockData());
            }
        }.runTaskTimer(super.getPlugin(), bleeding_start_delay.toMillis() / 50, bleeding_interval.toMillis() / 50);
    }

    @EventHandler
    public void entityDamage(EntityDamageByEntityEvent event) {
        if (event.getEntity() instanceof Player player && event.getDamager() instanceof Player damager) {
            if (isBuffed(damager)) {
                //reduce resis and critical hit
                if (!event.isCritical()) {
                    event.setDamage(event.getDamage() * buffCriticalMultiplier);
                    player.getWorld().playSound(player.getLocation(), Sound.ENTITY_PLAYER_ATTACK_CRIT, 1, 1);
                    player.getWorld().spawnParticle(Particle.ENCHANTED_HIT, player.getLocation(), 60, 0.5, 1, 0.5, 0.2);
                    player.getWorld().spawnParticle(Particle.CRIT, player.getLocation(), 60, 0.5, 1, 0.5, 0.2);
                }
                event.setDamage(event.getDamage() * buffDamageMultiplier);
            }
        }
    }

    @EventHandler
    public void onPlayerDeath(PlayerDeathEvent event) {
        Player player = event.getEntity();
        if (isBleeding(player)) {
            this.setBleeding(player, false);
        }

        if (isBuffed(player)) {
            this.buffPlayer(player, false);
        }
    }
}
