package org.contrum.prisma.customitems.ability.items.legendarypickaxe;

import org.bukkit.*;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryAction;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.player.PlayerSwapHandItemsEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.scheduler.BukkitRunnable;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customitems.ability.Ability;
import org.contrum.prisma.utils.NBTUtil;
import org.contrum.prisma.utils.PlayerUtils;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.tritosa.placeholder.LocalPlaceholders;
import org.jetbrains.annotations.Nullable;

public class CoinsExcavatorBookItem extends Ability implements Listener {

    private final LegendaryPickaxeAbility legendaryPickaxeAbility;

    public CoinsExcavatorBookItem(LegendaryPickaxeAbility legendaryPickaxeAbility) {
        super(legendaryPickaxeAbility.getServices());

        this.legendaryPickaxeAbility = legendaryPickaxeAbility;
    }

    @Override
    public String displayName(Player player) {
        return "Coins Excavator";
    }

    @Override
    public boolean allowSafeZoneUse() {
        return true;
    }

    @Override
    public String getID() {
        return "COINS_EXCAVATOR_BOOK";
    }

    public boolean canUpgrade(Player player, ItemStack item) {
        return legendaryPickaxeAbility.isItem(item) && !this.isUpgrading(item) && !this.isUpgrading(player);
    }

    public boolean isUpgrading(ItemStack item) {
        return item != null && item.hasItemMeta() && NBTUtil.getNBT(item.getItemMeta(), "excavatorLevelUpgrading", PersistentDataType.BOOLEAN, false);
    }

    public boolean isUpgrading(Player player) {
        return player.hasMetadata("excavatorLevelUpgrading");
    }

    public void setUpgrading(Player player, boolean upgrading) {
        if (!upgrading) {
            player.removeMetadata("excavatorLevelUpgrading", getServices().getPlugin());
        } else {
            player.setMetadata("excavatorLevelUpgrading", new FixedMetadataValue(getServices().getPlugin(), true));
        }
    }

    public void setUpgrading(ItemStack item, boolean upgrading) {
        NBTUtil.setNBT(item, "excavatorLevelUpgrading", PersistentDataType.BOOLEAN, upgrading);
    }

    public void startUpgrade(Player player, ItemStack item) {
        this.setUpgrading(item, true);
        this.setUpgrading(player, true);

        int newLevel = LegendaryPickaxeAbility.getCoinsExcavatorLevel(item) + 1;
        legendaryPickaxeAbility.setCoinsExcavatorLevel(item, newLevel);
        TaskUtil.runTimer(getServices().getPlugin(), new BukkitRunnable() {

            int ticks = 0;
            int runs = 0;
            int nextInterval = 16;

            @Override
            public void run() {
                ticks++;

                if (!player.isOnline()) {
                    cancel();
                    return;
                }

                if (runs >= 25) {
                    setUpgrading(player, false);
                    for (@Nullable ItemStack content : player.getInventory().getContents()) {
                        if (content != null && isUpgrading(content)) {
                            setUpgrading(content, false);
                            announceUpgrade(player, LegendaryPickaxeAbility.getCoinsExcavatorLevel(content));
                            legendaryPickaxeAbility.updateItemLore(content);
                        }
                    }
                    cancel();
                    return;
                }

                if (ticks >= nextInterval || (ticks == 1 && nextInterval == 16)) {
                    ticks = 0;
                    runs++;

                    Material newMaterial = item.getType() == Material.NETHERITE_PICKAXE
                            ? Material.GOLDEN_PICKAXE
                            : Material.NETHERITE_PICKAXE;
                    item.setType(newMaterial);

                    WorldUtils.playSound(Sound.BLOCK_NOTE_BLOCK_PLING, player);

                    nextInterval = Math.max(1, nextInterval - 1);
                }
            }
        }, 0L, 1L);
    }

    private void announceUpgrade(Player player, int newLevel) {
        PlayerUtils.spawnWinFirework(player, Color.RED, FireworkEffect.Type.STAR);
        WorldUtils.playSound(Sound.ENTITY_ENDER_DRAGON_GROWL, player);
        WorldUtils.playSound(Sound.ENTITY_PLAYER_LEVELUP);
        Bukkit.broadcast(super.getLangMessage(player, "UPGRADE_BROADCAST", player, LocalPlaceholders.builder().add("<new_level>", newLevel + "")));
    }

    @EventHandler(ignoreCancelled = true, priority = EventPriority.HIGH)
    public void onPlayerUseAbility(InventoryClickEvent event) {
        Player player = (Player) event.getWhoClicked();

        ItemStack cursor = player.getItemOnCursor();
        ItemStack item = event.getCurrentItem();

        if (event.getClick() == ClickType.NUMBER_KEY) {
            int slot = event.getHotbarButton();

            ItemStack slotItem = player.getInventory().getItem(slot);
            if (this.isUpgrading(slotItem) || this.isUpgrading(item))
                event.setCancelled(true);

            return;
        }

        if (this.isUpgrading(item)) {
            event.setCancelled(true);
            return;
        }

        if (this.isItem(cursor) && this.canUpgrade(player, item)) {
            event.setCancelled(true);
            player.setItemOnCursor(null);

            // Update animation :d
            this.startUpgrade(player, item);
        }
    }

    @EventHandler
    public void playerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        if (this.isUpgrading(player)) {
            this.setUpgrading(player, false);
            for (@Nullable ItemStack content : player.getInventory().getContents()) {
                if (content != null && this.isUpgrading(content)) {
                    this.setUpgrading(content, false);
                    this.announceUpgrade(player, LegendaryPickaxeAbility.getCoinsExcavatorLevel(content));
                    legendaryPickaxeAbility.updateItemLore(content);
                }
            }
        }
    }

    @EventHandler(priority = EventPriority.HIGH, ignoreCancelled = true)
    public void swapItems(PlayerSwapHandItemsEvent event) {
        ItemStack item = event.getMainHandItem();
        ItemStack offHandItem = event.getOffHandItem();

        if (this.isUpgrading(item) || this.isUpgrading(offHandItem)) {
            event.setCancelled(true);
        }
    }
}
