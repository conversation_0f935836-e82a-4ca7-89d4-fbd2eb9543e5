package org.contrum.prisma.customitems.ability.items;

import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityResurrectEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customitems.ability.Ability;

public class UpgradedTotemAbility extends Ability implements Listener {

    public UpgradedTotemAbility(PaperServices services) {
        super(services);
    }

    @Override
    public String displayName(Player player) {
        return "Totem Mejorado";
    }

    @Override
    public String getID() {
        return "UPGRADED_TOTEM";
    }

    @Override
    public boolean allowSafeZoneUse() {
        return true;
    }

    @Override
    public Material getMaterial() {
        return Material.TOTEM_OF_UNDYING;
    }

    @EventHandler
    public void UseTotem(EntityResurrectEvent event) {
        if (event.getEntity() instanceof Player player) {
            if (event.getHand() == null) return;
            ItemStack item = player.getInventory().getItem(event.getHand());

            if (!this.isItem(item) || !this.tryUse(player, item)) return;

            TaskUtil.runLater(getPlugin(), () -> {
                player.addPotionEffect(new PotionEffect(PotionEffectType.ABSORPTION, 20 * 15, 3));
                player.addPotionEffect(new PotionEffect(PotionEffectType.RESISTANCE, 20 * 15, 0));
                player.addPotionEffect(new PotionEffect(PotionEffectType.REGENERATION, 20 * 50, 2));
            }, 1L);
        }
    }
}
