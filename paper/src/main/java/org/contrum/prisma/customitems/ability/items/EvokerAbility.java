
package org.contrum.prisma.customitems.ability.items;

import org.bukkit.*;
import org.bukkit.block.Block;
import org.bukkit.entity.EvokerFangs;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customitems.ability.CooldownAbility;
import org.contrum.prisma.systems.impl.CustomEffectSystem;
import org.contrum.prisma.utils.PlayerUtils;
import org.contrum.prisma.utils.UClansUtils;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.prisma.utils.time.TimeUtils;

import java.time.Duration;
import java.util.UUID;

public class EvokerAbility extends CooldownAbility {

    public static final String METADATA_FANGS = "evoker_fangs";
    public static final String METADATA_FANG_EFFECT = "evoker_fang_effect";

    private double fang_damage = this.getConfigSection().getDouble("FANG_DAMAGE", 6);
    private Duration fang_effect_duration = TimeUtils.parseDuration(this.getConfigSection().getString("FANG_EFFECT_DURATION", "10s"));
    private int fang_weakness_amplifier = this.getConfigSection().getInt("FANG_WEAKNESS_AMPLIFIER", 1);

    // Effects
    private Duration absorption_duration = TimeUtils.parseDuration(this.getConfigSection().getString("ABSORPTION_DURATION", "15s"));
    private Duration regeneration_duration = TimeUtils.parseDuration(this.getConfigSection().getString("REGENERATION_DURATION", "10s"));
    private Duration strength_duration = TimeUtils.parseDuration(this.getConfigSection().getString("STRENGTH_DURATION", "30s"));
    private int absorption_amplifier = this.getConfigSection().getInt("ABSORPTION_AMPLIFIER", 4);
    private int regeneration_amplifier = this.getConfigSection().getInt("REGENERATION_AMPLIFIER", 2);
    private int strength_amplifier = this.getConfigSection().getInt("STRENGTH_AMPLIFIER", 1);

    private Duration ally_absorption_duration = TimeUtils.parseDuration(this.getConfigSection().getString("ALLY_ABSORPTION_DURATION", "10s"));
    private Duration ally_regeneration_duration = TimeUtils.parseDuration(this.getConfigSection().getString("ALLY_REGENERATION_DURATION", "5s"));
    private int ally_absorption_amplifier = this.getConfigSection().getInt("ALLY_ABSORPTION_AMPLIFIER", 2);
    private int ally_regeneration_amplifier = this.getConfigSection().getInt("ALLY_REGENERATION_AMPLIFIER", 1);

    public EvokerAbility(PaperServices services) {
        super(services);
    }

    @Override
    public void onReload() {
        this.fang_damage = this.getConfigSection().getDouble("FANG_DAMAGE", 6);
        this.fang_effect_duration = TimeUtils.parseDuration(this.getConfigSection().getString("FANG_EFFECT_DURATION", "10s"));
        this.fang_weakness_amplifier = this.getConfigSection().getInt("FANG_WEAKNESS_AMPLIFIER", 1);

        // Effects
        this.absorption_duration = TimeUtils.parseDuration(this.getConfigSection().getString("ABSORPTION_DURATION", "15s"));
        this.regeneration_duration = TimeUtils.parseDuration(this.getConfigSection().getString("REGENERATION_DURATION", "10s"));
        this.strength_duration = TimeUtils.parseDuration(this.getConfigSection().getString("STRENGTH_DURATION", "30s"));
        this.absorption_amplifier = this.getConfigSection().getInt("ABSORPTION_AMPLIFIER", 4);
        this.regeneration_amplifier = this.getConfigSection().getInt("REGENERATION_AMPLIFIER", 2);
        this.strength_amplifier = this.getConfigSection().getInt("STRENGTH_AMPLIFIER", 1);

        this.ally_absorption_duration = TimeUtils.parseDuration(this.getConfigSection().getString("ALLY_ABSORPTION_DURATION", "10s"));
        this.ally_regeneration_duration = TimeUtils.parseDuration(this.getConfigSection().getString("ALLY_REGENERATION_DURATION", "5s"));
        this.ally_absorption_amplifier = this.getConfigSection().getInt("ALLY_ABSORPTION_AMPLIFIER", 2);
        this.ally_regeneration_amplifier = this.getConfigSection().getInt("ALLY_REGENERATION_AMPLIFIER", 1);
    }

    @Override
    public boolean allowSafeZoneUse() {
        return false;
    }

    @Override
    public String displayName(Player player) {
        return "Evoker";
    }

    @Override
    public String getID() {
        return "EVOKER";
    }

    public boolean isFanged(Player target) {
        return target.hasMetadata(METADATA_FANG_EFFECT);
    }

    @Override
    public void onClick(Player player, ItemStack item, boolean isRight, PlayerInteractEvent event) {
        if (!isRight) return;
        if (this.hasCooldown(player)) {
            this.sendCooldownMessage(player);
            event.setCancelled(true);
            return;
        }
        if (!this.tryUse(player, item)) return;
        item.subtract();

        WorldUtils.playSound(Sound.ENTITY_ILLUSIONER_CAST_SPELL, player.getLocation(), 1, 1);
        player.addPotionEffect(new PotionEffect(PotionEffectType.ABSORPTION, (int) (absorption_duration.toMillis() / 50), absorption_amplifier));
        player.addPotionEffect(new PotionEffect(PotionEffectType.REGENERATION, (int) (regeneration_duration.toMillis() / 50), regeneration_amplifier));
        player.addPotionEffect(new PotionEffect(PotionEffectType.STRENGTH, (int) (strength_duration.toMillis() / 50), strength_amplifier));
        Block block = player.getLocation().getBlock();
        for (int offsetX = -1; offsetX <= 1; offsetX++) {
            for (int offsetZ = -1; offsetZ <= 1; offsetZ++) {
                Location fangLocation = block.getLocation().add(offsetX + 0.5, 0, offsetZ + 0.5);
                EvokerFangs spawn = player.getWorld().spawn(fangLocation, EvokerFangs.class);
                spawn.setMetadata(METADATA_FANGS, new FixedMetadataValue(super.getPlugin(), player.getUniqueId().toString()));
            }
        }
        super.sendLangMessage(player, "USE");
    }

    @EventHandler
    public void hit(EntityDamageByEntityEvent event) {
        if (event.getEntity() instanceof Player player && event.getDamager() instanceof EvokerFangs fangs && fangs.hasMetadata(METADATA_FANGS)) {
            event.setCancelled(true);

            UUID ownerUUID = UUID.fromString(fangs.getMetadata(METADATA_FANGS).getFirst().asString());
            if (player.getUniqueId().equals(ownerUUID))
                return;

            Player owner = Bukkit.getPlayer(ownerUUID);

            // Check for allies
            if (UClansUtils.areSameClan(super.getServices(), player.getUniqueId(), ownerUUID)) {

                if (!super.callAbilityEffectEvent(owner, player, false))
                    return;

                super.sendLangMessage(player, "POSITIVE_FANG_APPLIED", owner);
                player.addPotionEffect(new PotionEffect(PotionEffectType.ABSORPTION, (int) (ally_absorption_duration.toMillis() / 50), ally_absorption_amplifier));
                player.addPotionEffect(new PotionEffect(PotionEffectType.REGENERATION, (int) (ally_regeneration_duration.toMillis() / 50), ally_regeneration_amplifier));
                return;
            }

            if (!super.callAbilityEffectEvent(owner, player, true))
                return;

            super.sendLangMessage(player, "NEGATIVE_FANG_APPLIED", owner);
            PlayerUtils.damage(super.getServices(), player, fang_damage, false, true, false, false, true);

            CustomEffectSystem customEffectSystem = super.getServices().getSystemService().getSystem(CustomEffectSystem.class);
            customEffectSystem.addFakeWeaknessEffect(player, (int) fang_effect_duration.toSeconds(), fang_weakness_amplifier);
        }
    }
}