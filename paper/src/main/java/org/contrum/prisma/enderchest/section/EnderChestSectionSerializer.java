package org.contrum.prisma.enderchest.section;

import lombok.SneakyThrows;
import org.bukkit.Material;
import org.bukkit.configuration.MemorySection;
import org.contrum.chorpu.configuration.serializer.Serializer;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class EnderChestSectionSerializer implements Serializer<Object, List<EnderChestSection>> {

    @Override
    public Map<String, Object> serialize(List<EnderChestSection> queuePriority) {
        return null;
    }

    @SneakyThrows
    @Override
    public List<EnderChestSection> deserialize(Object obj) {
        List<EnderChestSection> sections = new ArrayList<>();

        MemorySection section = (MemorySection) obj;
        Map<String, Object> values = section.getValues(false);

        for (Object o : values.values()) {
            if (!(o instanceof MemorySection value)) continue;

            int start = value.getInt("start");
            int end = value.getInt("end");
            String permission = value.getString("permission");
            Material material = Material.valueOf(value.getString("material"));

            sections.add(new EnderChestSection(start, end, permission, material));
        }

        return sections;
    }
}