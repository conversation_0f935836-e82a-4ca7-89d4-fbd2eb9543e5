package org.contrum.prisma.enderchest;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryOpenEvent;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.PrismaCoreSetting;

public class EnderChestService implements Listener {
    private final PaperServices services;

    public EnderChestService(PaperServices services) {
        this.services = services;

        Bukkit.getPluginManager().registerEvents(this, services.getPlugin());
    }

    public boolean isCustomEnderChestEnabled() {
        return PrismaCoreSetting.CUSTOM_ENDER_CHEST_ENABLED;
    }

    @EventHandler
    public void open(InventoryOpenEvent event) {
        Player player = (Player) event.getPlayer();
        if (event.getInventory().equals(player.getEnderChest())) {
            if (isCustomEnderChestEnabled()) {
                new EnderChestInstance(services, player, event.getInventory());
            }
        }
    }
}
