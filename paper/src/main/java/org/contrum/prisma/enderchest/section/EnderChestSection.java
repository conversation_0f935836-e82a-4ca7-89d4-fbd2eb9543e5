package org.contrum.prisma.enderchest.section;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.contrum.prisma.utils.ItemBuilder1_20;
import org.contrum.tritosa.Translator;

@AllArgsConstructor @Getter
public class EnderChestSection {
    private final int start;
    private final int end;
    private final String permission;
    private final Material material;

    public ItemStack getItem(Player player, Translator translator) {
        ItemBuilder1_20 builder = new ItemBuilder1_20(material);
        builder.name(translator.getAsText(player, "ENDER_CHEST.ITEM_NAME"));
        builder.lore(translator.getListString(player, "ENDER_CHEST.ITEM_LORE"));

        return builder.build();
    }
}
