package org.contrum.prisma.enderchest;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.PrismaCoreSetting;
import org.contrum.prisma.enderchest.section.EnderChestSection;
import org.contrum.prisma.utils.WorldUtils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class EnderChestInstance implements Listener {
    private final PaperServices services;
    private final Player player;
    private final Inventory inventory;

    private final HashMap<Integer, ItemStack> cachedItems = new HashMap<>();

    public EnderChestInstance(PaperServices services, Player player, Inventory inventory) {
        this.services = services;
        this.player = player;
        this.inventory = inventory;

        this.setSections();
        Bukkit.getPluginManager().registerEvents(this, services.getPlugin());
    }

    public void setSections() {
        for (EnderChestSection section : PrismaCoreSetting.CUSTOM_ENDER_CHEST_SECTIONS) {
            boolean hasPermission = section.getPermission() == null || player.hasPermission(section.getPermission());

            ItemStack sectionItem = section.getItem(player, services.getTranslator());
            for (int i = section.getStart(); i <= section.getEnd(); i++) {
                if (inventory.getSize() < i) continue;

                if (hasPermission) {
                    ItemStack item = inventory.getItem(i);
                    if (item != null && item.getType().equals(section.getMaterial())) {
                        inventory.setItem(i, null);
                    }
                    continue;
                }
                cachedItems.put(i, inventory.getItem(i));
                inventory.setItem(i, sectionItem);
            }
        }
    }

    @EventHandler
    public void onClick(InventoryClickEvent event) {
        if (!event.getWhoClicked().equals(player)) return;

        int slot = event.getSlot();
        int rawSlot = event.getRawSlot();

        Inventory clickedInventory = event.getClickedInventory();
        if (clickedInventory == null || !event.getClickedInventory().equals(event.getView().getTopInventory())) return;

        if (cachedItems.containsKey(slot)) {
            WorldUtils.playBlockedSound(player);
            services.getTranslator().send(player, "ENDER_CHEST.BLOCKED_SLOT");
            event.setCancelled(true);
        }
    }

    @EventHandler
    public void close(InventoryCloseEvent event) {
        if (event.getInventory().hashCode() == this.inventory.hashCode()) {
            HandlerList.unregisterAll(this);

            //Set old items
            for (Map.Entry<Integer, ItemStack> entry : cachedItems.entrySet()) {
                Integer slot = entry.getKey();
                ItemStack item = entry.getValue();

                inventory.setItem(slot, item);
            }
        }
    }
}
