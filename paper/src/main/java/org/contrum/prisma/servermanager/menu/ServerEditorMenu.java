package org.contrum.prisma.servermanager.menu;

import lombok.RequiredArgsConstructor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.contrum.chorpu.chat.CC;
import org.contrum.chorpu.inventory.ItemBuilder;
import org.contrum.chorpu.menu.button.Button;
import org.contrum.chorpu.menu.impl.PaginatedMenu;
import org.contrum.chorpu.menu.storage.StorageMenu;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.server.PrismaServer;
import org.contrum.prisma.server.packet.ServerConnectRequestPacket;
import org.contrum.prisma.server.packet.ServerPacket;
import org.contrum.prisma.utils.ItemBuilder1_20;
import org.contrum.prisma.utils.user.SimpleUser;

import java.time.Duration;
import java.util.*;
import java.util.function.Predicate;

@RequiredArgsConstructor
public class ServerEditorMenu extends PaginatedMenu {

    private final PaperServices services;
    private Filter filter = Filter.NONE;

    @Override
    public List<Button> getPaginatedButtons(Player player) {
        List<Button> buttons = new ArrayList<>();

        List<PrismaServer> servers = services.getServersService().getServers()
                .stream()
                .filter(filter.predicate)
                .sorted(new ServerComparator()).toList();

        for (PrismaServer server : servers) {
            ItemBuilder1_20 builder = new ItemBuilder1_20(server.isOnline() ? Material.LIME_WOOL : server.hasWhitelist() ? Material.YELLOW_BANNER : Material.RED_WOOL);
            builder.name("&e" + server.getName() + " &8(" + (server.isOnline() ? "&aOnline" : server.hasWhitelist() ? "&eWhitelisted" : "&cOffline") + "&8)");

            builder.addLore("&f");
            if (server.isOnline() || server.hasWhitelist()) {
                builder.addLore("&eType: &c" + server.getType());
                builder.addLore("&eOnline Players: &c" + server.getOnlinePlayers().size());
                builder.addLore("&eTPS: &c" + server.getTps());
                builder.addLore("&f");
                builder.addLore("&aLeft click to join.");
            }
            builder.addLore("&cRight click to remove.");

            buttons.add(Button.of(builder.build(), (p, click) -> {
                if (click.isLeftClick() && server.isOnline()) {
                    SimpleUser user = new SimpleUser(p.getUniqueId(), p.getName());
                    services.getRedisBackend().sendPacket(new ServerConnectRequestPacket(user, server.getName()));
                    p.sendMessage(CC.translate("&aTrying to connect to the server..."));
                    return;
                }

                if (click.isRightClick()) {
                    services.getServersService().unregisterServer(server);
                    services.getRedisBackend().sendPacket(new ServerPacket(ServerPacket.ServerAction.REMOVE, server.serialize()));
                    p.sendMessage(CC.translate("&cServer " + server.getName() + " has been removed!"));
                }
            }));
        }

        return buttons;
    }

    @Override
    public Map<Integer, Button> getGlobalButtons(Player player) {
        Map<Integer, Button> buttons = new HashMap<>();

        buttons.put(4, Button.of(new ItemBuilder(Material.LEATHER).setName("&eRefresh").setLore("&7Click to refresh servers.").build(), (c) -> {
            ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(c.getUniqueId(), ProfilePaperMetadata.class);
            if (metadata == null) return;

            if (metadata.hasCooldown("menuUpdate")) {
                c.sendMessage(CC.translate("&cWait " + metadata.getCooldown("menuUpdate").getTimeLeft(true) + " to do this again!"));
                return;
            }

            metadata.setCooldown("menuUpdate", Duration.ofSeconds(3));
            this.update(c);
        }));

        buttons.put(37, Button.of(new ItemBuilder(Material.BARRIER).setName("&eGlobal").setLore("&7Remove filter.").build(), (c) -> {
            filter = Filter.NONE;
            this.update(c);
        }));

        buttons.put(39, Button.of(new ItemBuilder(Material.COMPARATOR).setName("&eProxy").setLore("&7Filter by proxies.").build(), (c) -> {
            filter = Filter.PROXY;
            this.update(c);
        }));

        buttons.put(41, Button.of(new ItemBuilder(Material.NETHER_STAR).setName("&eLobby").setLore("&7Filter by lobbies.").build(), (c) -> {
            filter = Filter.LOBBY;
            this.update(c);
        }));

        buttons.put(43, Button.of(new ItemBuilder(Material.HOPPER).setName("&eGame Server").setLore("&7Filter by game servers.").build(), (c) -> {
            filter = Filter.GAME_SERVERS;
            this.update(c);
        }));

        return buttons;
    }

    @Override
    public int getRows(Player player) {
        return 5;
    }

    @Override
    public boolean isAutoUpdate() {
        return false;
    }

    @Override
    public StorageMenu.FillType getFillType() {
        return StorageMenu.FillType.ONLY_CORNERS;
    }

    @Override
    public String getTitle(Player player) {
        return "Server editor";
    }

    private enum Filter {
        NONE(server -> true),
        LOBBY(server -> server.getType().equals(PrismaServer.ServerType.LOBBY)),
        PROXY(server -> server.getType().equals(PrismaServer.ServerType.PROXY)),
        GAME_SERVERS(server -> server.getType().isGameMode());

        private final Predicate<PrismaServer> predicate;

        private Filter(Predicate<PrismaServer> predicate) {
            this.predicate = predicate;
        }

        public boolean applies(PrismaServer server) {
            return predicate.test(server);
        }
    }

    private static class ServerComparator implements Comparator<PrismaServer> {

        @Override
        public int compare(PrismaServer s1, PrismaServer s2) {
            return Boolean.compare(s2.isOnline(), s1.isOnline());
        }
    }
}
