package org.contrum.prisma.servermanager;

import lombok.RequiredArgsConstructor;
import org.bukkit.Bukkit;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.server.ServersService;
import org.contrum.prisma.servermanager.packets.SMRunCmdPacket;
import org.contrum.prisma.utils.redis.pyrite.packet.PacketContainer;
import org.contrum.prisma.utils.redis.pyrite.packet.RedisPacketListener;

@RequiredArgsConstructor
public class ServerManagerListener implements PacketContainer {

    private final PaperServices services;
    private final ServersService serversService;

    @RedisPacketListener
    public void runCmd(SMRunCmdPacket packet) {
        if (serversService.getCurrentServer() != null && serversService.getCurrentServer().getName().equalsIgnoreCase(packet.getServer())) {
            TaskUtil.run(services.getPlugin(), () -> Bukkit.dispatchCommand(Bukkit.getConsoleSender(), packet.getCmd()));
        }
    }
}