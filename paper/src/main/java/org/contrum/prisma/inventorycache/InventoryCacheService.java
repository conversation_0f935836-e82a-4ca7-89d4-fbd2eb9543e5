package org.contrum.prisma.inventorycache;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.metadata.FixedMetadataValue;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;

public class InventoryCacheService implements Listener {
    private final PaperServices services;

    public static final String INVENTORY_CACHED_METADATA = "inventoryCached";

    public InventoryCacheService(PaperServices services) {
        this.services = services;

        Bukkit.getPluginManager().registerEvents(this, services.getPlugin());
    }

    public void shutdown() {
        for (Player player : Bukkit.getOnlinePlayers()) {
            if (this.hasTemporalInventory(player))
                this.applyMainInventory(player);
        }

        HandlerList.unregisterAll(this);
    }

    public boolean cacheInventory(Player player) {
        if (this.hasTemporalInventory(player)) {
            services.getTranslator().send(player, "ERRORS.INVENTORY_CANNOT_BE_CACHED");
            return false;
        }

        player.setMetadata(INVENTORY_CACHED_METADATA, new FixedMetadataValue(services.getPlugin(), true));
        ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
        metadata.setCachedInventory(player.getInventory().getContents().clone());

        return true;
    }

    public void applyMainInventory(Player player) {
        ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
        player.removeMetadata(INVENTORY_CACHED_METADATA, services.getPlugin());

        ItemStack[] cachedInventory = metadata.getCachedInventory();
        if (cachedInventory != null) {
            player.getInventory().setContents(cachedInventory);
            metadata.setCachedInventory(null);
        }
    }

    public boolean hasTemporalInventory(Player player) {
        return player.hasMetadata(INVENTORY_CACHED_METADATA);
    }

    @EventHandler
    public void onQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();

        if (this.hasTemporalInventory(player))
            this.applyMainInventory(player);
    }

    @EventHandler
    public void onJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);

        if (this.hasTemporalInventory(player)) {
            this.applyMainInventory(player);
        }
    }
}
