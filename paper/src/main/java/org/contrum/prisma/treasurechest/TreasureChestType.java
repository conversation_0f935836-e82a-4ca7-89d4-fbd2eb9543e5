/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by izLoki on 26/05/2024
 * Website: contrum.org
 */

package org.contrum.prisma.treasurechest;

import lombok.Getter;
import org.bukkit.Material;

import java.util.List;

@Getter
public class TreasureChestType {
    private final String id;
    private final String displayName;
    private final Material material;
    private final int inventorySize;
    private final int minItems;
    private final int maxItems;
    private final int resetEvery;
    private final double hologramOffsetY;
    private final List<String> hologramLines;
    private final List<TreasureChestReward> rewards;

    public TreasureChestType(String id, String displayName, Material material, int inventorySize, int minItems, int maxItems, int resetEvery, double hologramOffsetY, List<String> hologramLines, List<TreasureChestReward> rewards) {
        this.id = id;
        this.displayName = displayName;
        this.material = material;
        this.inventorySize = inventorySize;
        this.minItems = minItems;
        this.maxItems = maxItems;
        this.resetEvery = resetEvery;
        this.hologramOffsetY = hologramOffsetY;
        this.hologramLines = hologramLines;
        this.rewards = rewards;
    }

    public List<TreasureChestReward> getTreasureRewards(){
        return this.rewards.stream().map(r -> (TreasureChestReward) r).toList();
    }
}
