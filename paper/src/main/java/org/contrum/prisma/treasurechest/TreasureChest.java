/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by izLoki on 26/05/2024
 * Website: contrum.org
 */

package org.contrum.prisma.treasurechest;

import lombok.Getter;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.block.Container;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.inventory.*;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitTask;
import org.contrum.chorpu.TaskUtil;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.events.TreasureChestOpenEvent;
import org.contrum.prisma.events.TreasureChestRefillEvent;
import org.contrum.prisma.holograms.Hologram;
import org.contrum.prisma.holograms.HologramHandler;
import org.contrum.prisma.utils.CCP;
import org.contrum.prisma.utils.Cooldown;
import org.contrum.prisma.utils.NBTUtil;

import java.time.Duration;
import java.util.*;

@Getter
public class TreasureChest implements Listener {
    private final PaperServices services;
    //Chest info
    private final UUID uuid;
    private final Location location;
    private final TreasureChestType chestType;

    //Instance info
    private Hologram hologram;
    private Container container;
    private final BukkitTask updateTask;
    private Cooldown resetCooldown;
    private boolean isEmpty = false;

    public TreasureChest(PaperServices services, UUID uuid, Location location, TreasureChestType chestType, Cooldown resetCooldown) {
        this.services = services;
        this.uuid = uuid;
        this.location = location;
        this.chestType = chestType;
        this.resetCooldown = resetCooldown;
        Bukkit.getPluginManager().registerEvents(this, services.getPlugin());

        //Delayed load
        TaskUtil.runLater(services.getPlugin(), this::load, 10L);

        //Run update task
        updateTask = Bukkit.getScheduler().runTaskTimer(services.getPlugin(), () -> {
            if (this.resetCooldown == null) return;

            if (this.resetCooldown.hasExpired()) {
                this.resetCooldown = null;
                this.setNewLoot();
            }
        }, 20L, 20L);
    }

    public void disable() {
        this.hologram.destroy();
        HandlerList.unregisterAll(this);
        if (updateTask != null && !updateTask.isCancelled()) updateTask.cancel();
    }

    public void load() {
        //Place chest
        if (!location.getBlock().getType().equals(chestType.getMaterial()))
            location.getBlock().setType(chestType.getMaterial());

        //Set loot
        if (resetCooldown == null || resetCooldown.hasExpired()) setNewLoot();

        //Create hologram
        if (hologram != null) {
            hologram.destroy();
        }

        //Replace lore lines
        List<String> hologramLines = new ArrayList<>(chestType.getHologramLines());
        for (int i = 0; i < hologramLines.size(); i++) {
            String cadena = hologramLines.get(i);
            cadena = cadena.replace("<UUID>", uuid.toString());
            hologramLines.set(i, cadena);
        }

        HologramHandler hologramHandler = services.getHologramsService().getHandler();
        hologram = hologramHandler.getHologram("TreasureChestDH-" + this.uuid.toString());
        if (hologram == null)
            hologram = hologramHandler.createHologram("TreasureChestDH-" + this.uuid.toString(), location.clone().add(0.5, chestType.getHologramOffsetY(), 0.5), false, hologramLines);
        hologram.setDisplayRange(6);
    }

    public void setNewLoot() {
        Block block = location.getBlock();
        if (!(block.getState() instanceof Container)) {
            block.setType(Material.CHEST);
        }

        this.container = (Container) block.getState();
        Inventory inventory = container.getSnapshotInventory();

        //Clear inventory
        inventory.clear();

        //Get loot count
        Random random = new Random();
        int range = chestType.getMaxItems() - chestType.getMinItems() + 1;
        int itemCount = random.nextInt(range) + chestType.getMinItems();
        if (itemCount > inventory.getSize()) itemCount = inventory.getSize();

        //Add random loot
        HashMap<Integer, ItemStack> toAdd = new HashMap<>();
        List<String> addedRewards = new ArrayList<>();
        for (int i = 0; i < itemCount; i++) {
            TreasureChestReward reward = chestType.getRewards().get(random.nextInt(chestType.getRewards().size()));
            if (reward.getMaxPerChest() > 0) {
                int count = Collections.frequency(addedRewards, reward.getId());
                if (count >= reward.getMaxPerChest()) {
                    //Try to get another reward
                    reward = null;
                    for (TreasureChestReward r : chestType.getRewards()) {
                        if (r.getMaxPerChest() > 0) {
                            count = Collections.frequency(addedRewards, r.getId());
                            if (count < r.getMaxPerChest()) reward = r;
                        } else {
                            reward = r;
                        }
                    }
                    if (reward == null) continue;
                }
                ;
            }
            //Get random empty slot
            int slot = 0;
            do {
                slot = random.nextInt(inventory.getSize());
            } while (!isEmpty(inventory, slot));
            addedRewards.add(reward.getId());

            //Add item
            toAdd.put(slot, reward.getItem());
        }


        TreasureChestRefillEvent refillEvent = new TreasureChestRefillEvent(this, block, toAdd);
        Bukkit.getPluginManager().callEvent(refillEvent);

        for (Map.Entry<Integer, ItemStack> map : refillEvent.getItems().entrySet()) {
            int slot = map.getKey();
            ItemStack item = map.getValue();
            inventory.setItem(slot, item);
        }
        container.setCustomName(CC.translate(this.getChestType().getDisplayName()));
        container.update(true);
        resetCooldown = new Cooldown(Duration.ofSeconds(this.getChestType().getResetEvery()));
        isEmpty = false;
    }

    public Container getContainer() {
        return this.container != null ? container : (Container) location.getBlock().getState();
    }

    public boolean isEmpty(Inventory inv, int slot) {
        return inv.getItem(slot) == null || inv.getItem(slot).getType() == Material.AIR;
    }

    @EventHandler
    public void playerOpenChest(PlayerInteractEvent event) {
        if (event.getAction().equals(Action.RIGHT_CLICK_BLOCK)) {
            Block block = event.getClickedBlock();
            if (block == null) return;

            //Clicked block
            if (block.getLocation().equals(this.location)) {
                TreasureChestOpenEvent e = new TreasureChestOpenEvent(event.getPlayer(), block, this);
                Bukkit.getPluginManager().callEvent(e);
                if (e.isCancelled()) event.setCancelled(true);
            }
        }
    }

    @EventHandler
    public void addItemDrag(InventoryDragEvent event) {
        if (event.getInventory().hashCode() == this.getContainer().getInventory().hashCode()) {
            event.setCancelled(true);
        }
    }

    @EventHandler
    public void addItem(InventoryClickEvent event) {
        if (event.getInventory().hashCode() == this.getContainer().getInventory().hashCode()) {
            InventoryAction action = event.getAction();
            if (action.equals(InventoryAction.MOVE_TO_OTHER_INVENTORY)) {
                Inventory inventory = event.getClickedInventory();
                if (inventory != null && inventory.getType().equals(InventoryType.PLAYER)) event.setCancelled(true);
            }

            if (action.equals(InventoryAction.SWAP_WITH_CURSOR)) {
                Inventory inventory = event.getClickedInventory();
                if (inventory != null && !inventory.getType().equals(InventoryType.PLAYER)) event.setCancelled(true);
            }

            if (action.name().contains("PLACE")) {
                Inventory inventory = event.getClickedInventory();
                if (inventory != null && !inventory.getType().equals(InventoryType.PLAYER)) event.setCancelled(true);
            }

            int hotbarButton = event.getHotbarButton();
            if (hotbarButton != -1 && (event.getCurrentItem() == null || event.getCurrentItem().getType().equals(Material.AIR))) {
                event.setCancelled(true);
            }
        }
    }

    @EventHandler(ignoreCancelled = true, priority = EventPriority.HIGH)
    public void itemCommandDispatch(InventoryClickEvent event) {
        if (event.getInventory().hashCode() == this.getContainer().getInventory().hashCode()) {
            //Pass item check
            Player player = (Player) event.getWhoClicked();
            InventoryAction action = event.getAction();
            ItemStack item = null;

            if (event.getClick() == ClickType.SWAP_OFFHAND) {
                event.setCancelled(true);
                return;
            }

            int hotbarButton = event.getHotbarButton();
            if (hotbarButton != -1 || action.equals(InventoryAction.MOVE_TO_OTHER_INVENTORY) || action.name().contains("PICKUP")) {
                item = event.getCurrentItem();
                if (item == null || !item.hasItemMeta()) return;

                Inventory clickedInventory = event.getClickedInventory();
                if (clickedInventory == null || clickedInventory.getType().equals(InventoryType.PLAYER)) return;

                if (!item.hasItemMeta()) return;
                for (TreasureChestReward reward : chestType.getRewards()) {
                    if (reward.isRewardItem(item)) {
                        //Run commands
                        player.playSound(player.getLocation(), Sound.ENTITY_ARROW_HIT_PLAYER, 1, 1);
                        for (String command : reward.getCommands()) {
                            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command.replaceAll("<player>", player.getName()));
                        }

                        if (reward.isRemoveOnCommand()) {
                            event.setCurrentItem(null);
                            break;
                        }
                        // remove nbt
                        NBTUtil.removeNBT(item, TreasureChestReward.NBT_KEY);
                    }
                }
            }
            TaskUtil.run(services.getPlugin(), () -> {
                this.isEmpty = event.getInventory().isEmpty();
            });
        }
    }

    @EventHandler
    public void inventoryOpen(InventoryOpenEvent event) {
        if (event.getInventory().hashCode() == this.getContainer().getInventory().hashCode()) {
            if (event.getInventory().isEmpty()) {
                event.getPlayer().sendMessage(CCP.translateAndFront("&ceste cofre está vacio!"));
                event.setCancelled(true);
            }
        }
    }
}
