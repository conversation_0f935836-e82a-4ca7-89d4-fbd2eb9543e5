/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by izLoki on 26/05/2024
 * Website: contrum.org
 */

package org.contrum.prisma.treasurechest;

import lombok.Getter;
import org.bukkit.Bukkit;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.persistence.PersistentDataType;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.utils.NBTUtil;
import org.contrum.prisma.utils.config.reward.ConfigReward;

import java.util.List;
import java.util.Random;

@Getter
public class TreasureChestReward {
    public static final String NBT_KEY = "treasure_chest_reward";

    private final String id;
    private final ItemStack item;
    private int maxPerChest = -1;
    private boolean enchanted = false;
    private String amount = "1";
    private boolean removeOnCommand = false;
    private final List<String> commands;

    public TreasureChestReward(PaperServices addon, ConfigurationSection itemSection) {
        // Get item
        this.item = new ConfigReward(addon, itemSection).getItemStack();

        // Get commands
        this.id = itemSection.getName();
        this.removeOnCommand = itemSection.getBoolean("REMOVE_ITEM", false);
        this.commands = itemSection.getStringList("COMMANDS");
        this.amount = itemSection.getString("AMOUNT", "1");
        this.enchanted = itemSection.getBoolean("ENCHANTED", false);
        this.maxPerChest = itemSection.getInt("MAX_PER_CHEST", -1);
    }

    public ItemStack getItem() {
        if (this.item == null) {
            Bukkit.getLogger().warning("Invalid treasure chest item: " + this.id);
            return null;
        }
        ItemStack i = this.item.clone();
        i.setAmount(this.getAmount());
        NBTUtil.setNBT(i, NBT_KEY, PersistentDataType.STRING, this.id);
        if (this.enchanted && !i.hasEnchants()) {
            i.addUnsafeEnchantment(Enchantment.UNBREAKING, 1);
            i.addItemFlags(ItemFlag.HIDE_ENCHANTS);
        }
        return i;
    }

    public boolean isRewardItem(ItemStack item) {
        return item.hasItemMeta() && NBTUtil.getNBT(item.getItemMeta(), NBT_KEY, PersistentDataType.STRING, "").equals(this.id);
    }

    public int getAmount() {
        if (amount.contains(",")) {
            amount = amount.replace(" ", "");
            String[] args = amount.split(",");
            int min = Integer.parseInt(args[0]);
            int max = Integer.parseInt(args[1]);

            Random random = new Random();
            int range = max - min + 1;
            int count = random.nextInt(range) + min;
            return count;
        }
        return Integer.parseInt(amount);
    }
}
