/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by izLoki on 26/05/2024
 * Website: contrum.org
 */

package org.contrum.prisma.treasurechest;

import lombok.Getter;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.persistence.PersistentDataType;
import org.contrum.chorpu.TaskUtil;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.utils.config.ConfigFile;
import org.contrum.prisma.utils.Cooldown;
import org.contrum.prisma.utils.ItemBuilder1_20;
import org.contrum.prisma.utils.NBTUtil;

import java.time.Duration;
import java.util.*;

@Getter
public class TreasureChestService implements Listener {
    private final PaperServices services;
    private ConfigFile config;
    private final HashMap<UUID, TreasureChest> chests = new HashMap<>();
    private final HashMap<String, TreasureChestType> chestTypes = new HashMap<>();
    private String typeString;

    public TreasureChestService(PaperServices services){
        this.services = services;
        config = new ConfigFile(services.getPlugin(), "treasurechest.yml");

        //Load chests
        loadChests();

        //Register listener
        Bukkit.getPluginManager().registerEvents(this, services.getPlugin());
    }

    public void save(){
        FileConfiguration cfg = config.getConfig();

        //Save chests
        cfg.set("CHESTS", null);
        for (TreasureChest chest : chests.values()){
            String id = chest.getUuid().toString();
            cfg.set("CHESTS." + id + ".LOCATION", chest.getLocation());
            cfg.set("CHESTS." + id + ".TYPE", chest.getChestType().getId());
            cfg.set("CHESTS." + id + ".RESET_COOLDOWN", chest.getResetCooldown().getTimeLeftAsDuration().getSeconds());
        }
        config.save();
    }

    public void unload(){
        this.save();

        //destroy chests
        chests.values().forEach(TreasureChest::disable);
    }

    public void reload(){
        this.chests.values().forEach(TreasureChest::disable);
        this.chests.clear();
        this.chestTypes.clear();
        config = new ConfigFile(services.getPlugin(), "treasurechest.yml");
        loadChests();
    }

    public void loadChests(){
        //Load chest types
        ConfigurationSection chestTypesSection = config.getConfig().getConfigurationSection("CHEST_TYPES");
        if (chestTypesSection != null){
            Set<String> types = chestTypesSection.getKeys(false);
            for (String id : types){
                ConfigurationSection section = chestTypesSection.getConfigurationSection(id);
                if (section != null){

                    //Get rewards
                    List<TreasureChestReward> rewards = new ArrayList<>();
                    ConfigurationSection rewardsSection = section.getConfigurationSection("REWARDS");
                    if (rewardsSection != null){
                        for (String rewardID : rewardsSection.getKeys(false)){
                            ConfigurationSection rewardSection = rewardsSection.getConfigurationSection(rewardID);
                            if (rewardSection != null) rewards.add(new TreasureChestReward(services, rewardSection));
                        }
                    }

                    //Create chest type
                    TreasureChestType type = new TreasureChestType(
                            id,
                            section.getString("INVENTORY_TITTLE", ""),
                            Material.valueOf(section.getString("MATERIAL", "STONE")),
                            section.getInt("INVENTORY_SIZE", 27),
                            section.getInt("MIN_ITEMS", 1),
                            section.getInt("MAX_ITEMS", 1),
                            section.getInt("RESET_EVERY", 300),
                            section.getDouble("HOLOGRAM_OFFSET_Y", 0),
                            section.getStringList("HOLOGRAM"),
                            rewards);
                    chestTypes.put(id.toUpperCase(), type);
                }
            }
        }

        if (chestTypes.isEmpty()) {
            Bukkit.getLogger().warning("No hay chest_types creados! Skipeando TreasureChest System");
            return;
        }

        //Load chests
        ConfigurationSection chestsSection = config.getConfig().getConfigurationSection("CHESTS");
        if (chestsSection != null){
            Set<String> ids = chestsSection.getKeys(false);
            for (String id : ids){
                UUID uuid = UUID.fromString(id);
                ConfigurationSection section = chestsSection.getConfigurationSection(id);
                if (section == null) continue;

                Location location = section.getLocation("LOCATION");
                String chestTypeString = section.getString("TYPE", "");
                long nextResetSeconds = section.getLong("RESET_COOLDOWN", -1);
                Cooldown nextReset = nextResetSeconds == -1 ? null : new Cooldown(Duration.ofSeconds(nextResetSeconds));
                TreasureChestType type = chestTypes.get(chestTypeString.toUpperCase());
                if (type == null) type = chestTypes.get(chestTypes.values().toArray(new TreasureChestType[0])[0]);

                chests.put(uuid, new TreasureChest(services, uuid, location, type, nextReset));
            }
        }
    }


    public HashMap<UUID, TreasureChest> getTreasureChests() {
        return chests;
    }

    public HashMap<String, TreasureChestType> getTreasureChestTypes() {
        return chestTypes;
    }

    public void refillChests(){
        this.chests.values().forEach(TreasureChest::setNewLoot);
    }

    public ItemStack getChestItem(String typeString){
        this.typeString = typeString;
        TreasureChestType type = chestTypes.get(typeString);
        if (type == null) return null;
        ItemBuilder1_20 builder = new ItemBuilder1_20(type.getMaterial()).addNBT("TreasureChest", PersistentDataType.STRING, typeString);
        builder.name("&eChest: &c" + typeString);

        return builder.build();
    }

    public void createChest(Location location, String typeString){
        TreasureChestType type = chestTypes.get(typeString);
        UUID uuid = UUID.randomUUID();
        chests.put(uuid, new TreasureChest(services, uuid, location, type, null));
    }

    public Cooldown getResetDuration(UUID uuid){
        TreasureChest treasureChest = chests.get(uuid);
        if (treasureChest == null) return null;
        return treasureChest.getResetCooldown();
    }

    public boolean isEmpty(UUID uuid){
        TreasureChest treasureChest = chests.get(uuid);
        if (treasureChest == null) return false;

        return treasureChest.isEmpty();
    }

    @EventHandler(ignoreCancelled = true, priority = EventPriority.HIGH)
    public void blockBreak(BlockBreakEvent event){
        Player player = event.getPlayer();
        Block block = event.getBlock();
        for (Map.Entry<UUID, TreasureChest> map : chests.entrySet()){
            TreasureChest chest = map.getValue();
            if (block.getLocation().equals(chest.getLocation())){
                event.setCancelled(true);
                if (player.isOp()){
                    if (!player.isSneaking()){
                        player.sendMessage(CC.translate("&cRompe el cofre agachado para eliminarlo!"));
                    } else {
                        chest.disable();
                        chest.getLocation().getBlock().setType(Material.AIR);
                        chests.remove(map.getKey());
                        player.sendMessage(CC.translate("&aEliminaste un cofre!"));
                    }
                }
                return;
            }
        }
    }

    @EventHandler
    public void placeBlockEvent(BlockPlaceEvent event){
        ItemStack item = event.getItemInHand();
        if (!item.hasItemMeta()) return;

        String typeString = NBTUtil.getNBT(item.getItemMeta(), "TreasureChest", PersistentDataType.STRING, "");
        if (typeString.isEmpty()) return;

        TaskUtil.run(services.getPlugin(), ()->createChest(event.getBlock().getLocation(), typeString));
    }
}
