package org.contrum.prisma.cacheService;

import org.bukkit.Location;
import org.bukkit.configuration.ConfigurationSection;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.utils.config.ConfigFile;

public class CacheService {

    private final PaperServices services;

    private final ConfigFile file;

    public CacheService(PaperServices services) {
        this.services = services;

        this.file = new ConfigFile(services.getPlugin(), "data/cache.yml");
    }

    public boolean contains(String path) {
        return file.getConfig().contains(path);
    }

    public ConfigurationSection createSection(String path) {
        return file.getConfig().createSection(path);
    }

    public Object get(String path) {
        return file.getConfig().get(path);
    }

    public ConfigurationSection getConfigurationSection(String path) {
        return file.getConfig().getConfigurationSection(path);
    }

    public String getString(String path) {
        return file.getConfig().getString(path);
    }

    public int getInt(String path) {
        return file.getConfig().getInt(path);
    }

    public Location getLocation(String path) {
        return file.getConfig().getLocation(path);
    }

    public void set(String path, Object value) {
        file.getConfig().set(path, value);
    }

    public void save() {
        file.save();
    }
}
