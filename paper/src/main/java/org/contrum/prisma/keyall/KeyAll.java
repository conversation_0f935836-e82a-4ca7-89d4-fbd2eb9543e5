package org.contrum.prisma.keyall;

import lombok.Getter;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;

import java.util.HashMap;
import java.util.Map;

@Getter
public class KeyAll {
    private static final String DEFAULT_PERMISSION = "default";

    private final PaperServices services;
    private final String name;

    private final Map<String, KeyAllRewards> rewardsMap = new HashMap<>(); // Data = permission -> rewards;

    public KeyAll(PaperServices services, ConfigurationSection section) {
        this.services = services;
        this.name = section.getString("NAME", "KeyAll");

        ConfigurationSection rewardsSection = section.getConfigurationSection("REWARDS");
        if (rewardsSection == null)
            return;

        for (String key : rewardsSection.getKeys(false)) {
            ConfigurationSection rewardSec = rewardsSection.getConfigurationSection(key);
            if (rewardSec == null)
                continue;

            KeyAllRewards rewards = new KeyAllRewards(services, rewardSec);
            this.rewardsMap.put(key, rewards);
        }
    }

    public KeyAllRewards getRewardsFor(Player player) {

        KeyAllRewards bestReward = null;
        for (Map.Entry<String, KeyAllRewards> entry : rewardsMap.entrySet()) {
            String permission = entry.getKey();
            KeyAllRewards reward = entry.getValue();

            if ((permission.equalsIgnoreCase(DEFAULT_PERMISSION) || player.hasPermission(permission)) && (bestReward == null || bestReward.getPriority() < reward.getPriority())) {
                bestReward = reward;
            }
        }

        return bestReward;
    }
}
