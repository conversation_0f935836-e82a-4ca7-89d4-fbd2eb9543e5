package org.contrum.prisma.keyall;

import lombok.Getter;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.utils.config.reward.ConfigReward;

import java.util.ArrayList;
import java.util.List;

@Getter
public class KeyAllRewards {


    private final PaperServices services;
    private final int priority;
    private final List<ConfigReward> rewards = new ArrayList<>();

    public KeyAllRewards(PaperServices services, ConfigurationSection section) {
        this.services = services;
        this.priority = section.getInt("PRIORITY", 0);

        for (String key : section.getKeys(false)) {
            ConfigurationSection rewardSection = section.getConfigurationSection(key);
            if (rewardSection == null) continue;

            ConfigReward reward = new ConfigReward(services, rewardSection);
            this.rewards.add(reward);
        }
    }

    public void giveRewards(Player player) {
        for (ConfigReward reward : rewards) {
            reward.give(services, player);
        }
    }

}
