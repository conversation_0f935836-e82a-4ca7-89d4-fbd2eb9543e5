package org.contrum.prisma.holograms.impl.fancy;

import de.oliver.fancyholograms.api.FancyHologramsPlugin;
import de.oliver.fancyholograms.api.HologramManager;
import de.oliver.fancyholograms.api.data.TextHologramData;
import org.bukkit.Location;
import org.bukkit.entity.Display;
import org.contrum.prisma.holograms.Hologram;
import org.contrum.prisma.holograms.HologramHandler;
import org.jetbrains.annotations.Nullable;

import java.util.List;

public class FancyHologramsHandler extends HologramHandler {


    private final FancyHologramsPlugin plugin;

    public FancyHologramsHandler() {
        plugin = FancyHologramsPlugin.get();
    }

    @Override
    public Hologram createHologram(String name, Location location, boolean persistent, List<String> lines) {
        TextHologramData textHologramData = new TextHologramData(name, location);
        textHologramData.setPersistent(persistent);
        textHologramData.setBillboard(Display.Billboard.FIXED);
        for (String line : lines) {
            textHologramData.addLine(line);
        }

        de.oliver.fancyholograms.api.hologram.Hologram hologram = plugin.getHologramManager().create(textHologramData);
        return new FancyHologramWrapper(hologram);
    }

    @Override
    public @Nullable Hologram getHologram(String name) {
        HologramManager manager = plugin.getHologramManager();
        de.oliver.fancyholograms.api.hologram.Hologram hologram = manager.getHologram(name).orElse(null);
        return hologram == null ? null : new FancyHologramWrapper(hologram);
    }
}
