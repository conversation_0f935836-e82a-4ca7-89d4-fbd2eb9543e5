package org.contrum.prisma.holograms.impl.decent;

import eu.decentsoftware.holograms.api.DHAPI;
import org.bukkit.Location;
import org.contrum.prisma.holograms.Hologram;
import org.contrum.prisma.holograms.HologramHandler;
import org.jetbrains.annotations.Nullable;

import java.util.List;

public class DecentHologramsHandler extends HologramHandler {

    @Override
    public Hologram createHologram(String name, Location location, boolean persistent, List<String> lines) {
        return new DecentHologramWrapper(DHAPI.createHologram(name, location, lines));
    }

    @Override
    public @Nullable Hologram getHologram(String name) {
        eu.decentsoftware.holograms.api.holograms.Hologram hologram = DHAPI.getHologram(name);
        return hologram == null ? null : new DecentHologramWrapper(hologram);
    }
}
