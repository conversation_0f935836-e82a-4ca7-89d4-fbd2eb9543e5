package org.contrum.prisma.holograms;

import org.bukkit.Location;
import org.jetbrains.annotations.Nullable;

import java.util.List;

public abstract class HologramHandler {

    public Hologram createHologram(String name, Location location, boolean persistent, String... lines) {
        return this.createHologram(name, location, persistent, List.of(lines));
    }

    public abstract Hologram createHologram(String name, Location location, boolean persistent, List<String> lines) ;

    public boolean destroyHologram(String name) {
        Hologram hologram = getHologram(name);
        if (hologram != null) {
            hologram.destroy();
            return true;
        }
        return false;
    }

    @Nullable
    public abstract Hologram getHologram(String name);
}
