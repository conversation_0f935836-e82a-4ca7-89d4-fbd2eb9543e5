package org.contrum.prisma.scoreboard.parser;

import me.clip.placeholderapi.PlaceholderAPI;
import org.bukkit.entity.Player;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.scoreboard.ScoreboardService;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

public class ScoreboardLineParser {
    private final PaperServices services;

    private final List<ScoreboardPlaceholder> placeholders = new ArrayList<>();

    public ScoreboardLineParser(ScoreboardService service) {
        this.services = service.getServices();
    }

    public void addPlaceholder(ScoreboardPlaceholder placeholder) {
        this.placeholders.add(placeholder);
    }

    public void removePlaceholder(ScoreboardPlaceholder placeholder) {
        this.placeholders.remove(placeholder);
    }

    public String parseLine(Player player, String original) {
        for (ScoreboardPlaceholder p : this.placeholders) {
            original = p.replace(player, original);
        }

        return CC.translate(PlaceholderAPI.setPlaceholders(player, original));
    }

    public List<String> splitLines(List<String> lines) {
        List<String> newList = new ArrayList<>();

        for (String line : lines) {
            String[] s = line.split("\n");
            for (String splitLine : s) {
                if (splitLine.contains("\\x")) {
                    long count = Pattern.compile("\\\\x").matcher(splitLine).results().count()-1;

                    for (int j = 0; j < count; j++) {
                        if (!newList.isEmpty()) {
                            String removed = newList.remove(newList.size() - 1);
                        }
                    }
                } else {
                    newList.add(splitLine);
                }
            }
        }

        return newList;
    }
}
