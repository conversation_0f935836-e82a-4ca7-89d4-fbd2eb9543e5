package org.contrum.prisma.scoreboard.preset;

import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.contrum.prisma.scoreboard.ScoreboardService;
import org.contrum.prisma.scoreboard.line.ScoreboardLine;
import org.contrum.prisma.scoreboard.parser.ScoreboardLineParser;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class ScoreboardContainer {
    private final ScoreboardService service;
    private final ScoreboardLineParser parser;
    private final ScoreboardPreset preset;

    private ScoreboardLine tittle;
    private List<ScoreboardLine> lines = new ArrayList<>();

    public ScoreboardContainer(ScoreboardService service, ScoreboardPreset preset) {
        this.service = service;
        this.parser = service.getParser();
        this.preset = preset;

        this.load();
    }

    public List<String> tickLines(Player player) {
        return parser.splitLines(lines.stream().map(l -> l.tickLine(player)).toList());
    }

    public String tickTittle(Player player) {
        return tittle.tickLine(player);
    }

    public void load() {
        FileConfiguration configuration = preset.getFile().getConfig();
        this.tittle = new ScoreboardLine(service, configuration.getConfigurationSection("title"));

        ConfigurationSection rows = configuration.getConfigurationSection("rows");
        if (rows == null) return;
        this.lines = rows.getKeys(false)
                .stream()
                .map(rows::getConfigurationSection)
                .filter(Objects::nonNull)
                .map(s -> new ScoreboardLine(service, s))
                .toList();
    }

    public void reload() {
        this.load();
    }
}