package org.contrum.prisma.scoreboard;

import lombok.Getter;
import org.bukkit.Bukkit;
import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.java.JavaPlugin;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customitems.ability.items.LifeStealerAbility;
import org.contrum.prisma.scoreboard.parser.ScoreboardLineParser;
import org.contrum.prisma.scoreboard.preset.ScoreboardContainer;
import org.contrum.prisma.scoreboard.preset.ScoreboardPreset;
import org.contrum.prisma.utils.config.ConfigFile;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Getter
public class ScoreboardService {
    private final PaperServices services;
    private final ScoreboardLineParser parser;

    private final HashMap<String, ScoreboardPreset> presets = new HashMap<>();
    private final Map<UUID, ScoreboardProfile> scoreboards = new ConcurrentHashMap<>();

    private final Set<Plugin> pluginBoards = new HashSet<>();

    private ScoreboardTask scoreboardTask;

    public ScoreboardService(PaperServices services) {
        this.services = services;

        this.parser = new ScoreboardLineParser(this);
        this.scoreboardTask = new ScoreboardTask(this);

        TaskUtil.runAsyncTimer(services.getPlugin(), scoreboardTask, 0, 1);
        Bukkit.getPluginManager().registerEvents(new ScoreboardListener(this), services.getPlugin());
        this.loadBoards();
    }

    public ScoreboardPreset getActivePreset() {
        if (presets.isEmpty()) return null;
        ScoreboardPreset preset = presets.values().iterator().next();
        for (ScoreboardPreset p : presets.values()) {
            if (p.isActive()) {
                preset = p;
                break;
            }
        }

        return preset;
    }

    public void setActivePreset(String name, boolean save) {
        ScoreboardPreset preset = this.getPreset(name);
        this.setActivePreset(preset, save);
    }

    public void setActivePreset(ScoreboardPreset preset, boolean save) {
        ScoreboardPreset activePreset = this.getActivePreset();
        if (activePreset != preset) {
            //Update
            for (ScoreboardProfile value : scoreboards.values()) {
                value.setBoard(new ScoreboardContainer(this, preset));
            }
        }

        for (ScoreboardPreset p : presets.values()) {
            if (p.isActive()) {
                p.setActive(false);
                p.getFile().getConfig().set("active", false);
                p.getFile().save();
            }
        }

        preset.setActive(true);
        if (save) {
            preset.getFile().getConfig().set("active", true);
            preset.getFile().save();
        }
    }

    public ScoreboardPreset getPreset(String name) {
        return presets.get(name);
    }

    public void registerPluginBoards(JavaPlugin plugin) {
        File scoreboardsDir = new File(plugin.getDataFolder(), "scoreboards");

        if (!scoreboardsDir.exists()) {
            scoreboardsDir.mkdirs();

            String resourcePath = "scoreboards/";
            try (var stream = plugin.getResource(resourcePath)) {
                System.out.println("Loading scoreboard resources from " + resourcePath);
                if (stream != null) {
                    System.out.println("Loading scoreboard resources from " + resourcePath);
                    try (var jarStream = plugin.getClass().getClassLoader().getResourceAsStream(resourcePath)) {
                        if (jarStream != null) {
                            var zipStream = new java.util.zip.ZipInputStream(jarStream);
                            java.util.zip.ZipEntry entry;
                            while ((entry = zipStream.getNextEntry()) != null) {
                                System.out.println("Loading: " + entry.getName());
                                if (entry.getName().startsWith(resourcePath) && entry.getName().endsWith(".yml")) {
                                    String fileName = entry.getName().substring(resourcePath.length());
                                    File destinationFile = new File(scoreboardsDir, fileName);
                                    if (!destinationFile.exists()) {
                                        try (var resourceStream = plugin.getResource(entry.getName())) {
                                            if (resourceStream != null) {
                                                java.nio.file.Files.copy(resourceStream, destinationFile.toPath());
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (IOException e) {
                plugin.getLogger().severe("Error while copying scoreboard resources: " + e.getMessage());
            }
        }

        File[] yamlFiles = scoreboardsDir.listFiles((dir, name) -> name.endsWith(".yml"));

        if (yamlFiles == null) {
            return;
        }

        for (File file : yamlFiles) {
            try {
                ConfigFile configFile = new ConfigFile(plugin, "scoreboards/" + file.getName());
                this.presets.put(file.getName().replaceAll(".yml", ""), new ScoreboardPreset(configFile));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void loadBoards() {
        File scoreboardsDir = new File(services.getPlugin().getDataFolder(), "scoreboards");

        if (!scoreboardsDir.exists()) {
            //Create main file
            this.presets.put("main", new ScoreboardPreset(new ConfigFile(services.getPlugin(), "scoreboards/main.yml")));
            return;
        }

        File[] yamlFiles = scoreboardsDir.listFiles((dir, name) -> name.endsWith(".yml"));

        if (yamlFiles == null) {
            return;
        }

        for (File file : yamlFiles) {
            try {
                ConfigFile configFile = new ConfigFile(services.getPlugin(), "scoreboards/" + file.getName());
                this.presets.put(file.getName().replaceAll(".yml", ""), new ScoreboardPreset(configFile));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void unload(){
        if (this.scoreboardTask != null)
            this.scoreboardTask.cancel();
        scoreboards.forEach((uuid, profile) -> {
            profile.getFastBoard().delete();
        });
        scoreboards.clear();
    }

    public void reload(){
        presets.values().forEach(ScoreboardPreset::reload);
        scoreboards.values().forEach(p -> p.getBoard().reload());
    }
}
