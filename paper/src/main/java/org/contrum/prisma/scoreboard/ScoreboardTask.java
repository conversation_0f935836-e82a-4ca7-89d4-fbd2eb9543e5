package org.contrum.prisma.scoreboard;
import lombok.RequiredArgsConstructor;
import org.bukkit.scheduler.BukkitRunnable;

@RequiredArgsConstructor
public class ScoreboardTask extends BukkitRunnable {
    private final ScoreboardService scoreboardService;

    @Override
    public void run() {
        try {
            this.scoreboardService.getScoreboards().forEach((uuid, scoreboardProfile) -> {
                scoreboardProfile.tick();
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
