package org.contrum.prisma.scoreboard.line;

import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.contrum.prisma.scoreboard.ScoreboardService;
import org.contrum.prisma.scoreboard.parser.ScoreboardLineParser;

import java.util.List;

public class ScoreboardLine {
    private final ScoreboardService service;
    private final ScoreboardLineParser parser;

    private List<String> rawLines;

    private int tickInterval;

    private int currentIndex = 0;
    private int currentTick;

    public ScoreboardLine(ScoreboardService service, ConfigurationSection section) {
        this.service = service;
        this.parser = service.getParser();

        this.rawLines = section.getStringList("frames");
        this.tickInterval = section.getInt("ticks", 1);
    }

    public ScoreboardLine(ScoreboardService service, List<String> raw, int tickInterval) {
        this.service = service;
        this.parser = service.getParser();
        this.rawLines = raw;
        this.tickInterval = tickInterval;
    }

    public String tickLine(Player player) {
        currentTick++;

        if (currentTick % tickInterval != 0) {
            return parser.parseLine(player, rawLines.get(currentIndex));
        }

        currentIndex++;
        if (currentIndex >= rawLines.size()) {
            currentIndex = 0;
        }
        currentTick = 0;

        return parser.parseLine(player, rawLines.get(currentIndex));
    }
}
