package org.contrum.prisma.scoreboard;

import fr.mrmicky.fastboard.FastBoard;
import lombok.RequiredArgsConstructor;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;

@RequiredArgsConstructor
public class ScoreboardListener implements Listener{
    private final ScoreboardService service;

    @EventHandler
    public void onJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        ScoreboardProfile scoreboardProfile = new ScoreboardProfile(service, player.getUniqueId(), new FastBoard(player), this.service.getActivePreset());
        this.service.getScoreboards().put(player.getUniqueId(), scoreboardProfile);
    }

    @EventHandler
    public void onQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        ScoreboardProfile profile = this.service.getScoreboards().remove(player.getUniqueId());
        if (profile == null) {
            return;
        }
        if (profile.getFastBoard() != null) {
            profile.getFastBoard().delete();
        }
    }
}