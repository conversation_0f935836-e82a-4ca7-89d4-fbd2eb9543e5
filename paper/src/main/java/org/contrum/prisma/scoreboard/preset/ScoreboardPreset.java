package org.contrum.prisma.scoreboard.preset;

import lombok.Getter;
import lombok.Setter;
import org.contrum.prisma.utils.config.ConfigFile;

@Getter @Setter
public class ScoreboardPreset {
    private ConfigFile file;
    private boolean isActive;

    public ScoreboardPreset(ConfigFile file) {
        this.file = file;

        this.isActive = file.getConfig().getBoolean("active");
    }

    public void reload() {
        file = new ConfigFile(file.getPlugin(), file.getFileName());
    }
}
