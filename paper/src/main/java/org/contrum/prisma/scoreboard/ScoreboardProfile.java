package org.contrum.prisma.scoreboard;

import fr.mrmicky.fastboard.FastBoard;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.entity.Player;
import org.contrum.prisma.scoreboard.preset.ScoreboardContainer;
import org.contrum.prisma.scoreboard.preset.ScoreboardPreset;

import java.util.UUID;

@Getter @Setter
public class ScoreboardProfile {
    private final UUID uuid;
    private final FastBoard fastBoard;

    private ScoreboardContainer board;

    public ScoreboardProfile(ScoreboardService service, UUID uuid, FastBoard fastBoard, ScoreboardPreset preset) {
        this.uuid = uuid;
        this.fastBoard = fastBoard;
        this.board = new ScoreboardContainer(service, preset);
    }

    public void tick() {
        Player player = fastBoard.getPlayer();
        if (player == null || board == null) return;

        fastBoard.updateTitle(board.tickTittle(player));
        fastBoard.updateLines(board.tickLines(player));
    }
}