/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by izLoki on 24/05/2024
 * Website: contrum.org
 */

package org.contrum.prisma.customnpcs.npcs.armor;

import lombok.Getter;
import lombok.Setter;
import org.bukkit.Location;
import org.bukkit.configuration.ConfigurationSection;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.customarmor.CustomArmor;
import org.contrum.prisma.customnpcs.CustomNPC;
import org.contrum.prisma.customnpcs.CustomNPCService;
import org.contrum.prisma.customnpcs.npcs.armor.menu.CustomTradeCustomMenu;
import org.contrum.prisma.customnpcs.npcs.armor.menu.CustomTradeMerchantMenu;
import org.contrum.prisma.npc.action.NPCActionAdapter;
import org.contrum.prisma.profile.metadata.ProfileGlobalPaperMetadata;
import org.contrum.prisma.settings.impl.ArmorTradeMode;

@Getter
@Setter
public class NPCArmorTrader extends CustomNPC {
    private String armorName;
    private CustomArmor armor;

    public NPCArmorTrader(CustomNPCService service, Location location, String name, String armorName) {
        super(service, location, name);
        this.armorName = armorName;
        this.armor = service.getServices().getCustomArmorService().getArmor(armorName);

        this.registerActions();
    }

    public NPCArmorTrader(CustomNPCService service, ConfigurationSection section) {
        super(service, section);

        this.armorName = section.getString("ARMOR_NAME");
        this.armor = service.getServices().getCustomArmorService().getArmor(armorName);

        this.registerActions();
    }

    private void registerActions() {
        super.registerInteract(new NPCActionAdapter(NPCActionAdapter.Type.RIGHT, (clicker) -> {
            ProfileGlobalPaperMetadata metadata = this.getServices().getProfileService().getGlobalMetadata(clicker.getUniqueId(), ProfileGlobalPaperMetadata.class);

            if (clicker.isSneaking()) return;
            //Ensure sync
            TaskUtil.run(getServices().getPlugin(), () -> {
                if (metadata.getSettings().getSetting(ArmorTradeMode.class).getValue().equals(ArmorTradeMode.Value.CUSTOM)) {
                    new CustomTradeCustomMenu(getServices(), armor).open(clicker);
                } else {
                    new CustomTradeMerchantMenu(getServices(), clicker, armor);
                }
            });
        }));
    }

    @Override
    public boolean serialize(ConfigurationSection section) {
        boolean exist = super.serialize(section);
        if (exist) {
            String uuid = this.getNpc().getUUID().toString();
            section.set(uuid + ".TYPE", "ARMOR");
            section.set(uuid + ".ARMOR_NAME", this.getArmorName());
        }
        return exist;
    }
}
