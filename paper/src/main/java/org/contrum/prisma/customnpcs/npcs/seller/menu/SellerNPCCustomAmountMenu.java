/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by izLoki on 24/05/2024
 * Website: contrum.org
 */

package org.contrum.prisma.customnpcs.npcs.seller.menu;

import net.kyori.adventure.text.Component;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.inventory.InventoryType;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customarmor.CustomArmorService;
import org.contrum.prisma.customnpcs.npcs.seller.SellerItem;
import org.contrum.prisma.economy.EconomyService;
import org.contrum.prisma.events.PlayerSellerNPCPurchaseEvent;
import org.contrum.prisma.profile.currency.Currency;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.systems.impl.battlepass.BattlePassSystem;
import org.contrum.prisma.systems.impl.battlepass.prestige.BattlePassPrestigeManager;
import org.contrum.prisma.utils.CCP;
import org.contrum.prisma.utils.InventoryUtils;
import org.contrum.prisma.utils.ItemBuilder1_20;

import java.util.ArrayList;
import java.util.List;

public class SellerNPCCustomAmountMenu implements Listener {
    private final PaperServices services;
    private final BattlePassSystem passSystem;
    private final CustomArmorService customArmorManager;

    private final Player player;
    private final ProfilePaperMetadata metadata;
    private final List<SellerItem> items;
    private final int i;
    private Inventory inventory;


    public SellerNPCCustomAmountMenu(PaperServices services, Player player, List<SellerItem> items, int i) {
        this.services = services;
        this.passSystem = services.getSystemService().getSystem(BattlePassSystem.class);
        this.customArmorManager = services.getCustomArmorService();
        this.player = player;
        this.metadata = services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
        this.items = items;
        this.i = i;

        open();

        Bukkit.getPluginManager().registerEvents(this, services.getPlugin());
    }

    private void open() {
        this.inventory = Bukkit.createInventory(null, 45, "Tienda");
        update();
        player.openInventory(inventory);
    }

    private void update() {
        ItemStack glass = new ItemStack(Material.BLACK_STAINED_GLASS_PANE);
        ItemStack bed = new ItemBuilder1_20(Material.RED_BED).name(CCP.translateAndFront("&c&lvolver")).build();

        for (int i = 0; i < inventory.getSize(); i++) {
            inventory.setItem(i, glass);
        }

        long cost = items.get(this.i).getCost();
        long newCost = passSystem.getPrestigeManager().applyPrestigePriceModifier(player, cost, BattlePassPrestigeManager.DiscountType.SELLER);

        List<String> lore = new ArrayList<>();
        lore.add("&f");
        lore.add(this.formatPrice(metadata, cost, newCost, 1));
        lore.add("&f");
        lore.add("&cClick para comprar.");

        ItemStack it = items.get(this.i).getItem().clone();
        String itemName = it.getDisplayName();
        if (itemName.isEmpty()) itemName = it.getI18NDisplayName();
        it.setDisplayName(CCP.translate("&7x1 &f" + itemName));
        it.setLore(CCP.translateAndFront(lore));

        ItemStack it5 = it.clone();
        List<Component> it5Lore = it5.lore();
        it5Lore.set(1, Component.text(CCP.translateAndFront(this.formatPrice(metadata, cost, newCost, 5))));
        it5.lore(it5Lore);
        it5.setDisplayName(CCP.translate("&7x5 &f" + itemName));
        it5.setAmount(5);

        ItemStack it10 = it.clone();
        List<Component> it10Lore = it10.lore();
        it10Lore.set(1, Component.text(CCP.translateAndFront(this.formatPrice(metadata, cost, newCost, 10))));
        it10.lore(it10Lore);
        it10.setDisplayName(CCP.translate("&7x10 &f" + itemName));
        it10.setAmount(10);

        ItemStack it20 = it.clone();
        List<Component> it20Lore = it20.lore();
        it20Lore.set(1, Component.text(CCP.translateAndFront(this.formatPrice(metadata, cost, newCost, 20))));
        it20.lore(it20Lore);
        it20.setDisplayName(CCP.translate("&7x20 &f" + itemName));
        it20.setAmount(20);

        ItemStack it16 = it.clone();
        List<Component> it16Lore = it16.lore();
        it16Lore.set(1, Component.text(CCP.translateAndFront(this.formatPrice(metadata, cost, newCost, 16))));
        it16.lore(it16Lore);
        it16.setDisplayName(CCP.translate("&7x16 &f" + itemName));
        it16.setAmount(16);

        ItemStack it32 = it.clone();
        List<Component> it32Lore = it32.lore();
        it32Lore.set(1, Component.text(CCP.translateAndFront(this.formatPrice(metadata, cost, newCost, 32))));
        it32.lore(it32Lore);
        it32.setDisplayName(CCP.translate("&7x32 &f" + itemName));
        it32.setAmount(32);

        ItemStack it64 = it.clone();
        List<Component> it64Lore = it64.lore();
        it64Lore.set(1, Component.text(CCP.translateAndFront(this.formatPrice(metadata, cost, newCost, 64))));
        it64.lore(it64Lore);
        it64.setDisplayName(CCP.translate("&7x64 &f" + itemName));
        it64.setAmount(64);

        //10
        inventory.setItem(10, it);
        inventory.setItem(12, it5);
        inventory.setItem(14, it10);
        inventory.setItem(16, it20);

        inventory.setItem(29, it16);
        inventory.setItem(31, it32);
        inventory.setItem(33, it64);


        //Bed
        inventory.setItem(36, bed);
    }

    private void buyItem(ItemStack item, int amount) {
        SellerItem sellerItem = items.get(this.i);
        long oldCost = sellerItem.getCost() * amount;
        long cost = passSystem.getPrestigeManager().applyPrestigePriceModifier(player, oldCost, BattlePassPrestigeManager.DiscountType.SELLER);

        int maxStackSize = item.getMaxStackSize();
        int slots = (int) Math.ceil((double) amount / maxStackSize);

        if (InventoryUtils.getEmptySlotsCount(player.getInventory()) < slots) {
            player.sendMessage(CCP.translateAndFront("&cno tienes suficiente espacio en el inventario!"));
            player.playSound(player.getLocation(), Sound.ENTITY_IRON_GOLEM_REPAIR, 0.3F, 1);
            return;
        }

        long coins = metadata.getCurrency(Currency.COINS);
        if (coins < cost) {
            long requiredCoins = cost - coins;
            player.sendMessage(CCP.translateAndFront("&cNecesitas &6" + EconomyService.formatCoins(metadata, cost - coins) + " coins &cmás para compras esto."));
            return;
        }
        if (!metadata.withdrawCurrency(Currency.COINS, cost)) {
            player.sendMessage(CCP.translateAndFront("&cHa ocurrido un error."));
            return;
        }

        //Add item
        ItemStack i = items.get(this.i).getItem().clone();

        int addedAmount = amount;
        while (addedAmount > 0) {
            int newAmount = Math.min(addedAmount, maxStackSize);
            ItemStack itemToAdd = i.clone();
            itemToAdd.setAmount(newAmount);

            player.getInventory().addItem(itemToAdd);
            addedAmount -= newAmount;
        }
        String itemName = i.getDisplayName();
        if (itemName.isEmpty()) itemName = i.getI18NDisplayName();
        player.sendMessage(CCP.translateAndFront("&aHas comprado &ex" + amount + " " + itemName + " &apor &6" + EconomyService.formatCoins(metadata, cost) + " coins!"));

        player.playSound(player.getLocation(), Sound.ENTITY_ARROW_HIT_PLAYER, 1, 2);
        new PlayerSellerNPCPurchaseEvent(player, i, cost).callEvent();
    }

    @EventHandler
    public void inventoryClick(InventoryClickEvent event) {
        if (event.getClickedInventory() != null && event.getWhoClicked() instanceof Player p) {
            if (p.getUniqueId().equals(player.getUniqueId()) && event.getInventory().hashCode() == this.inventory.hashCode()) {
                event.setCancelled(true);

                ItemStack item = event.getCurrentItem();
                if (item == null || item.getType().equals(Material.AIR) || item.getType().equals(Material.BLACK_STAINED_GLASS_PANE))
                    return;
                //inventory side
                if (!event.getClickedInventory().getType().equals(InventoryType.PLAYER)) {
                    //back
                    if (item.getType().equals(Material.RED_BED)) {
                        new SellerNPCMenu(services, player, items);
                        return;
                    }
                    //instant buy one
                    buyItem(item, item.getAmount());
                }
            }
        }
    }

    @EventHandler
    public void playerCloseInventory(InventoryCloseEvent event) {
        if (player.getUniqueId().equals(event.getPlayer().getUniqueId())) {
            HandlerList.unregisterAll(this);
        }
    }

    private String formatPrice(ProfilePaperMetadata metadata, long cost, long newCost, int costMultiplier) {
        String loreFormat;
        if (newCost != cost) {
            if (newCost < cost) {
                loreFormat = "&fPrecio: &6&m" + EconomyService.formatCoins(metadata, cost * costMultiplier) + "&a " + EconomyService.formatCoins(metadata, newCost * costMultiplier);
            } else {
                loreFormat = "&fPrecio: &6&m" + EconomyService.formatCoins(metadata, cost * costMultiplier) + "&c " + EconomyService.formatCoins(metadata, newCost * costMultiplier);
            }
        } else {
            loreFormat = "&fPrecio: &6" + EconomyService.formatCoins(metadata, cost * costMultiplier);
        }

        return loreFormat;
    }
}
