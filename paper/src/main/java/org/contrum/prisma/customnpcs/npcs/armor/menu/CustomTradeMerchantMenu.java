/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by izLoki on 21/05/2024
 * Website: contrum.org
 */

package org.contrum.prisma.customnpcs.npcs.armor.menu;

import io.papermc.paper.event.player.PlayerPurchaseEvent;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.*;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customarmor.CustomArmor;
import org.contrum.prisma.customarmor.CustomArmorService;
import org.contrum.prisma.events.PlayerUpgradeArmorEvent;
import org.contrum.prisma.systems.impl.battlepass.BattlePassSystem;
import org.contrum.prisma.systems.impl.battlepass.prestige.BattlePassPrestigeManager;

import java.util.ArrayList;
import java.util.List;

public class CustomTradeMerchantMenu implements Listener {
    private final PaperServices services;
    private final CustomArmorService customArmorManager;
    private final BattlePassSystem passSystem;

    private final Player player;
    private final CustomArmor requiredArmor;
    private final CustomArmor armor;
    private Merchant merchant;

    private Inventory inventory;


    public CustomTradeMerchantMenu(PaperServices services, Player player, CustomArmor armor) {
        this.services = services;
        this.passSystem = services.getSystemService().getSystem(BattlePassSystem.class);
        this.customArmorManager = services.getCustomArmorService();
        this.player = player;
        this.armor = armor;
        this.requiredArmor = customArmorManager.getPreviousArmor(armor.getName());
        open();
        Bukkit.getPluginManager().registerEvents(this, services.getPlugin());
    }

    private void open(){
        this.merchant = Bukkit.createMerchant(armor.getFormatDisplayName());

        List<MerchantRecipe> recipes = new ArrayList<>();

        List<ItemStack> previousSet = customArmorManager.getSet(requiredArmor);
        int i = 0;
        for (ItemStack item : customArmorManager.getSet(armor)) {
            MerchantRecipe recipe = new MerchantRecipe(item, 10000);
            recipe.addIngredient(previousSet.get(i));
            int cost = armor.getCompressRequired();
            int newCost = (int) passSystem.getPrestigeManager().applyPrestigePriceModifier(player, cost, BattlePassPrestigeManager.DiscountType.ARMOR);
            recipe.addIngredient(customArmorManager.getArmorCompress(armor, newCost));

            recipes.add(recipe);
            i++;
        }

        merchant.setRecipes(recipes);
        InventoryView inventoryView = player.openMerchant(merchant, true);
        this.inventory = inventoryView.getTopInventory();
    }

    @EventHandler
    public void onTrade(PlayerPurchaseEvent event) {
        if (event.getPlayer().getUniqueId().equals(player.getUniqueId())) {
            new PlayerUpgradeArmorEvent(player, requiredArmor, armor, event.getTrade().getResult()).callEvent();
        }
    }

    @EventHandler
    public void close(InventoryCloseEvent event) {
        if (event.getInventory().hashCode() == this.inventory.hashCode()) {
            HandlerList.unregisterAll(this);
        }
    }
}
