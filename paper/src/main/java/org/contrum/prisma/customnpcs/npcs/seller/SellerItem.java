/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by izLoki on 24/05/2024
 * Website: contrum.org
 */

package org.contrum.prisma.customnpcs.npcs.seller;

import lombok.Getter;
import lombok.Setter;
import org.bukkit.inventory.ItemStack;
@Getter
@Setter
public class SellerItem {
    private String UUID;
    private ItemStack item;
    private long cost;

    public SellerItem(ItemStack item, long cost){
        this.item = item;
        this.cost = cost;
    }
}
