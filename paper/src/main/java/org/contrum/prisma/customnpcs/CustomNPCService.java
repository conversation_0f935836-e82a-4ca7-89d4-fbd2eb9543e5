/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by izLoki on 24/05/2024
 * Website: contrum.org
 */

package org.contrum.prisma.customnpcs;

import lombok.Getter;
import org.bukkit.Location;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Entity;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customarmor.CustomArmorService;
import org.contrum.prisma.customnpcs.npcs.armor.NPCArmorTrader;
import org.contrum.prisma.customnpcs.npcs.compress.NPCCompressTrader;
import org.contrum.prisma.customnpcs.npcs.seller.NPCSeller;
import org.contrum.prisma.npc.NPCAdapter;
import org.contrum.prisma.utils.PlayerUtils;
import org.contrum.prisma.utils.config.ConfigFile;

import java.util.*;
import java.util.stream.Collectors;


@Getter
public class CustomNPCService {
    private final PaperServices services;
    private final CustomArmorService armorService;

    private ConfigFile config;

    private final HashMap<UUID, CustomNPC> NPCMap = new HashMap<>();
    private boolean loaded = false;

    public CustomNPCService(PaperServices services) {
        this.services = services;
        this.armorService = services.getCustomArmorService();

        config = new ConfigFile(services.getPlugin(), "npcs.yml");
        config.save();

        ConfigurationSection tradersConfig = config.getConfig().getConfigurationSection("NPCS");
        if (tradersConfig != null) {
            Set<String> armorList = tradersConfig.getKeys(false);

            for (String uuid : armorList) {
                ConfigurationSection armorSection = tradersConfig.getConfigurationSection(uuid);
                this.registerNPC(armorSection);
            }
        }

        //Respawn removed npcs
        TaskUtil.runLater(services.getPlugin(), ()  -> {
            NPCMap.forEach((uuid, npc) -> {
                npc.makeSureExist();
            });
            loaded = true;
        }, 20L*10);
    }

    public void shutdown() {
        this.save();
    }

    public void save() {
        if (!loaded) return;
        config.getConfig().set("NPCS", null);
        ConfigurationSection section = config.getConfig().getConfigurationSection("NPCS");
        if (section == null) section = config.getConfig().createSection("NPCS");

        for (Map.Entry<UUID, CustomNPC> entry : NPCMap.entrySet()) {
            CustomNPC npc = entry.getValue();

            if (!npc.isPersistent()) continue;

            npc.serialize(section);
        }
        config.save();
    }

    public void registerNPC(ConfigurationSection section) {
        String type = section.getString("TYPE", "DUMMY");

        CustomNPC npc = null;

        switch (type) {
            case "SELLER":
                npc = new NPCSeller(this, section);
                break;
            case "COMPRESS":
                npc = new NPCCompressTrader(this, section);
                break;
            case "ARMOR":
                npc = new NPCArmorTrader(this, section);
                break;
            default:
                break;
        }

        if (npc == null) return;

        this.registerNPC(UUID.fromString(section.getName()), npc, false);
    }

    public CustomNPC getNPC(UUID uuid) {
        return NPCMap.get(uuid);
    }

    public Collection<CustomNPC> getNPCs() {
        return NPCMap.values();
    }

    public <T extends CustomNPC> Set<T> getNPCs(Class<T> clazz) {
        return NPCMap.values().stream().filter(clazz::isInstance).map(clazz::cast).collect(Collectors.toSet());
    }

    public void registerNPC(UUID uuid, CustomNPC npc, boolean save) {
        NPCMap.put(uuid, npc);
        if (save)
            this.save();
    }

    public NPCArmorTrader spawnNPCTrader(Location location, String name, String armorName, boolean save) {
        NPCArmorTrader trader = new NPCArmorTrader(this, location, name, armorName);
        trader.create();
        this.registerNPC(trader.getUniqueId(), trader, save);
        return trader;
    }

    public NPCSeller spawnNPCSeller(Location location, String name, boolean save) {
        NPCSeller seller = new NPCSeller(this, location, name);
        seller.create();
        this.registerNPC(seller.getUniqueId(), seller, save);
        return seller;
    }

    public NPCCompressTrader spawnNPCCompress(Location location, String name, String armorName, boolean save) {
        NPCCompressTrader npc = new NPCCompressTrader(this, location, name, armorName);
        npc.create();
        this.registerNPC(npc.getUniqueId(), npc, save);
        return npc;
    }

    public boolean removeNPC(Entity entity) {
        if (PlayerUtils.isNPC(entity)) {
            NPCAdapter npc = services.getNpcAdapterService().getNpc(entity);
            return removeNPC(npc);
        }
        return false;
    }

    public boolean removeNPC(NPCAdapter npc) {
        if (npc == null) return false;
        for (Map.Entry<UUID, CustomNPC> entry : NPCMap.entrySet()) {
            String i = entry.getKey().toString();
            CustomNPC customNPC = entry.getValue();
            if (customNPC.getUniqueId() != null && customNPC.getUniqueId().equals(npc.getUUID())) {
                this.removeNPC(customNPC);
                return true;
            }
        }
        return false;
    }

    public void removeNPC(CustomNPC customNPC) {
        if (customNPC.getUniqueId() != null) {
            NPCMap.remove(customNPC.getUniqueId());
            if (customNPC.isPersistent()) {
                config.getConfig().set("NPCS." + customNPC.getUniqueId(), null);
                config.save();
            }
        }

        customNPC.getNpc().destroy();
    }
}
