/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by izLoki on 24/05/2024
 * Website: contrum.org
 */

package org.contrum.prisma.customnpcs;

import lombok.Getter;
import lombok.Setter;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.EntityType;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customnpcs.npcs.seller.NPCSeller;
import org.contrum.prisma.npc.NPCAdapter;
import org.contrum.prisma.npc.NPCAdapterService;
import org.contrum.prisma.npc.action.NPCActionAdapter;

import javax.annotation.Nullable;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

@Getter
@Setter
public abstract class CustomNPC {
    private final PaperServices services;
    private final CustomNPCService customNPCService;
    private final NPCAdapterService npcService;

    private NPCAdapter npc;
    private UUID uuid;
    private Location location;
    private String name = "";
    private String skin = "";

    private final Set<NPCActionAdapter> actions = new HashSet<>();
    private boolean persistent = true;

    public CustomNPC(CustomNPCService service, Location location, String name) {
        this.services = service.getServices();
        this.npcService = service.getServices().getNpcAdapterService();
        this.customNPCService = service;
        this.location = location;
        this.name = name;

        this.registerActions();
    }

    public CustomNPC(CustomNPCService service, ConfigurationSection section) {
        this.services = service.getServices();
        this.npcService = service.getServices().getNpcAdapterService();
        this.customNPCService = service;

        this.location = section.getLocation("LOCATION");
        this.name = section.getString("NAME", "");
        this.skin = section.getString("SKIN");

        String uuidString = section.getString("UUID", "");
        if (!uuidString.isEmpty()) {
            this.uuid = UUID.fromString(uuidString);

            this.npc = npcService.getNpc(uuid);

            //Save npc data if isn't saved
            if (npc != null) {
                this.location = npc.getLocation();
                this.name = npc.getName();
                this.skin = npc.getSkinUrl();
            }
        }

        this.registerActions();
    }

    private void registerActions() {
        this.registerInteract(new NPCActionAdapter(NPCActionAdapter.Type.LEFT, player -> {
            if (player.hasMetadata("npcRemove")) {
                customNPCService.removeNPC(this);
            } else if (player.hasMetadata("npcSetMenu")) {
                if (this instanceof NPCSeller seller) {
                    seller.setShowInMenu(!seller.isShowInMenu());
                    player.sendMessage(CC.translate("&eNew show in menu value: &d" + seller.isShowInMenu()));
                } else {
                    player.sendMessage(CC.translate("&cTarget NPC is not a seller!"));
                }
            }
        }));
    }

    @Nullable
    public UUID getUniqueId() {
        return npc == null ? null : npc.getUUID();
    }

    public void makeSureExist() {
        if (npc == null) {
            if (uuid != null) {
                this.npc = npcService.getNpc(uuid);
            }

            if (npc == null)
                this.create();
            else {
                actions.forEach(this::registerInteract);
                this.location = npc.getLocation();
                this.name = npc.getName();
                this.skin = npc.getSkinUrl();
            }
        }
    }

    public void create() {
        if (name == null)  {
            return;
        };

        if (uuid != null) {
            npc = npcService.createNPC(EntityType.PLAYER, uuid, name, location);
        } else {
            npc = npcService.createNPC(EntityType.PLAYER, name, location);
        }

        if (!skin.isEmpty()) {
            npcService.setSkinUrl(npc, skin);
        }

        this.uuid = npc.getUUID();
        this.actions.forEach(npc::registerAction);
        npc.setLookClose(true);

        System.out.println("NPC Created! " + this.getUniqueId());
    }

    public void setSkin(String name) {
        npc.setSkin(name);
    }


    public void registerInteract(NPCActionAdapter action) {
        actions.add(action);
        if (npc != null)
            npc.registerAction(action);
    }

    public boolean serialize(ConfigurationSection section) {
        UUID uuid = this.getUniqueId();
        if (uuid == null) {
            Bukkit.getLogger().severe("Error saving a custom NPC!");
            return false;
        }

        section.set(uuid.toString() + ".TYPE", "DUMMY");
        section.set(uuid.toString() + ".LOCATION", location);
        section.set(uuid.toString() + ".SKIN", skin);
        section.set(uuid.toString() + ".NAME", name != null ? name : "");
        section.set(uuid.toString() + ".UUID", uuid.toString());
        return true;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || this.getUniqueId() == null || getClass() != obj.getClass()) return false;

        CustomNPC that = (CustomNPC) obj;
        return this.getUniqueId().equals(that.getUniqueId());
    }
}
