package org.contrum.prisma.customnpcs.npcs.compress.menu;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.Listener;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.Merchant;
import org.bukkit.inventory.MerchantRecipe;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customarmor.CustomArmor;
import org.contrum.prisma.customarmor.CustomArmorService;
import org.contrum.prisma.systems.impl.battlepass.BattlePassSystem;
import org.contrum.prisma.systems.impl.battlepass.prestige.BattlePassPrestigeManager;

import java.util.ArrayList;
import java.util.List;

public class CompressNPCMenu implements Listener {
    private final PaperServices services;
    private final BattlePassSystem passSystem;
    private final CustomArmorService customArmorManager;

    private final Player player;
    private final CustomArmor armor;
    private Merchant merchant;


    public CompressNPCMenu(PaperServices services, Player player, CustomArmor armor) {
        this.services = services;
        this.customArmorManager = services.getCustomArmorService();
        this.player = player;
        this.armor = armor;
        this.passSystem = services.getSystemService().getSystem(BattlePassSystem.class);
        Bukkit.getPluginManager().registerEvents(this, services.getPlugin());
        open();
    }

    private void open(){
        this.merchant = Bukkit.createMerchant(armor.getFormatDisplayName());

        MerchantRecipe recipe1 = new MerchantRecipe(customArmorManager.getArmorCompress(armor, 1), 10000);
        int cost = armor.getCompressCost();
        int newCost = (int) passSystem.getPrestigeManager().applyPrestigePriceModifier(player, cost, BattlePassPrestigeManager.DiscountType.COMPRESS);
        recipe1.addIngredient(customArmorManager.getArmorBlock(armor, cost));

        MerchantRecipe recipe2 = new MerchantRecipe(customArmorManager.getArmorCompress(armor, 2), 10000);
        if ((cost *2) - 14 <= 64){
            recipe2.addIngredient(customArmorManager.getArmorBlock(armor, cost *2));
        } else {
            recipe2.addIngredient(customArmorManager.getArmorBlock(armor, cost -14));
            recipe2.addIngredient(customArmorManager.getArmorBlock(armor, cost));
        }

        MerchantRecipe recipe3 = null;
        if (armor.isPreviousCompressUpgrade()) {
            CustomArmor previousArmor = customArmorManager.getPreviousArmor(armor);
            if (previousArmor == null) return;

            recipe3 = new MerchantRecipe(customArmorManager.getArmorCompress(armor, 1), 10000);
            recipe3.addIngredient(customArmorManager.getArmorCompress(previousArmor, armor.getPreviousCompressUpgradeCost()));
        }

        if (newCost != cost) {
            recipe1.setSpecialPrice(newCost - cost);
            recipe2.setSpecialPrice(newCost - cost);
        }

        List<MerchantRecipe> recipes = new ArrayList<>();
        recipes.add(recipe1);
        recipes.add(recipe2);

        if (recipe3 != null)
            recipes.add(recipe3);

        merchant.setRecipes(recipes);
        player.openMerchant(merchant, true);
    }
}
