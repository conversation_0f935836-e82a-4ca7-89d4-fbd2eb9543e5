/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by izLoki on 24/05/2024
 * Website: contrum.org
 */

package org.contrum.prisma.customnpcs.npcs.seller.menu;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.inventory.InventoryType;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.persistence.PersistentDataType;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customnpcs.npcs.seller.SellerItem;
import org.contrum.prisma.economy.EconomyService;
import org.contrum.prisma.events.PlayerSellerNPCPurchaseEvent;
import org.contrum.prisma.profile.currency.Currency;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.systems.impl.battlepass.BattlePassSystem;
import org.contrum.prisma.systems.impl.battlepass.prestige.BattlePassPrestigeManager;
import org.contrum.prisma.utils.CCP;
import org.contrum.prisma.utils.InventoryUtils;
import org.contrum.prisma.utils.ItemBuilder1_20;
import org.contrum.prisma.utils.NBTUtil;

import java.util.ArrayList;
import java.util.List;

public class SellerNPCMenu implements Listener {
    private final PaperServices services;
    private final BattlePassSystem passSystem;
    private final Player player;
    private final ProfilePaperMetadata metadata;
    private final List<SellerItem> items;
    private Inventory inventory;


    public SellerNPCMenu(PaperServices services, Player player, List<SellerItem> items) {
        this.services = services;
        this.player = player;
        this.metadata = services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
        this.items = items;
        this.passSystem = services.getSystemService().getSystem(BattlePassSystem.class);

        Bukkit.getPluginManager().registerEvents(this, services.getPlugin());
        open();
    }

    private void open(){
        this.inventory = Bukkit.createInventory(null, 45, "Tienda");
        update();
        player.openInventory(inventory);
    }

    private void update(){
        ItemStack book = new ItemBuilder1_20(Material.BOOK).name(CCP.translateAndFront("&e&ltienda")).setFormattedLore(List.of("&b| &e¡tienda virtual! &fcompra &cequipamiento &fy", "&b| &cobjetos especiales &fcon tus &6coins.", "", "&b| &fcoloca tu mouse sobre un item para", "&b| &fpara conocer su &6precio &fy haz &cclic", "&b| &cpara comprarlo!")).hideInformation().build();
        ItemStack glass = new ItemStack(Material.BLACK_STAINED_GLASS_PANE);

        for (int i = 0; i < inventory.getSize() ; i++){
            if (i > 9 && i < 17 || i > 18 && i < 26 || i > 27 && i < 35) {
                inventory.setItem(i, null);
                continue;
            };
            inventory.setItem(i, glass);
        }

        for (int i = 0; i < items.size(); i++) {
            SellerItem npcItem = items.get(i);
            if (npcItem == null || npcItem.getItem() == null) {
                continue;
            }
            ItemStack item = npcItem.getItem().clone();
            List<String> lore = item.getLore();
            if (lore == null) lore = new ArrayList<>();
            lore.add(CCP.translate("&f"));
            long cost = npcItem.getCost();
            long newCost = passSystem.getPrestigeManager().applyPrestigePriceModifier(player, cost, BattlePassPrestigeManager.DiscountType.SELLER);
            if (newCost != cost) {
                if (newCost < cost) {
                    lore.add(CCP.translateAndFront("&fPrecio: &6&m" + EconomyService.formatCoins(metadata, cost) + "&a " + EconomyService.formatCoins(metadata, newCost)));
                } else {
                    lore.add(CCP.translateAndFront("&fPrecio: &6&m" + EconomyService.formatCoins(metadata, cost) + "&c " + EconomyService.formatCoins(metadata, newCost)));
                }
            } else {
                lore.add(CCP.translateAndFront("&fPrecio: &6" + EconomyService.formatCoins(metadata, cost)));
            }
            lore.add(CCP.translate("&f"));
            lore.add(CCP.translateAndFront("&eClick izquierdo para cambiar la cantidad."));
            lore.add(CCP.translateAndFront("&cClick derecho para comprar."));
            item.setLore(lore);
            NBTUtil.setNBT(item, "i", PersistentDataType.INTEGER, i);
            NBTUtil.setNBT(item, "price", PersistentDataType.LONG, newCost);
            inventory.setItem(inventory.firstEmpty(), item);
        }
        inventory.setItem(4, book);
    }


    private long getItemCost(ItemStack item){
        return NBTUtil.getNBT(item.getItemMeta(), "price", PersistentDataType.LONG, Long.MAX_VALUE);
    }

    private int getItemID(ItemStack item){
        return NBTUtil.getNBT(item.getItemMeta(), "i", PersistentDataType.INTEGER, Integer.MAX_VALUE);
    }

    private static int getEmptySlots(org.bukkit.inventory.PlayerInventory inventory) {
        int count = 0;
        for (ItemStack itemStack : inventory.getContents()) {
            if (itemStack == null || itemStack.getType() == Material.AIR) {
                count++;
            }
        }
        return count -5;
    }

    private void buyItem(ItemStack item, int amount){
        long oldCost = getItemCost(item) * amount;
        long cost = passSystem.getPrestigeManager().applyPrestigePriceModifier(player, oldCost, BattlePassPrestigeManager.DiscountType.SELLER);

        int maxStackSize = item.getMaxStackSize();
        int slots = (int) Math.ceil((double) amount / maxStackSize);

        if (InventoryUtils.getEmptySlotsCount(player.getInventory()) < slots){
            player.sendMessage(CCP.translateAndFront("&cno tienes suficiente espacio en el inventario!"));
            player.playSound(player.getLocation(), Sound.ENTITY_IRON_GOLEM_REPAIR, 0.3F, 1);
            return;
        }

        long coins = metadata.getCurrency(Currency.COINS);

        if (coins < cost){
            player.sendMessage(CCP.translateAndFront("&cNecesitas &6" + EconomyService.formatCoins(metadata, cost - coins) + " coins &cmás para compras esto."));
            return;
        }
        if (!metadata.withdrawCurrency(Currency.COINS, cost)){
            player.sendMessage(CCP.translateAndFront("&cHa ocurrido un error."));
            return;
        }

        //Add item
        ItemStack i = items.get(getItemID(item)).getItem().clone();

        int addedAmount = amount;
        while (addedAmount > 0) {
            int newAmount = Math.min(addedAmount, maxStackSize);
            ItemStack itemToAdd = i.clone();
            itemToAdd.setAmount(newAmount);

            player.getInventory().addItem(itemToAdd);
            addedAmount -= newAmount;
        }
        String itemName = i.getDisplayName();
        if (itemName.isEmpty()) itemName = i.getI18NDisplayName();
        player.sendMessage(CCP.translateAndFront("&aHas comprado &ex" + amount + " " + itemName + " &apor &6" + EconomyService.formatCoins(metadata, cost) + " coins!"));

        player.playSound(player.getLocation(), Sound.ENTITY_ARROW_HIT_PLAYER, 1, 2);
        new PlayerSellerNPCPurchaseEvent(player, i, cost).callEvent();
    }

    @EventHandler
    public void inventoryClick(InventoryClickEvent event){
        if (event.getClickedInventory() != null && event.getWhoClicked() instanceof Player p){
            if (p.getUniqueId().equals(player.getUniqueId())  && event.getInventory().hashCode() == this.inventory.hashCode()){
                event.setCancelled(true);

                ItemStack item = event.getCurrentItem();
                if (item == null || item.getType().equals(Material.AIR) || item.getType().equals(Material.BLACK_STAINED_GLASS_PANE) || item.getType().equals(Material.BOOK)) return;
                //inventory side
                if (!event.getClickedInventory().getType().equals(InventoryType.PLAYER)) {
                    //instant buy one
                    if (event.getClick().equals(ClickType.RIGHT) || event.getClick().equals(ClickType.SHIFT_RIGHT)){
                        buyItem(item, 1);
                    }
                    if (event.getClick().equals(ClickType.LEFT) || event.getClick().equals(ClickType.SHIFT_LEFT)){
                        new SellerNPCCustomAmountMenu(services, player, items, getItemID(item));
                    }
                }
            }
        }
    }

    @EventHandler
    public void playerCloseInventory(InventoryCloseEvent event){
        if (player.getUniqueId().equals(event.getPlayer().getUniqueId()) && event.getInventory().hashCode() == this.inventory.hashCode()){
            HandlerList.unregisterAll(this);
        }
    }

}
