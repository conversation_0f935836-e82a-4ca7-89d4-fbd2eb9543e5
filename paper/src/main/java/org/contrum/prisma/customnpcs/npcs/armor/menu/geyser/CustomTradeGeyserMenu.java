package org.contrum.prisma.customnpcs.npcs.armor.menu.geyser;

import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customarmor.CustomArmor;
import org.contrum.prisma.customarmor.CustomArmorService;
import org.contrum.prisma.events.PlayerUpgradeArmorEvent;
import org.contrum.prisma.systems.impl.battlepass.BattlePassSystem;
import org.contrum.prisma.systems.impl.battlepass.prestige.BattlePassPrestigeManager;
import org.contrum.prisma.utils.InventoryUtils;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.prisma.utils.menus.geyser.CoreGeyserSimpleFormMenu;
import org.contrum.prisma.utils.menus.geyser.wrappers.simpleform.ButtonWrapper;
import org.contrum.prisma.utils.menus.geyser.wrappers.simpleform.SimpleFormWrapper;
import org.contrum.tritosa.Translator;
import org.contrum.tritosa.placeholder.LocalPlaceholders;
import org.geysermc.cumulus.util.FormImage;

import java.util.ArrayList;
import java.util.List;

public class CustomTradeGeyserMenu extends CoreGeyserSimpleFormMenu {
    private final PaperServices services;
    private final CustomArmorService customArmorService;
    private final Translator translator;

    private final CustomArmor requiredArmor;
    private final CustomArmor armor;

    private int compressCost;

    public CustomTradeGeyserMenu(PaperServices services, CustomArmor armor) {
        this.services = services;
        this.customArmorService = services.getCustomArmorService();
        this.translator = services.getTranslator();
        this.armor = armor;
        this.requiredArmor = services.getCustomArmorService().getPreviousArmor(armor);

        this.compressCost = armor.getCompressRequired();
    }

    @Override
    protected SimpleFormWrapper createForm(Player player, SimpleFormWrapper form) {
        BattlePassSystem system = services.getSystemService().getSystem(BattlePassSystem.class);
        long newCost = system.getPrestigeManager().applyPrestigePriceModifier(player, compressCost, BattlePassPrestigeManager.DiscountType.ARMOR);
        if (compressCost != newCost)
            this.compressCost = (int) newCost;

        String costText = "( " + compressCost + " compress )";

        List<ButtonWrapper> buttons = new ArrayList<>();
        List<ItemStack> set = customArmorService.getSet(armor);
        buttons.add(
                ButtonWrapper.of(form, "Casco " + costText,
                        FormImage.Type.URL,
                        "https://mc-heads.net/head/"+armor.getSkullSkin().replaceAll("http://textures.minecraft.net/texture/", "")+"/200.png"
                ).onClick(() -> {
                    this.performTrade(player, set.get(0));
                })
        );

        buttons.add(
                ButtonWrapper.of(form, "Pechera " + costText,
                        FormImage.Type.PATH,
                        "textures/items/leather_chestplate.png"
                ).onClick(() -> {
                    this.performTrade(player, set.get(1));
                })
        );

        buttons.add(
                ButtonWrapper.of(form, "Pantalones " + costText,
                        FormImage.Type.PATH,
                        "textures/items/leather_leggings.tga"
                ).onClick(() -> {
                    this.performTrade(player, set.get(2));
                })
        );

        buttons.add(
                ButtonWrapper.of(form, "Botas " + costText,
                        FormImage.Type.PATH,
                        "textures/items/leather_boots.tga"
                ).onClick(() -> {
                    this.performTrade(player, set.get(3));
                })
        );

        buttons.add(
                ButtonWrapper.of(form, "Espada " + costText,
                        FormImage.Type.PATH,
                        "textures/items/diamond_sword.png"
                ).onClick(() -> {
                    this.performTrade(player, set.get(4));
                })
        );

        buttons.add(
                ButtonWrapper.of(form, "Pico " + costText,
                        FormImage.Type.PATH,
                        "textures/items/diamond_pickaxe.png"
                ).onClick(() -> {
                    this.performTrade(player, set.get(5));
                })
        );

        form.tittle("Mejora tu armadura a armadura: " + armor.getFormatDisplayName());
        form.addButtons(buttons);
        return form;
    }

    @Override
    public void onClose(Player player) {

    }

    private void performTrade(Player player, ItemStack reward) {
        //Find required armor piece in player's inventory
        int itemSlot = -1;

        ItemStack[] contents = player.getInventory().getContents();
        for (int i = 0; i < contents.length; i++) {
            ItemStack stack = contents[i];

            if (stack != null && this.requiredArmor.equals(customArmorService.getArmor(stack)) && this.sameArmorPiece(stack, reward)) {
                itemSlot = i;
                break;
            }
        }

        //Perform trade
        if (itemSlot != -1 && this.subtractCompress(player.getInventory())) {
            WorldUtils.playSound(Sound.ENTITY_ARROW_HIT_PLAYER, player);
            translator.send(player, "CUSTOM_ARMOR.TRADE_MENU.TRADE", armor);
            ItemStack newItem = reward.clone();
            player.getInventory().setItem(itemSlot, null);
            player.getInventory().setItem(itemSlot, newItem);

            new PlayerUpgradeArmorEvent(player, requiredArmor, armor, newItem).callEvent();
            return;
        }

        //Insufficient compress or armor
        WorldUtils.playSound(Sound.ENTITY_VILLAGER_NO, player);
        if (itemSlot == -1) {
            translator.send(player, "CUSTOM_ARMOR.TRADE_MENU.PIECE_NOT_FOUND", requiredArmor);
            return;
        }

        ItemStack compressItem = customArmorService.getArmorCompress(armor, 1);
        int count = compressCost - InventoryUtils.getItemCount(player.getInventory(), compressItem);
        translator.send(player, "CUSTOM_ARMOR.TRADE_MENU.INSUFFICIENT_COMPRESS", LocalPlaceholders.builder().add("<missing_compress>", count + "").add("<compress_name>", armor.getFormatDisplayName() + " &7&lᴄᴏᴍᴘʀᴇss"));
    }

    private boolean subtractCompress(Inventory inventory) {

        ItemStack compressItem = customArmorService.getArmorCompress(armor, 1);
        int count = InventoryUtils.getItemCount(inventory, compressItem);
        if (count < compressCost) return false;

        InventoryUtils.subtract(inventory, compressItem, compressCost);
        return true;
    }

    private boolean sameArmorPiece(ItemStack i1, ItemStack i2) {
        int id1 = customArmorService.getItemArmorId(i1);
        int id2 = customArmorService.getItemArmorId(i2);

        return id1 != -1 && id1 == id2;
    }
}
