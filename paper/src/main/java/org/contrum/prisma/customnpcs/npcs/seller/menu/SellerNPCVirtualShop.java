/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by izLoki on 24/05/2024
 * Website: contrum.org
 */

package org.contrum.prisma.customnpcs.npcs.seller.menu;

import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.contrum.chorpu.menu.button.Button;
import org.contrum.chorpu.menu.impl.PaginatedMenu;
import org.contrum.chorpu.menu.storage.StorageMenu;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customnpcs.npcs.seller.NPCSeller;
import org.contrum.prisma.utils.ItemBuilder1_20;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.tritosa.Translator;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class SellerNPCVirtualShop extends PaginatedMenu {

    private final PaperServices services;
    private final Translator translator;

    public SellerNPCVirtualShop(PaperServices services) {
        this.services = services;
        this.translator = services.getTranslator();
    }

    @Override
    public List<Button> getPaginatedButtons(Player player) {
        List<Button> buttons = new ArrayList<>();

        for (NPCSeller npc : services.getCustomNPCService().getNPCs(NPCSeller.class)) {
            if (npc.isShowInMenu()) {
                ItemBuilder1_20 builder = new ItemBuilder1_20(Material.PLAYER_HEAD);
                String name;
                if (npc.getNpc() != null) {
                    name = npc.getNpc().getDisplayName();
                } else {
                    name = npc.getName() != null ? npc.getName() : "Click!";
                }

                builder.name(name);
                builder.setOwnerUrl(npc.getNpc().getSkinUrl());

                //Lore
                if (player.hasPermission("core.command.virtualshop")) {
                    builder.setLore(translator.getListString(player, "MENUS.VIRTUAL_SHOP.ALLOWED_LORE"));
                } else {
                    builder.setLore(translator.getListString(player, "MENUS.VIRTUAL_SHOP.NO_PERMISSION_LORE"));
                }

                buttons.add(Button.of(builder.build(), (c) -> {
                    if (c.hasPermission("core.command.virtualshop"))
                        new SellerNPCMenu(services, player, npc.getItems());
                    else {
                        translator.send(c, "MENUS.VIRTUAL_SHOP.NO_PERMISSION_MESSAGE");
                        WorldUtils.playSound(Sound.ENTITY_VILLAGER_NO, c);
                    }
                }));

            }
        }

        return buttons;
    }

    @Override
    public Map<Integer, Button> getGlobalButtons(Player player) {
        return Map.of();
    }

    @Override
    public int getRows(Player player) {
        return 4;
    }

    @Override
    public StorageMenu.FillType getFillType() {
        return StorageMenu.FillType.ONLY_CORNERS;
    }

    @Override
    public String getTitle(Player player) {
        return "Virtual";
    }
}
