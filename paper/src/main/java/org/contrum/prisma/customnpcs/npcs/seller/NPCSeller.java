/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by izLoki on 24/05/2024
 * Website: contrum.org
 */

package org.contrum.prisma.customnpcs.npcs.seller;

import com.google.common.reflect.TypeToken;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.Location;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customnpcs.CustomNPC;
import org.contrum.prisma.customnpcs.CustomNPCService;
import org.contrum.prisma.customnpcs.npcs.seller.menu.SellerNPCEditorMenu;
import org.contrum.prisma.customnpcs.npcs.seller.menu.SellerNPCMenu;
import org.contrum.prisma.npc.action.NPCActionAdapter;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class NPCSeller extends CustomNPC {

    private List<SellerItem> items = new ArrayList<>();
    private boolean showInMenu = false;

    public NPCSeller(CustomNPCService service, Location location, String name) {
        super(service, location, name);

        this.registerActions();
    }

    public NPCSeller(CustomNPCService service, ConfigurationSection section) {
        super(service, section);

        Type listType = new TypeToken<List<SellerItem>>() {}.getType();

        if (section.contains("ITEMS")) {
            List<SellerItem> i = PaperServices.GSON.fromJson(section.getString("ITEMS"), listType);
            if (i == null) i = new ArrayList<>();

            this.items = i;
        }
        this.showInMenu = section.getBoolean("SHOW_IN_MENU", false);

        this.registerActions();
    }

    public void addItem(ItemStack item, int cost){
        items.add(new SellerItem(item, cost));
    }

    public void removeItem(int i){
        items.remove(i);
    }

    public void changePrice(int i, int price){
        items.get(i).setCost(price);
    }


    private void registerActions() {
        super.registerInteract(new NPCActionAdapter(NPCActionAdapter.Type.RIGHT, (player) -> {
            //Ensure sync
            TaskUtil.run(getServices().getPlugin(), () -> {
                if (player.isOp() && player.isSneaking()) {
                    new SellerNPCEditorMenu(this.getServices(), player, this);
                    return;
                }

                if (player.isSneaking()) return;
                new SellerNPCMenu(this.getServices(), player, this.items);
            });
        }));
    }

    @Override
    public boolean serialize(ConfigurationSection section) {
        boolean result = super.serialize(section);

        Type listType = new TypeToken<List<SellerItem>>() {}.getType();
        if (result) {
            String uuid = this.getNpc().getUUID().toString();
            section.set(uuid + ".TYPE", "SELLER");
            section.set(uuid + ".ITEMS", PaperServices.GSON.toJson(this.getItems(), listType));
            section.set(uuid + ".SHOW_IN_MENU", this.isShowInMenu());
        }
        return result;
    }
}
