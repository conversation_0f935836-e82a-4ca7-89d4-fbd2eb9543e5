/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by izLoki on 24/05/2024
 * Website: contrum.org
 */

package org.contrum.prisma.customnpcs.npcs.seller.menu;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.*;
import org.bukkit.event.player.PlayerChatEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.persistence.PersistentDataType;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customnpcs.npcs.seller.NPCSeller;
import org.contrum.prisma.customnpcs.npcs.seller.SellerItem;
import org.contrum.prisma.utils.CCP;
import org.contrum.prisma.utils.NBTUtil;

import java.util.ArrayList;
import java.util.List;

public class SellerNPCEditorMenu implements Listener {
    private final Player player;
    private final NPCSeller npc;
    private Inventory inventory;
    private ItemStack selectedItem;


    public SellerNPCEditorMenu(PaperServices services, Player player, NPCSeller npc) {
        this.player = player;
        this.npc = npc;

        Bukkit.getPluginManager().registerEvents(this, services.getPlugin());
        open();
    }

    private void open(){
        this.inventory = Bukkit.createInventory(null, 45, "Tienda");
        update();
        player.openInventory(inventory);
    }

    private void update(){
        //ItemStack book = new ItemBuilder(Material.BOOK).name(MessageUtil.prismaFont("&e&ltienda")).setFormattedLore(List.of("&b| &e¡tienda virtual! &fcompra &cequipamiento &fy", "&b| &cobjetos especiales &fcon tus &6coins.", "", "&b| &fcoloca tu mouse sobre un item para", "&b| &fpara conocer su &6precio &fy haz &cclic", "&b| &cpara comprarlo!")).hideInformation().build();
        ItemStack glass = new ItemStack(Material.YELLOW_STAINED_GLASS_PANE);
        for (int i = 0; i < inventory.getSize() ; i++){
            if (i > 9 && i < 17 || i > 18 && i < 26 || i > 27 && i < 35) {
                inventory.setItem(i, null);
                continue;
            };
            inventory.setItem(i, glass);
        }

        int i = 0;
        for (SellerItem npcitem : npc.getItems()){
            ItemStack item = npcitem.getItem().clone();
            List<String> lore = new ArrayList<>();
            lore.add("&fPrecio: &6" + npcitem.getCost());
            lore.add("&f");
            lore.add("&cClick izquierdo para eliminar.");
            lore.add("&eClick derecho para cambiar precio.");
            item.setLore(CCP.translateAndFront(lore));
            NBTUtil.setNBT(item, "id", PersistentDataType.INTEGER, i);
            inventory.setItem(inventory.firstEmpty(), item);
            i++;
        }
        //inventory.setItem(4, book);
    }

    private void removeItem(ItemStack item) {
        int i = NBTUtil.getNBT(item.getItemMeta(), "id", PersistentDataType.INTEGER, 9999999);
        if (npc.getItems().size() > i){
            npc.removeItem(i);
            update();
        }
    }

    private void addItem(ItemStack item) {
        item.setAmount(1);
        npc.addItem(item, 999999999);
        update();
    }

    private void changeItemPrice(ItemStack item) {
        this.selectedItem = item;
        player.closeInventory();
        //update();
    }

    private void changeItemPrice(int newPrice) {
        if (selectedItem != null){
            int i = NBTUtil.getNBT(selectedItem.getItemMeta(), "id", PersistentDataType.INTEGER, 9999999);
            if (npc.getItems().size() > i){
                npc.changePrice(i, newPrice);
                selectedItem = null;
                update();
                player.openInventory(inventory);
            }
        }
    }


    @EventHandler
    public void inventoryClick(InventoryClickEvent event){
        if (event.getClickedInventory() != null && event.getWhoClicked() instanceof Player p){
            if (p.getUniqueId().equals(player.getUniqueId())  && event.getInventory().hashCode() == this.inventory.hashCode()){
                event.setCancelled(true);
                ItemStack item = event.getCurrentItem();
                if (item == null || item.getType().equals(Material.AIR) || item.getType().equals(Material.YELLOW_STAINED_GLASS_PANE)) return;
                //Inventory side
                if (!event.getClickedInventory().getType().equals(InventoryType.PLAYER)){
                    if (event.getClick().equals(ClickType.LEFT) || event.getClick().equals(ClickType.SHIFT_LEFT)){
                        player.getInventory().addItem(npc.getItems().get(NBTUtil.getNBT(item.getItemMeta(), "id", PersistentDataType.INTEGER, 9999999)).getItem().clone());
                        removeItem(item);
                        return;
                    }
                    if (event.getClick().equals(ClickType.RIGHT) || event.getClick().equals(ClickType.SHIFT_RIGHT)){
                        player.sendMessage(CCP.translate("&aIngresa el nuevo precio del item por el chat!"));
                        changeItemPrice(item);
                        return;
                    }
                }
                else  { //Player side
                    if (event.getClick().equals(ClickType.LEFT) || event.getClick().equals(ClickType.SHIFT_LEFT)){
                        if (npc.getItems().size() >= 21){
                            player.sendMessage(CCP.translate("&cEste npc ya no puede vender mas items!"));
                            return;
                        }
                        addItem(item.clone());
                        event.setCurrentItem(null);
                        return;
                    }
                }
            }
        }
    }

    @EventHandler
    public void playerChatMessage(PlayerChatEvent event){
        if (selectedItem != null && event.getPlayer().getUniqueId().equals(player.getUniqueId())){
            String message = ChatColor.stripColor(event.getMessage());
            int price = 0;
            try {
                price = Integer.parseInt(message);
            } catch (NumberFormatException e) {
                player.sendMessage(CCP.translate("&cIngresa un numero!"));
            }
            changeItemPrice(price);
            event.setCancelled(true);
        }
    }

    @EventHandler
    public void playerOpenInventory(InventoryOpenEvent event){
        if (selectedItem != null && player.getUniqueId().equals(event.getPlayer().getUniqueId())){
            if (!event.getInventory().getType().equals(InventoryType.CREATIVE) && !event.getInventory().getType().equals(InventoryType.PLAYER)) HandlerList.unregisterAll(this);
        }
    }

    @EventHandler
    public void playerCloseInventory(InventoryCloseEvent event){
        if (player.getUniqueId().equals(event.getPlayer().getUniqueId())&& event.getInventory().hashCode() == this.inventory.hashCode()){
            if (this.selectedItem != null) return;
            HandlerList.unregisterAll(this);
        }
    }
}
