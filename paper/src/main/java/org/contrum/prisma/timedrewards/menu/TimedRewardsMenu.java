package org.contrum.prisma.timedrewards.menu;

import co.aikar.commands.BaseCommand;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.chat.CC;
import org.contrum.chorpu.menu.button.Button;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.timedrewards.TimedReward;
import org.contrum.prisma.timedrewards.TimedRewardsService;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.prisma.utils.menus.CoreMenu;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class TimedRewardsMenu extends CoreMenu {

    private final TimedRewardsService service;
    private ProfilePaperMetadata metadata;

    public TimedRewardsMenu(PaperServices services) {
        super(services);

        this.service = services.getTimedRewardsService();
    }

    @Override
    public int getRows(Player player) {
        return 6;
    }

    @Override
    public void open(Player player) {
        this.metadata = super.getServices().getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
        super.open(player);
    }

    @Override
    public Map<Integer, Button> getButtons(Player player) {
        Map<Integer, Button> buttons = new HashMap<>();

        for (TimedReward reward : service.getRewards()) {
            ItemStack item = reward.getIcon().getItem().clone();

            List<String> lore = item.getLore().stream()
                    .map((line) -> line
                            .replace("{time}", CC.translate(service.getTimeLeft(metadata,reward)))).toList();
            item.setLore(lore);

            buttons.put(reward.getSlot(), Button.of(item, (clicker) -> {
                if (!reward.hasPermission(clicker)) {
                    super.getServices().getTranslator().send(clicker, "TIMED_REWARDS.NO_PERMISSION");
                    WorldUtils.playBlockedSound(clicker);
                    return;
                }

                if (!service.canClaimReward(clicker, reward)) {
                    super.getServices().getTranslator().send(clicker, "TIMED_REWARDS.REWARD_ON_COOLDOWN");
                    WorldUtils.playNoSound(clicker);
                    return;
                }

                service.claimReward(clicker, reward);
                WorldUtils.playLevelUpSound(clicker);
                super.getServices().getTranslator().send(clicker, "TIMED_REWARDS.REWARD_CLAIMED");
            }));
        }

        return buttons;
    }

    @Override
    public boolean isAutoUpdate() {
        return false;
    }

    @Override
    public String getTitle(Player player) {
        return "Recompensas";
    }
}
