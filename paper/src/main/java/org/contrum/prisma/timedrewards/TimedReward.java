package org.contrum.prisma.timedrewards;

import lombok.Getter;
import lombok.Setter;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.utils.config.reward.ConfigReward;
import org.contrum.prisma.utils.config.reward.item.ConfigItem;
import org.contrum.prisma.utils.time.TimeUtils;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

@Getter @Setter
public class TimedReward {

    private String name;
    private String permission;
    private Duration cooldown;
    private int slot;
    private ConfigItem icon;
    private List<ConfigReward> rewards = new ArrayList<>();

    public TimedReward(PaperServices services, ConfigurationSection section) {
        this.name = section.getString("NAME");
        this.permission = section.getString("PERMISSION", null);
        this.cooldown = TimeUtils.parseDuration(section.getString("COOLDOWN", "0s"));
        this.slot = section.getInt("SLOT", 0);
        this.icon = new ConfigItem(section.getConfigurationSection("ICON"));

        if (section.contains("REWARDS")) {
            section.getConfigurationSection("REWARDS").getKeys(false).forEach(key -> {
                ConfigurationSection rewardSection = section.getConfigurationSection("REWARDS." + key);
                System.out.println("Loading reward: " + key);
                if (rewardSection != null) {
                    ConfigReward reward = new ConfigReward(services, rewardSection);
                    this.rewards.add(reward);
                }
            });
        }
    }

    public boolean hasPermission(Player player) {
        return permission == null || permission.isEmpty() || player.hasPermission(permission);
    }

    public void giveRewards(PaperServices services, Player player) {
        for (ConfigReward reward : rewards) {
            reward.give(services, player);
        }
    }
}
