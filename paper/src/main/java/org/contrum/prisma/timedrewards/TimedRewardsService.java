package org.contrum.prisma.timedrewards;

import lombok.Getter;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.utils.config.ConfigFile;
import org.contrum.prisma.utils.time.TimeUtils;

import java.util.ArrayList;
import java.util.List;

public class TimedRewardsService {

    private final PaperServices services;

    private ConfigFile config;
    @Getter
    private final List<TimedReward> rewards = new ArrayList<>();

    public TimedRewardsService(PaperServices services) {
        this.services = services;

        this.reload();
    }

    public void loadRewards() {
        ConfigurationSection section = config.getConfig().getConfigurationSection("REWARDS");
        if (section == null)
            return;

        this.rewards.clear();
        for (String key : section.getKeys(false)) {
            ConfigurationSection reward = section.getConfigurationSection(key);
            this.rewards.add(new TimedReward(services, reward));
        }
    }

    public TimedReward getReward(String name) {
        return this.rewards.stream()
                .filter(reward -> reward.getName().equalsIgnoreCase(name))
                .findFirst()
                .orElse(null);
    }

    public void claimReward(Player player, TimedReward reward) {
        ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
        metadata.getClaimedTimedRewards().put(reward.getName(), System.currentTimeMillis());
        reward.giveRewards(services, player);

        services.getProfileService().saveProfile(metadata.getProfile());
    }

    public String getTimeLeft(ProfilePaperMetadata metadata, TimedReward reward) {
        long lastClaimTime = metadata.getClaimedTimedRewardTime(reward.getName());
        long cooldown = reward.getCooldown().toMillis();
        long timeLeft = (lastClaimTime + cooldown) - System.currentTimeMillis();

        if (timeLeft <= 0) {
            return "&aAhora";
        }

        return "&e" + TimeUtils.getFormattedTime(timeLeft, true);
    }

    public boolean canClaimReward(Player player, TimedReward reward) {
        if (!reward.hasPermission(player)) {
            return false;
        }

        ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
        long lastClaimTime = metadata.getClaimedTimedRewardTime(reward.getName());
        if ((System.currentTimeMillis() - lastClaimTime) < reward.getCooldown().toMillis()) {
            return false; // Still in cooldown
        }

        return true;
    }

    public boolean canClaimReward(Player player, String name) {
        TimedReward timedReward = this.getReward(name);
        return this.canClaimReward(player, timedReward);
    }

    public void reload() {
        this.config = new ConfigFile(services.getPlugin(), "data/timed-rewards.yml");
        this.loadRewards();
    }
}
