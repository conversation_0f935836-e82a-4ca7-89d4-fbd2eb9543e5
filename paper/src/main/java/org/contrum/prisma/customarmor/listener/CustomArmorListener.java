package org.contrum.prisma.customarmor.listener;

import lombok.RequiredArgsConstructor;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.EquipmentSlot;
import org.bukkit.inventory.ItemStack;
import org.contrum.prisma.customarmor.CustomArmor;
import org.contrum.prisma.customarmor.CustomArmorService;
import org.contrum.prisma.events.BlockDropEvent;
import org.contrum.prisma.events.CurrencyAddEvent;
import org.contrum.prisma.utils.ProbabilityUtil;
import org.contrum.prisma.utils.WorldUtils;

@RequiredArgsConstructor
public class CustomArmorListener implements Listener {
    private final CustomArmorService customArmorService;

    @EventHandler
    public void onRightClick(PlayerInteractEvent event) {
        if (event.getAction().isRightClick()) {
            ItemStack item = event.getPlayer().getInventory().getItemInMainHand();

            if (event.getHand() != EquipmentSlot.HAND) return;

            if (item.getType().equals(Material.PLAYER_HEAD) && customArmorService.isArmor(item)) {
                event.setCancelled(true);
                Player player = event.getPlayer();
                ItemStack headItem = player.getInventory().getItem(EquipmentSlot.HEAD).clone();
                player.getInventory().setItem(EquipmentSlot.HEAD, item);
                player.getInventory().setItemInMainHand(headItem);
                player.swingMainHand();
                WorldUtils.playSound(Sound.ITEM_ARMOR_EQUIP_LEATHER, player);
            }
        }
    }

    @EventHandler
    private void blockBreak(BlockDropEvent event){
        Player player = event.getPlayer();

        ItemStack item = player.getInventory().getItemInMainHand();
        //TODO: Optimize
        /*
        CustomArmor armor = customArmorService.getArmor(item);

        if (armor == null) return;
        if (item.getType().name().contains("PICKAXE") && ProbabilityUtil.probability(armor.getPickaxeMultiplyChance())){
            player.getWorld().playSound(player.getLocation(), Sound.ENTITY_ARROW_HIT_PLAYER, 1, 1);
            event.setAmount(event.getAmount()+1);
        }

         */
    }

    @EventHandler
    public void coinsAdd(CurrencyAddEvent event){
        if (event.getCause().equals(CurrencyAddEvent.Cause.BLOCK_BREAK)){
            Player player = event.getPlayer();
            if (player == null) return;

            ItemStack item = player.getInventory().getItemInMainHand();
            CustomArmor armor = customArmorService.getArmor(item);

            if (armor == null) return;
            if (item.getType().name().contains("PICKAXE") && ProbabilityUtil.probability(armor.getPickaxeMultiplyChance())){
                player.getWorld().playSound(player.getLocation(), Sound.ENTITY_ARROW_HIT_PLAYER, 1, 1);
                event.setAmount(event.getAmount()*2);
            }
        }
    }
}
