/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by izLoki on 24/05/2024
 * Website: contrum.org
 */

package org.contrum.prisma.customarmor;

import com.sk89q.worldguard.protection.regions.ProtectedRegion;
import lombok.Getter;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.attribute.Attribute;
import org.bukkit.attribute.AttributeModifier;
import org.bukkit.block.ShulkerBox;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.inventory.EquipmentSlot;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.PlayerInventory;
import org.bukkit.inventory.meta.BlockStateMeta;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;
import org.contrum.chorpu.TaskUtil;
import org.contrum.chorpu.chat.CC;
import org.contrum.chorpu.xseries.XMaterial;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.PrismaCoreSetting;
import org.contrum.prisma.customarmor.listener.CustomArmorListener;
import org.contrum.prisma.customarmor.runnable.ArmorUpdateRunnable;
import org.contrum.prisma.utils.LocationUtils;
import org.contrum.prisma.utils.WorldGuardUtils;
import org.contrum.prisma.utils.config.ConfigFile;
import org.contrum.prisma.utils.ItemBuilder1_20;
import org.contrum.prisma.utils.NBTUtil;
import org.contrum.tritosa.Translator;

import java.util.*;
import java.util.stream.Collectors;

@Getter
public class CustomArmorService {

    private final PaperServices services;
    private final Translator translator;
    private ConfigFile config;
    private final HashMap<String, CustomArmor> customArmors = new HashMap<>();

    private final HashMap<CustomArmor, List<ItemStack>> armorCache = new HashMap<>();
    private final HashMap<CustomArmor, List<ItemStack>> blockCache = new HashMap<>();

    public CustomArmorService(PaperServices services) {
        this.services = services;
        this.translator = services.getTranslator();

        config = new ConfigFile(services.getPlugin(), "armors.yml");
        ConfigurationSection armorConfig = config.getConfig().getConfigurationSection("ARMOR");
        if (armorConfig != null) {
            Set<String> armorList = armorConfig.getKeys(false);
            for (String armorName : armorList) {
                ConfigurationSection armorSection = armorConfig.getConfigurationSection(armorName);
                if (armorSection != null) {
                    CustomArmor armor = new CustomArmor(armorSection);
                    customArmors.put(armorName, armor);
                }
            }
        }

        Bukkit.getPluginManager().registerEvents(new CustomArmorListener(this), services.getPlugin());
        TaskUtil.runAsyncTimer(services.getPlugin(), new ArmorUpdateRunnable(this), 0L, 40L);
    }

    public void shutdown() {
        this.save();
    }

    public void save() {
        if (config == null) return;
        ConfigurationSection section = config.getConfig().getConfigurationSection("ARMOR");
        if (section == null) section = config.getConfig().createSection("ARMOR");
        for (Map.Entry<String, CustomArmor> entry : customArmors.entrySet()) {
            CustomArmor armor = entry.getValue();
            armor.serialize(section);
        }

        config.save();
    }

    public void createArmor(String name) {
        customArmors.put(name, new CustomArmor(name));
        this.save();
    }

    public void removeArmor(String name) {
        customArmors.remove(name);
        config.getConfig().set("ARMOR." + name, null);
        config.save();
    }

    public boolean isArmor(ItemStack i) {
        return i != null && i.hasItemMeta() && NBTUtil.getNBT(i.getItemMeta(), "customArmor", PersistentDataType.BOOLEAN, false);
    }

    public boolean isCompress(ItemStack i) {
        return i != null && i.hasItemMeta() && NBTUtil.getNBT(i.getItemMeta(), "customcompress", PersistentDataType.BOOLEAN, false);
    }

    public String getItemArmorName(ItemStack i) {
        if (!isArmor(i)) return "";
        return NBTUtil.getNBT(i.getItemMeta(), "name", PersistentDataType.STRING, "");
    }

    public List<CustomArmor> getArmorsSorted() {
        return this.getCustomArmors().values().stream().sorted(Comparator.comparingInt(CustomArmor::getPriority)).toList();
    }

    public CustomArmor getArmor(String name) {
        return customArmors.get(name);
    }

    public CustomArmor getArmor(ItemStack i) {
        if (!isArmor(i)) return null;
        return this.getArmor(NBTUtil.getNBT(i.getItemMeta(), "name", PersistentDataType.STRING, ""));
    }

    public CustomArmor getArmorByPriority(int priority) {
        for (CustomArmor armor : customArmors.values()) {
            if (armor.getPriority() == priority) return armor;
        }
        return null;
    }

    public CustomArmor getNextArmor(String name) {
        int currentPriority = customArmors.get(name).getPriority();
        int nextPriority = Integer.MAX_VALUE;
        CustomArmor nextArmor = null;

        for (Map.Entry<String, CustomArmor> entry : customArmors.entrySet()) {
            int armorPriority = entry.getValue().getPriority();
            if (armorPriority > currentPriority && armorPriority < nextPriority) {
                nextPriority = armorPriority;
                nextArmor = entry.getValue();
            }
        }

        return nextArmor;
    }

    public CustomArmor getPreviousArmor(CustomArmor armor) {
        int currentPriority = armor.getPriority();
        int previousPriority = Integer.MIN_VALUE;
        CustomArmor previousArmor = null;

        for (Map.Entry<String, CustomArmor> entry : customArmors.entrySet()) {
            int armorPriority = entry.getValue().getPriority();
            if (armorPriority < currentPriority && armorPriority > previousPriority) {
                previousPriority = armorPriority;
                previousArmor = entry.getValue();
            }
        }

        return previousArmor;
    }

    public CustomArmor getPreviousArmor(String name) {
        return getPreviousArmor(customArmors.get(name));
    }

    public List<ItemStack> getSet(String name) {
        CustomArmor armor = customArmors.get(name);
        if (armor == null) return null;
        return getSet(armor);
    }

    public List<ItemStack> getSet(CustomArmor armor) {
        List<ItemStack> cachedArmor = armorCache.get(armor);
        if (cachedArmor != null) {
            return cachedArmor.stream().map(ItemStack::clone).collect(Collectors.toList());
        }
        String name = armor.getName();
        String nextMineLore;
        if (getNextArmor(name) == null) {
            nextMineLore = "&c◆ &fᴇsᴛᴇ ɪᴛᴇᴍ ᴇsᴛᴀ ᴀʟ ɴɪᴠᴇʟ &c&l&nᴍᴀxɪᴍᴏ";
        } else {
            nextMineLore = "&c◆ &fsᴇ ᴘᴜᴇᴅᴇ ᴍᴇᴊᴏʀᴀʀ ᴇɴ﹕ &7&lᴍɪɴᴀ " + getNextArmor(name).getFormatDisplayName();
        }

        List<ItemStack> list = new ArrayList<>();

        //Helmet
        ItemBuilder1_20 helmetBuilder = new ItemBuilder1_20(XMaterial.matchXMaterial(armor.getArmorType() + "_HELMET").get().parseMaterial());
        helmetBuilder.setLore(parseLore(translator.getListString(Bukkit.getConsoleSender(), "CUSTOM_ARMOR.HELMET_LORE", armor)));

        if (armor.getArmorType().equals("LEATHER")) {
            helmetBuilder.color(armor.getColor());
        }

        if (armor.getSkullSkin() != null && !armor.getSkullSkin().isEmpty() && !armor.getSkullSkin().equals("none")) {
            helmetBuilder.type(Material.PLAYER_HEAD);
            if (armor.getSkullSkin().contains("textures.minecraft.net")) {
                helmetBuilder.setOwnerUrl(armor.getSkullSkin());
            } else {
                helmetBuilder.setOwner(armor.getSkullSkin());
            }
        }

        helmetBuilder.name(armor.getFormatDisplayName() + " &7&lᴄᴀsᴄᴏ");
        helmetBuilder.unbreakable(armor.isUnbreakable());
        helmetBuilder.addUnsafeEnchantment(Enchantment.PROTECTION, armor.getProtection());
        helmetBuilder.addUnsafeEnchantment(Enchantment.UNBREAKING, armor.getDurability());
        if (armor.getHealth() != 0) {
            helmetBuilder.addAttribute(Attribute.MAX_HEALTH, armor.getHealth(), AttributeModifier.Operation.ADD_NUMBER, EquipmentSlot.HEAD);
        }
        helmetBuilder.hideInformation();

        helmetBuilder.addNBT("customArmor", PersistentDataType.BOOLEAN, true);
        helmetBuilder.addNBT("name", PersistentDataType.STRING, name);
        list.add(helmetBuilder.build());


        //Chestplate
        ItemBuilder1_20 chestplateBuilder = new ItemBuilder1_20(Material.ELYTRA);

        chestplateBuilder.setLore(parseLore(translator.getListString(Bukkit.getConsoleSender(), "CUSTOM_ARMOR.CHEST_LORE", armor)));

        chestplateBuilder.name(armor.getFormatDisplayName() + " &7&lᴘᴇᴄʜᴇʀᴀ");
        chestplateBuilder.unbreakable(armor.isUnbreakable());
        chestplateBuilder.addUnsafeEnchantment(Enchantment.PROTECTION, armor.getProtection());
        chestplateBuilder.addUnsafeEnchantment(Enchantment.UNBREAKING, armor.getDurability());

        if (armor.getAttackSpeed() != 0) {
            chestplateBuilder.addAttribute(Attribute.ATTACK_SPEED, armor.getAttackSpeed() / 100, AttributeModifier.Operation.ADD_SCALAR, EquipmentSlot.CHEST);
        }
        chestplateBuilder.hideInformation();

        chestplateBuilder.addNBT("customArmor", PersistentDataType.BOOLEAN, true);
        chestplateBuilder.addNBT("name", PersistentDataType.STRING, name);
        chestplateBuilder.addNBT("durability", PersistentDataType.INTEGER, 3);
        chestplateBuilder.addNBT("max_durability", PersistentDataType.INTEGER, 3);
        list.add(chestplateBuilder.build());

        //Leggings
        ItemBuilder1_20 leggingsBuilder = new ItemBuilder1_20(Material.getMaterial(armor.getArmorType() + "_LEGGINGS"));

        leggingsBuilder.setLore(parseLore(translator.getListString(Bukkit.getConsoleSender(), "CUSTOM_ARMOR.LEGGINGS_LORE", armor)));

        leggingsBuilder.name(armor.getFormatDisplayName() + " &7&lᴘᴀɴᴛᴀʟᴏɴᴇs");
        leggingsBuilder.unbreakable(armor.isUnbreakable());
        leggingsBuilder.addUnsafeEnchantment(Enchantment.PROTECTION, armor.getProtection());
        leggingsBuilder.addUnsafeEnchantment(Enchantment.UNBREAKING, armor.getDurability());

        if (armor.getArmorType().equals("LEATHER")) {
            leggingsBuilder.color(armor.getColor());
        }

        if (armor.getDamage() != 0) {
            leggingsBuilder.addAttribute(Attribute.ATTACK_DAMAGE, armor.getDamage() / 100, AttributeModifier.Operation.ADD_SCALAR, EquipmentSlot.LEGS);
        }
        leggingsBuilder.hideInformation();

        leggingsBuilder.addNBT("customArmor", PersistentDataType.BOOLEAN, true);
        leggingsBuilder.addNBT("name", PersistentDataType.STRING, name);
        leggingsBuilder.addNBT("durability", PersistentDataType.INTEGER, 3);
        leggingsBuilder.addNBT("max_durability", PersistentDataType.INTEGER, 3);
        list.add(leggingsBuilder.build());

        //Boots
        ItemBuilder1_20 bootsBuilder = new ItemBuilder1_20(Material.getMaterial(armor.getArmorType() + "_BOOTS"));

        bootsBuilder.setLore(parseLore(translator.getListString(Bukkit.getConsoleSender(), "CUSTOM_ARMOR.BOOTS_LORE", armor)));

        bootsBuilder.name(armor.getFormatDisplayName() + " &7&lʙᴏᴛᴀs");
        bootsBuilder.unbreakable(armor.isUnbreakable());
        bootsBuilder.addUnsafeEnchantment(Enchantment.PROTECTION, armor.getProtection());
        bootsBuilder.addUnsafeEnchantment(Enchantment.UNBREAKING, armor.getDurability());
        if (armor.getSpeed() != 0) {
            bootsBuilder.addAttribute(Attribute.MOVEMENT_SPEED, armor.getSpeed() / 100, AttributeModifier.Operation.ADD_SCALAR, EquipmentSlot.FEET);
        }
        if (armor.getArmorType().equals("LEATHER")) {
            bootsBuilder.color(armor.getColor());
        }
        bootsBuilder.hideInformation();


        bootsBuilder.addNBT("customArmor", PersistentDataType.BOOLEAN, true);
        bootsBuilder.addNBT("name", PersistentDataType.STRING, name);
        bootsBuilder.addNBT("durability", PersistentDataType.INTEGER, 3);
        bootsBuilder.addNBT("max_durability", PersistentDataType.INTEGER, 3);
        list.add(bootsBuilder.build());

        //Sword
        ItemBuilder1_20 swordBuilder = new ItemBuilder1_20(armor.getSwordMaterial());

        swordBuilder.setLore(parseLore(translator.getListString(Bukkit.getConsoleSender(), "CUSTOM_ARMOR.SWORD_LORE", armor)));

        swordBuilder.name(armor.getFormatDisplayName() + " &7&lᴇsᴘᴀᴅᴀ");
        swordBuilder.unbreakable(armor.isUnbreakable());
        swordBuilder.addUnsafeEnchantment(Enchantment.SHARPNESS, armor.getSharpness());
        swordBuilder.addUnsafeEnchantment(Enchantment.UNBREAKING, armor.getDurability());
        swordBuilder.addAttribute(Attribute.ATTACK_DAMAGE, 0, AttributeModifier.Operation.ADD_NUMBER, EquipmentSlot.HAND);
        swordBuilder.hideInformation();

        swordBuilder.addNBT("customArmor", PersistentDataType.BOOLEAN, true);
        swordBuilder.addNBT("name", PersistentDataType.STRING, name);
        swordBuilder.addNBT("sword", PersistentDataType.STRING, armor.getSwordMaterial().name());
        swordBuilder.addNBT("durability", PersistentDataType.INTEGER, 3);
        swordBuilder.addNBT("max_durability", PersistentDataType.INTEGER, 3);
        list.add(swordBuilder.build());

        //Pickaxe
        Material pickaxeMaterial = Material.DIAMOND_PICKAXE;
        if (armor.getArmorType().equals("IRON") || armor.getArmorType().equals("GOLD") || armor.getArmorType().equals("NETHERITE")) {
            pickaxeMaterial = Material.getMaterial(armor.getArmorType() + "_PICKAXE");
        }
        ItemBuilder1_20 pickaxeBuilder = new ItemBuilder1_20(pickaxeMaterial);

        pickaxeBuilder.setLore(parseLore(translator.getListString(Bukkit.getConsoleSender(), "CUSTOM_ARMOR.PICKAXE_LORE", armor)));

        pickaxeBuilder.name(armor.getFormatDisplayName() + " &7&lᴘɪᴄᴏ");
        pickaxeBuilder.addUnsafeEnchantment(Enchantment.EFFICIENCY, armor.getEfficiency());
        pickaxeBuilder.addUnsafeEnchantment(Enchantment.UNBREAKING, armor.getDurability());
        pickaxeBuilder.unbreakable(armor.isUnbreakable());
        pickaxeBuilder.hideInformation();

        pickaxeBuilder.addNBT("customArmor", PersistentDataType.BOOLEAN, true);
        pickaxeBuilder.addNBT("name", PersistentDataType.STRING, name);
        pickaxeBuilder.addNBT("durability", PersistentDataType.INTEGER, 3);
        pickaxeBuilder.addNBT("max_durability", PersistentDataType.INTEGER, 3);
        list.add(pickaxeBuilder.build());

        this.armorCache.put(armor, list.stream().map(ItemStack::clone).toList());
        return list;
    }

    public ItemStack updateItem(ItemStack item) {
        if (!this.isArmor(item)) return null;
        CustomArmor armor = this.getArmor(item);
        if (armor == null) return null;
        switch (item.getType()) {
            case LEATHER_HELMET, PLAYER_HEAD -> {
                return getSet(armor).get(0);
            }
            case ELYTRA -> {
                return getSet(armor).get(1);
            }
            case LEATHER_LEGGINGS -> {
                return getSet(armor).get(2);
            }
            case LEATHER_BOOTS -> {
                return getSet(armor).get(3);
            }
            case DIAMOND_PICKAXE -> {
                return getSet(armor).get(5);
            }
            default -> {
                if (item.getType().equals(armor.getSwordMaterial())) {
                    return getSet(armor).get(4);
                }
            }
        }
        return null;
    }

    public ItemStack getNext(ItemStack item) {
        if (!this.isArmor(item)) return null;
        CustomArmor previewArmor = this.getArmor(item);
        CustomArmor armor = this.getNextArmor(previewArmor.getName());
        if (armor == null) return null;
        switch (item.getType()) {
            case LEATHER_HELMET, PLAYER_HEAD -> {
                return getSet(armor).get(0);
            }
            case ELYTRA -> {
                return getSet(armor).get(1);
            }
            case LEATHER_LEGGINGS -> {
                return getSet(armor).get(2);
            }
            case LEATHER_BOOTS -> {
                return getSet(armor).get(3);
            }
            case DIAMOND_PICKAXE -> {
                return getSet(armor).get(5);
            }
            default -> {
                if (item.getType().equals(previewArmor.getSwordMaterial())) {
                    return getSet(armor).get(4);
                }
            }
        }
        return null;
    }

    private List<String> parseLore(List<String> lore) {
        List<String> newLore = new ArrayList<>();
        for (String s : lore) {
            if (s.contains(" 0.0") || s.contains("+0.0") || s.contains(" 0")) {
                continue;
            }

            newLore.add(s);
        }
        return newLore;
    }

    public ItemStack getShulker(CustomArmor armor) {
        ItemStack shulkerItem = new ItemStack(armor.getShulkerMaterial());
        shulkerItem.setDisplayName(armor.getFormatDisplayName() + CC.translate(" &7&lsʜᴜʟᴋᴇʀ"));
        ItemMeta meta = shulkerItem.getItemMeta();

        if (meta instanceof BlockStateMeta) {
            BlockStateMeta blockStateMeta = (BlockStateMeta) meta;

            ShulkerBox shulkerBox = (ShulkerBox) blockStateMeta.getBlockState();
            for (ItemStack item : this.getSet(armor)) {
                shulkerBox.getInventory().addItem(item);
            }

            blockStateMeta.setBlockState(shulkerBox);
            shulkerItem.setItemMeta(blockStateMeta);
        }
        return shulkerItem;
    }

    public List<String> getArmorsBetweenPriorities(String armorName1, String armorName2) {
        CustomArmor armor1 = customArmors.get(armorName1);
        CustomArmor armor2 = customArmors.get(armorName2);

        if (armor1 != null && armor2 != null) {
            int minPriority = Math.min(armor1.getPriority(), armor2.getPriority());
            int maxPriority = Math.max(armor1.getPriority(), armor2.getPriority());

            List<String> armorsBetween = new ArrayList<>();

            for (Map.Entry<String, CustomArmor> entry : customArmors.entrySet()) {
                CustomArmor armor = entry.getValue();
                int priority = armor.getPriority();

                if (priority > minPriority && priority < maxPriority) {
                    armorsBetween.add(entry.getKey());
                }
            }

            return armorsBetween;
        }
        return Collections.emptyList();
    }

    public CustomArmor getBlockArmor(Material material) {
        for (CustomArmor armor : customArmors.values()) {
            if (armor.getBlockMaterial().equals(material)) return armor;
        }
        return null;
    }

    public ItemStack getArmorBlock(CustomArmor armor, int amount) {
        if (armor == null) return null;

        if (!this.blockCache.containsKey(armor)) {
            this.recreateArmorBlockCache(armor);
        }

        ItemStack itemStack = this.blockCache.get(armor).get(0).clone();
        itemStack.setAmount(amount);
        return itemStack;
    }

    public ItemStack getArmorCompress(CustomArmor armor, int amount) {
        if (armor == null) return null;

        if (!this.blockCache.containsKey(armor)) {
            this.recreateArmorBlockCache(armor);
        }

        ItemStack itemStack = this.blockCache.get(armor).get(1).clone();
        itemStack.setAmount(amount);
        return itemStack;
    }

    private void recreateArmorBlockCache(CustomArmor armor) {
        this.blockCache.put(armor, List.of(this.createArmorBlock(armor), this.createArmorCompressBlock(armor)));
    }

    private ItemStack createArmorBlock(CustomArmor armor) {
        ItemBuilder1_20 builder = new ItemBuilder1_20(armor.getBlockMaterial());

        builder.name(armor.getFormatDisplayName() + CC.translate(" &7&lʙʟᴏᴄᴋ"));
        builder.setLore(translator.getListString(Bukkit.getConsoleSender(), "CUSTOM_ARMOR.BLOCK_LORE").stream().map(s -> s.replaceAll("<armor_display_name>", armor.getFormatDisplayName())).toList());
        builder.amount(1);
        return builder.build();
    }

    private ItemStack createArmorCompressBlock(CustomArmor armor) {
        ItemBuilder1_20 builder = new ItemBuilder1_20(Material.PLAYER_HEAD);
        builder.amount(1);
        builder.name(armor.getFormatDisplayName() + " &7&lᴄᴏᴍᴘʀᴇss");
        builder.addNBT("customCompress", PersistentDataType.BOOLEAN, true);
        builder.addNBT("name", PersistentDataType.STRING, armor.getName());
        if (!armor.getCompressOwner().isEmpty()) {
            builder.setOwnerUrl(armor.getCompressOwner());
        } else {
            builder.type(armor.getBlockMaterial());
            builder.setGlowing(true);
        }
        builder.setLore(translator.getListString(Bukkit.getConsoleSender(), "CUSTOM_ARMOR.COMPRESS_LORE").stream().map(s -> s.replaceAll("<armor_display_name>", armor.getFormatDisplayName())).toList());
        builder.hideInformation();
        return builder.build();
    }

    public int getItemArmorId(ItemStack item) {
        CustomArmor armor = this.getArmor(item);
        if (armor == null) return -1;

        List<ItemStack> set = this.getSet(armor);
        for (int i = 0; i < set.size(); i++) {
            if (set.get(i).getType().equals(item.getType())) {
                return i;
            }
        }

        return -1;
    }

    public ItemStack reCreateItem(ItemStack item) {
        CustomArmor armor = this.getArmor(item);
        if (armor == null) return null;

        List<ItemStack> set = this.getSet(armor);
        for (int i = 0; i < set.size(); i++) {
            if (set.get(i).getType().equals(item.getType())) {
                return set.get(i);
            }
        }

        return null;
    }

    public CustomArmor getMostEquippedSet(Player player, boolean preferHigher) {
        return this.getMostEquippedSet(player.getInventory().getArmorContents(), preferHigher);
    }

    public CustomArmor getMostEquippedSet(ItemStack[] items, boolean preferHigher) {
        HashMap<CustomArmor, Integer> armors = new HashMap<>();

        for (ItemStack item : items) {
            CustomArmor armor = this.getArmor(item);
            if (armor != null) {
                armors.put(armor, armors.getOrDefault(armor, 0) + 1);
            }
        }

        return armors.entrySet().stream()
                .max((entry1, entry2) -> {
                    int countComparison = Integer.compare(entry1.getValue(), entry2.getValue());
                    if (countComparison != 0) {
                        return countComparison;
                    }

                    return preferHigher
                            ? Integer.compare(entry2.getKey().getPriority(), entry1.getKey().getPriority())
                            : Integer.compare(entry1.getKey().getPriority(), entry2.getKey().getPriority());
                })
                .map(Map.Entry::getKey)
                .orElse(null);
    }

    public int getSetDifference(Player player1, Player player2) {
        CustomArmor set1 = this.getMostEquippedSet(player1, true);
        CustomArmor set2 = this.getMostEquippedSet(player2, true);

        if (set1 == null || set2 == null) return -1;
        List<CustomArmor> sortedArmors = customArmors.values().stream()
                .sorted(Comparator.comparingInt(CustomArmor::getPriority))
                .toList();

        int set1Value = sortedArmors.indexOf(set1);
        int set2Value = sortedArmors.indexOf(set2);

        return set1Value - set2Value;
    }

    public int getSetDifference(CustomArmor set1, CustomArmor set2) {
        if (set1 == null || set2 == null) return -1;
        List<CustomArmor> sortedArmors = customArmors.values().stream()
                .sorted(Comparator.comparingInt(CustomArmor::getPriority))
                .toList();

        int set1Value = sortedArmors.indexOf(set1);
        int set2Value = sortedArmors.indexOf(set2);

        return set1Value - set2Value;
    }

    public boolean areSameArmor(ItemStack i1, ItemStack i2) {
        if (i1 == null || i2 == null) return false;
        CustomArmor a1 = this.getArmor(i1);
        CustomArmor a2 = this.getArmor(i2);
        if (a1 == null || a2 == null) return false;

        return a1.getName().equals(a2.getName());
    }

    public String getArmorRegion(CustomArmor armor) {
        return PrismaCoreSetting.CUSTOM_ARMOR_REGIONS.get(armor.getName());
    }

    public String getArmorRegion(String armor) {
        return PrismaCoreSetting.CUSTOM_ARMOR_REGIONS.get(armor);
    }

    public CustomArmor getRegionArmor(String region) {
        for (Map.Entry<String, String> entry : PrismaCoreSetting.CUSTOM_ARMOR_REGIONS.entrySet()) {
            String armor = entry.getKey();
            String rg = entry.getValue();
            if (rg.equals(region))
                return this.getArmor(armor);
        }

        return null;
    }

    public CustomArmor getArmorByRegion(Location location) {
        for (String s : WorldGuardUtils.getRegionsName(location)) {
            CustomArmor armor = this.getRegionArmor(s);

            if (armor != null)
                return armor;
        }

        return null;
    }
}
