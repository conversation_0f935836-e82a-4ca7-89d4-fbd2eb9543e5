package org.contrum.prisma.customarmor.runnable;

import com.github.retrooper.packetevents.protocol.component.builtin.item.ItemMapDecorations;
import lombok.RequiredArgsConstructor;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.scheduler.BukkitRunnable;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.customarmor.CustomArmorService;
import org.contrum.prisma.utils.InventoryUtils;
import org.contrum.prisma.utils.NBTUtil;
import org.contrum.prisma.utils.PlayerUtils;
import org.jetbrains.annotations.Nullable;

@RequiredArgsConstructor
public class ArmorUpdateRunnable extends BukkitRunnable {

    private final CustomArmorService customArmorService;

    @Override
    public void run() {
        for (Player player : Bukkit.getOnlinePlayers()) {
            if (PlayerUtils.isInDuel(player)) continue;

            ItemStack[] inventory = player.getInventory().getContents();
            for (int i = 0; i < inventory.length; i++) {
                ItemStack item = inventory[i];
                if (item == null || item.getItemMeta() == null || NBTUtil.getNBT(item.getItemMeta(), "DuelItem", PersistentDataType.BOOLEAN, false)) continue;
                ItemStack clone = customArmorService.reCreateItem(item);
                if (clone == null) continue;

                if (!item.isSimilar(clone)) {
                    //Set metadata sync to avoid crashes
                    int slot = i;
                    TaskUtil.run(customArmorService.getServices().getPlugin(), () -> {
                        //Check if item is still at the same inventory position and hasn't changed to avoid dupes
                        if (item.equals(player.getInventory().getContents()[slot]) && !item.isSimilar(clone)) {
                            item.setItemMeta(clone.getItemMeta());
                        }
                    });
                }
            }
        }
    }
}
