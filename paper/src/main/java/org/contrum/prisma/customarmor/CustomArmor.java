/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by izLoki on 24/05/2024
 * Website: contrum.org
 */

package org.contrum.prisma.customarmor;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.bukkit.ChatColor;
import org.bukkit.Color;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.contrum.chorpu.xseries.XMaterial;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Setter
@Getter
@AllArgsConstructor
public class CustomArmor {
    private int priority = 0;
    private String name;
    private String displayName = "";
    private String rgb = "0,0,0";
    private String compressOwner = "";
    private String skullSkin = "";
    private Material blockMaterial = Material.STONE;
    private String armorType = "LEATHER";
    private Material swordMaterial = Material.STONE;
    private Material shulkerMaterial = Material.SHULKER_BOX;
    private int compressCost = 64;
    private int compressRequired= 1;
    private int previousCompressUpgradeCost = 4;
    private int protection = 0;
    private int durability = 0;
    private int sharpness = 0;
    private int efficiency = 0;
    private double pickaxeMultiplyChance = 0;
    private double health = 0;
    private double damage = 0;
    private double speed = 0;
    private double attackSpeed = 0;
    private boolean unbreakable = true;
    private boolean previousCompressUpgrade = false;

    public CustomArmor(String name) {
        this.name = name;
    }

    public CustomArmor(ConfigurationSection armorSection) {
        this.priority = armorSection.getInt("PRIORITY", 0);
        this.name = armorSection.getName();
        this.displayName = armorSection.getString("DISPLAY_NAME", "");
        this.rgb = armorSection.getString("COLOR", "255,255,255");
        this.compressOwner = armorSection.getString("COMPRESS_OWNER", "");
        this.skullSkin = armorSection.getString("SKULL_SKIN", "");
        this.blockMaterial = Material.valueOf(armorSection.getString("BLOCK_MATERIAL", "STONE"));
        this.armorType = armorSection.getString("MATERIAL", "LEATHER");
        this.compressCost = armorSection.getInt("COMPRESS_COST", 64);
        this.compressRequired = armorSection.getInt("COMPRESS_REQUIRED", 1);
        this.previousCompressUpgradeCost = armorSection.getInt("PREVIOUS_COMPRESS_UPGRADE_COST", 4);
        this.swordMaterial = XMaterial.matchXMaterial(armorSection.getString("SWORD_ITEM", "STONE")).get().parseMaterial();
        this.shulkerMaterial = Material.valueOf(armorSection.getString("SHULKER_MATERIAL", "SHULKER_BOX"));
        this.protection = armorSection.getInt("PROTECTION", 0);
        this.durability = armorSection.getInt("DURABILITY", 0);
        this.sharpness = armorSection.getInt("SHARPNESS", 0);
        this.efficiency = armorSection.getInt("EFFICIENCY", 0);
        this.pickaxeMultiplyChance = armorSection.getDouble("PICKAXE_MULTIPLY_CHANCE", 0);
        this.health = armorSection.getDouble("HEALTH", 0);
        this.damage = armorSection.getDouble("DAMAGE", 0);
        this.speed = armorSection.getDouble("SPEED", 0);
        this.attackSpeed = armorSection.getDouble("ATTACK_SPEED", 0);
        this.unbreakable = armorSection.getBoolean("UNBREAKABLE", true);
        this.previousCompressUpgrade = armorSection.getBoolean("PREVIOUS_COMPRESS_UPGRADE", false);
    }

    public Color getColor(){
        String[] rgb = this.rgb.split(",");
        int r = Integer.parseInt(rgb[0]);
        int g = Integer.parseInt(rgb[1]);
        int b = Integer.parseInt(rgb[2]);
        return Color.fromRGB(r, g, b);
    }


    public String getHexDisplayName() {
        Pattern pattern = Pattern.compile("#[a-fA-F0-9]{6}");
        Matcher matcher = pattern.matcher(displayName);
        while (matcher.find()) {
            String hexCode = displayName.substring(matcher.start(), matcher.end());
            String replaceSharp = hexCode.replace('#', 'x');

            char[] ch = replaceSharp.toCharArray();
            StringBuilder builder = new StringBuilder();
            for (char c : ch) {
                builder.append("&" + c);
            }

            displayName = displayName.replace(hexCode, builder.toString());
            matcher = pattern.matcher(displayName);
        }
        return displayName;
    }

    public String getFormatDisplayName() {
        Pattern pattern = Pattern.compile("#[a-fA-F0-9]{6}");
        Matcher matcher = pattern.matcher(displayName);
        while (matcher.find()) {
            String hexCode = displayName.substring(matcher.start(), matcher.end());
            String replaceSharp = hexCode.replace('#', 'x');

            char[] ch = replaceSharp.toCharArray();
            StringBuilder builder = new StringBuilder("");
            for (char c : ch) {
                builder.append("&" + c);
            }

            displayName = displayName.replace(hexCode, builder.toString());
            matcher = pattern.matcher(displayName);
        }
        return ChatColor.translateAlternateColorCodes('&', displayName).replace("&", "");
    }

    public void setSwordMaterial(String string){
        this.swordMaterial = Material.valueOf(string);
    }

    public void serialize(ConfigurationSection section) {
        section.set(name + ".PRIORITY", this.getPriority());
        section.set(name + ".MATERIAL", this.getArmorType());
        section.set(name + ".DISPLAY_NAME", this.getDisplayName());
        section.set(name + ".COLOR", this.getRgb());
        section.set(name + ".COMPRESS_OWNER", this.getCompressOwner());
        section.set(name + ".SKULL_SKIN", this.getSkullSkin());
        section.set(name + ".BLOCK_MATERIAL", this.getBlockMaterial().name());
        section.set(name + ".COMPRESS_COST", this.getCompressCost());
        section.set(name + ".COMPRESS_REQUIRED", this.getCompressRequired());
        section.set(name + ".PREVIOUS_COMPRESS_UPGRADE_COST", this.getPreviousCompressUpgradeCost());
        try {
            section.set(name + ".SWORD_ITEM", this.getSwordMaterial().name());
        } catch (Exception e) {
            section.set(name + ".SWORD_ITEM", "STONE");
        }
        section.set(name + ".SHULKER_MATERIAL", this.getShulkerMaterial().name());
        section.set(name + ".PROTECTION", this.getProtection());
        section.set(name + ".DURABILITY", this.getProtection());
        section.set(name + ".SHARPNESS", this.getSharpness());
        section.set(name + ".EFFICIENCY", this.getEfficiency());
        section.set(name + ".PICKAXE_MULTIPLY_CHANCE", this.getPickaxeMultiplyChance());
        section.set(name + ".HEALTH", this.getHealth());
        section.set(name + ".DAMAGE", this.getDamage());
        section.set(name + ".SPEED", this.getSpeed());
        section.set(name + ".ATTACK_SPEED", this.getAttackSpeed());
        section.set(name + ".UNBREAKABLE", this.isUnbreakable());
        section.set(name + ".PREVIOUS_COMPRESS_UPGRADE", this.isPreviousCompressUpgrade());
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || this.getName() == null || getClass() != obj.getClass()) return false;

        CustomArmor that = (CustomArmor) obj;
        return this.getName().equals(that.getName());
    }
}
