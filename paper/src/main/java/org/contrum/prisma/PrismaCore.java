package org.contrum.prisma;

import com.github.retrooper.packetevents.PacketEvents;
import com.github.retrooper.packetevents.PacketEventsAPI;
import io.github.retrooper.packetevents.factory.spigot.SpigotPacketEventsBuilder;
import lombok.Getter;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.plugin.java.JavaPlugin;

@Getter
public class PrismaCore extends JavaPlugin {

    private PacketEventsAPI<?> packetEvents;
    private PaperServices paperServices;

    boolean loadPackets = false;

    @Override
    public void onLoad() {
        packetEvents = PacketEvents.getAPI();

        if (loadPackets) packetEvents.load();
    }

    @Override
    public void onEnable() {
        saveDefaultConfig();

        if (PacketEvents.getAPI() == null) {
            packetEvents = SpigotPacketEventsBuilder.build(this);
            PacketEvents.setAPI(packetEvents);
            loadPackets = true;
        }

        if (loadPackets) packetEvents.init();

        paperServices = new PaperServices(this);
    }

    @Override
    public void onDisable() {
        paperServices.shutdown();
    }
}