package org.contrum.prisma.personalpermissions;

import org.bukkit.command.CommandSender;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.ProfileService;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.jetbrains.annotations.Nullable;

public class PersonalPermissionService {
    private final PaperServices services;
    private final ProfileService profileService;

    public PersonalPermissionService(PaperServices services) {
        this.services = services;
        this.profileService = services.getProfileService();
    }

    public boolean addPermission(Profile profile, String permission, boolean global) {
        if (global) {
            if (profile.getPersonalPermissions().contains(permission.toLowerCase())) {
                return false;
            }

            profile.addPersonalPermission(permission.toLowerCase());
        } else {
            ProfilePaperMetadata metadata = profile.getServerMetadata(profileService, ProfilePaperMetadata.class);

            if (metadata.getPersonalPermissions().contains(permission.toLowerCase())) {
                return false;
            }

            metadata.addPersonalPermission(permission.toLowerCase());
        }

        if (profileService.isOnline(profile))
            profile.getPermissionHandler().update();
        profileService.saveProfile(profile);
        return true;
    }

    public boolean removePermission(Profile profile, String permission) {
        ProfilePaperMetadata metadata = profile.getServerMetadata(profileService, ProfilePaperMetadata.class);
        if (profile.getPersonalPermissions().contains(permission.toLowerCase())) {
            profile.removePersonalPermission(permission.toLowerCase());
        } else if (metadata.getPersonalPermissions().contains(permission.toLowerCase())) {
            metadata.removePersonalPermission(permission.toLowerCase());
        } else {
            return false;
        }

        if (profileService.isOnline(profile))
            profile.getPermissionHandler().update();
        profileService.saveProfile(profile);
        return true;
    }
}
