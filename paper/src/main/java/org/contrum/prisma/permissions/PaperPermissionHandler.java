/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by txmydev on 28/03/2024
 * Website: contrum.org
 */

package org.contrum.prisma.permissions;

import io.github.retrooper.packetevents.util.SpigotReflectionUtil;
import lombok.Getter;
import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import org.bukkit.permissions.PermissibleBase;
import org.contrum.prisma.PaperServices;

import java.lang.reflect.Field;

@Getter
public class PaperPermissionHandler extends PermissionHandler {
    private final static Field HUMAN_ENTITY_PERMISSIBLE_FIELD;

    private final OfflinePlayer player;
    private final CorePermissible permissible;

    static {
        try {
            Class<?> humanEntityClass = SpigotReflectionUtil.getOBCClass("entity.CraftHumanEntity");

            HUMAN_ENTITY_PERMISSIBLE_FIELD = humanEntityClass.getDeclaredField("perm");
            HUMAN_ENTITY_PERMISSIBLE_FIELD.setAccessible(true);
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        }
    }

    public PaperPermissionHandler(PaperServices services, OfflinePlayer player) {
        this.player = player;
        this.permissible = new CorePermissible(services, player);

        permissible.calculate(true);
    }

    public boolean hasPermission(String permission) {
        if (permission == null || permission.isEmpty()) {
            return true;
        }

        return permissible.hasPermission(permission);
    }

    public void clear() {
        permissible.clearPermissions();
    }

    @Override
    public void update() {
        permissible.calculate(true);
    }

    @Override
    public void apply() {
        Player p = Bukkit.getPlayer(player.getUniqueId());
        if (p == null) return;
        this.setPermissible(p);
    }

    private void removePermissible() {
        try {
            HUMAN_ENTITY_PERMISSIBLE_FIELD.set(player, new PermissibleBase(player));
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }

    private void setPermissible(Player p) {
        try {
            HUMAN_ENTITY_PERMISSIBLE_FIELD.set(p, permissible);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }
}
