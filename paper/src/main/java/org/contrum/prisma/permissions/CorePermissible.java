package org.contrum.prisma.permissions;

import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import org.bukkit.permissions.PermissibleBase;
import org.bukkit.permissions.Permission;
import org.bukkit.permissions.PermissionAttachment;
import org.bukkit.permissions.PermissionAttachmentInfo;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class CorePermissible extends PermissibleBase {

    private final PaperServices services;
    private UUID uuid;
    private Map<String, Boolean> permissions;
    private Map<String, Boolean> wildcardPermissions;
    private Map<String, Integer> numericPermissions;

    private static final Pattern NUMERIC_PATTERN = Pattern.compile("^(.+\\.)([0-9]+)(\\..*)?$");

    public CorePermissible(PaperServices services, OfflinePlayer player) {
        super(player);
        this.services = services;
        this.uuid = player.getUniqueId();
        this.permissions = new ConcurrentHashMap<>();
        this.wildcardPermissions = new ConcurrentHashMap<>();
        this.numericPermissions = new ConcurrentHashMap<>();
        this.calculate(false);
    }

    public void calculate(boolean clear) {
        if(clear) {
            this.clearPermissions();
        }

        this.calculatePermissions().forEach((key, value) -> {
            String lowerKey = key.toLowerCase();
            this.permissions.put(lowerKey, value);

            if (key.endsWith("*")) {
                String baseKey = key.substring(0, key.length() - 1);
                this.wildcardPermissions.put(baseKey.toLowerCase(), value);
            } else if (NUMERIC_PATTERN.matcher(key).matches()) {
                var matcher = NUMERIC_PATTERN.matcher(key);
                if (matcher.find()) {
                    String baseKey = matcher.group(1);
                    int number = Integer.parseInt(matcher.group(2));
                    String suffix = matcher.group(3) != null ? matcher.group(3) : "";
                    String numericKey = baseKey + suffix;

                    this.numericPermissions.merge(numericKey.toLowerCase(), number, Integer::max);
                }
            }
        });
    }

    @Override
    public boolean hasPermission(Permission perm) {
        return this.hasPermission(perm.getName());
    }

    @Override
    public boolean hasPermission(@NotNull String permission) {
        if (this.isOp()) {
            Boolean value = this.permissions.get(permission.toLowerCase());
            return value == null || value;
        }

        String lowerPermission = permission.toLowerCase();

        Boolean directValue = this.permissions.get(lowerPermission);
        if (directValue != null) {
            return directValue;
        }

        var numericMatcher = NUMERIC_PATTERN.matcher(permission);
        if (numericMatcher.matches()) {
            String baseKey = numericMatcher.group(1);
            int requestedNumber = Integer.parseInt(numericMatcher.group(2));
            String suffix = numericMatcher.group(3) != null ? numericMatcher.group(3) : "";
            String numericKey = (baseKey + suffix).toLowerCase();

            Integer maxAllowed = this.numericPermissions.get(numericKey);
            if (maxAllowed != null && requestedNumber <= maxAllowed) {
                return true;
            }
        }

        for (Map.Entry<String, Boolean> entry : this.wildcardPermissions.entrySet()) {
            if (lowerPermission.startsWith(entry.getKey()) && entry.getValue()) {
                return true;
            }
        }

        return false;
    }

    @Override
    public boolean isPermissionSet(Permission perm) {
        return this.isPermissionSet(perm.getName());
    }

    @Override
    public boolean isPermissionSet(String name) {
        String lowerName = name.toLowerCase();

        if (this.permissions.containsKey(lowerName)) {
            return true;
        }

        var numericMatcher = NUMERIC_PATTERN.matcher(name);
        if (numericMatcher.matches()) {
            String baseKey = numericMatcher.group(1);
            String suffix = numericMatcher.group(3) != null ? numericMatcher.group(3) : "";
            String numericKey = (baseKey + suffix).toLowerCase();

            if (this.numericPermissions.containsKey(numericKey)) {
                return true;
            }
        }

        for (String wildcardKey : this.wildcardPermissions.keySet()) {
            if (lowerName.startsWith(wildcardKey)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public void clearPermissions() {
        this.permissions.clear();
        this.wildcardPermissions.clear();
        this.numericPermissions.clear();
    }

    @Override
    public synchronized void removeAttachment(@NotNull PermissionAttachment attachment) {

    }

    @Override
    public void recalculatePermissions() {}

    @Override
    public @NotNull Set<PermissionAttachmentInfo> getEffectivePermissions() {
        return this.permissions.entrySet().stream()
                .map(entry -> new PermissionAttachmentInfo(this, entry.getKey(),
                        new PermissionAttachment(services.getPlugin(), this), entry.getValue()))
                .collect(Collectors.toSet());
    }

    public Map<String, Boolean> calculatePermissions() {
        Profile profile = services.getProfileService().getProfile(this.uuid);
        Map<String, Boolean> permissionToSet = new ConcurrentHashMap<>();
        if (profile == null) return permissionToSet;

        List<String> permissions = profile.getAllBukkitPermissions();

        ProfilePaperMetadata metadata = profile.getServerMetadata(services, ProfilePaperMetadata.class);
        permissions.addAll(metadata.getPersonalPermissions());

        for(String perm : permissions) {
            boolean value = !perm.startsWith("-");
            String permission = value ? perm : perm.substring(1);
            permissionToSet.put(permission.toLowerCase(), value);
        }

        return permissionToSet;
    }
}