package org.contrum.prisma.placeholders;

import lombok.RequiredArgsConstructor;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.punishment.PunishmentType;
import org.contrum.prisma.server.PrismaServer;
import org.contrum.prisma.service.ServiceManager;
import org.contrum.prisma.tag.Tag;
import org.contrum.prisma.utils.time.TimeUtils;
import org.contrum.tritosa.placeholder.Placeholder;
import org.contrum.tritosa.placeholder.complex.ComplexPlaceholder;

@RequiredArgsConstructor
public class ProfilePlaceholder extends Placeholder<Profile> {

    private final PaperServices services;

    @Override
    public ComplexPlaceholder<Profile> setupComplexPlaceholders() {
        return null;
    }

    @Override
    public String replace(Profile profile, String input) {
        String activeTagName = profile.getActiveTagName(profile.getLastServer());

        Tag activeTag = services.getTagService().getTag(activeTagName);

        String serverName = "offline";
        if (services.getServersService().isOnline(profile.getName())) {
            PrismaServer server = services.getServersService().getPlayerServer(profile.getUniqueId());
            if (server == null || server.getName() == null) serverName = "unknown";
            else serverName = server.getName();
        }
        return input.replace("<user_name>", profile.getName())
                .replace("<user_real_name>", profile.getRealName())
                .replace("<user_real_displayname>", profile.getRealDisplayName())
                .replace("<user_displayname>", profile.getDisplayName())
                .replace("<user_coloredname>", profile.getColoredName())
                .replace("<user_uuid>", profile.getUniqueId().toString())
                .replace("<user_tag_prefix>", activeTag == null ? "" : activeTag.getPrefix())
                .replace("<user_tag_suffix>", activeTag == null ? "" : activeTag.getSuffix())
                .replace("<user_first_seen>", TimeUtils.toDate(profile.getGlobalFirstSeen()))
                .replace("<user_last_seen>", TimeUtils.toDate(profile.getGlobalLastSeen()))
                .replace("<user_punished>", profile.isPunished() + "")
                .replace("<user_status>", profile.isPunished() ? "punished" : services.getServersService().isOnline(profile.getName()) ? "online" : "offline")
                .replace("<user_server>", serverName)
                .replace("<user_punishments_count>", profile.getPunishments().size() + "")
                .replace("<user_active_punishments_count>", profile.getActivePunishments().size() + "")
                .replace("<user_grants_count>", profile.getGrants().size() + "")
                .replace("<user_active_grants_count>", profile.getActiveGrants().size() + "");
    }
}
