/*
 *  This file is part of the Apple Core project.
 *  Copyright (c) 2022-2024. Contrum Services
 *  Created by izLoki on 12/06/2024
 *  Website: contrum.org
 */

package org.contrum.prisma.placeholders;

import org.contrum.tritosa.placeholder.GlobalPlaceholder;

import java.util.HashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PrismaFontPlaceHolder implements GlobalPlaceholder {

    private static final Pattern PRISMA_FONT_PATTERN = Pattern.compile("<prisma_font>(.*?)</prisma_font>", Pattern.DOTALL);

    private static final HashMap<Character, Character> fontMap = new HashMap<>();

    static {
        char[][] font = {
                {'a', 'ᴀ'}, {'b', 'ʙ'}, {'c', 'ᴄ'}, {'d', 'ᴅ'}, {'e', 'ᴇ'},
                {'f', 'ꜰ'}, {'g', 'ɢ'}, {'h', 'ʜ'}, {'i', 'ɪ'}, {'j', 'ᴊ'},
                {'k', 'ᴋ'}, {'l', 'ʟ'}, {'m', 'ᴍ'}, {'n', 'ɴ'}, {'ñ', 'ñ'},
                {'o', 'ᴏ'}, {'p', 'ᴘ'}, {'q', 'q'}, {'r', 'ʀ'}, {'s', 's'},
                {'t', 'ᴛ'}, {'u', 'ᴜ'}, {'v', 'ᴠ'}, {'w', 'ᴡ'}, {'x', 'x'},
                {'y', 'ʏ'}, {'z', 'ᴢ'}
        };

        for (char[] pair : font) {
            fontMap.put(pair[0], pair[1]);
        }
    }

    @Override
    public String replace(String s) {
        Matcher matcher = PRISMA_FONT_PATTERN.matcher(s);
        StringBuffer result = new StringBuffer();

        boolean matchedAtLeastOne = false;

        while (matcher.find()) {
            matchedAtLeastOne = true;
            String converted = convertPreservingPlaceholders(matcher.group(1));
            matcher.appendReplacement(result, Matcher.quoteReplacement(converted));
        }

        matcher.appendTail(result);

        if (!matchedAtLeastOne && s.contains("<prisma_font>")) {
            int start = s.indexOf("<prisma_font>") + "<prisma_font>".length();
            String before = s.substring(0, start - "<prisma_font>".length());
            String toConvert = s.substring(start);
            return before + convertPreservingPlaceholders(toConvert);
        }

        return result.toString();
    }

    private String convertPreservingPlaceholders(String input) {
        StringBuilder result = new StringBuilder();
        boolean inPlaceholder = false;
        boolean inColorCode;

        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);

            // Detect start of placeholder (%...% or <...>)
            if (!inPlaceholder && (c == '%' || c == '<')) {
                inPlaceholder = true;
            } else if (inPlaceholder && (c == '%' || c == '>')) {
                inPlaceholder = false;
            }

            // Detect Bukkit color codes
            inColorCode = (i > 0 && input.charAt(i - 1) == '&');

            // Apply custom font if not in placeholder or color code
            if (!inPlaceholder && !inColorCode) {
                c = applyCustomFont(c);
            }

            result.append(c);
        }

        return result.toString();
    }

    private char applyCustomFont(char c) {
        return fontMap.getOrDefault(c, c);
    }
}
