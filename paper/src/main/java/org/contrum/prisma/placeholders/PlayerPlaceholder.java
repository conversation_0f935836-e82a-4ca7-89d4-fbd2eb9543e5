package org.contrum.prisma.placeholders;

import lombok.RequiredArgsConstructor;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.tag.Tag;
import org.contrum.tritosa.placeholder.Placeholder;
import org.contrum.tritosa.placeholder.complex.ComplexPlaceholder;

@RequiredArgsConstructor
public class PlayerPlaceholder extends Placeholder<Player> {

    private final PaperServices services;

    @Override
    public ComplexPlaceholder<Player> setupComplexPlaceholders() {
        return null;
    }

    @Override
    public String replace(Player player, String input) {
        Profile profile = services.getProfileService().getProfile(player.getUniqueId());
        String activeTagName = profile.getActiveTagName(profile.getLastServer());

        Tag activeTag = null;

        if (activeTagName != null) {
            activeTag = services.getTagService().getTag(activeTagName);
        }

        return input
                .replace("<user_uuid>", player.getUniqueId().toString())
                .replace("<user_real_name>", profile.getRealName())
                .replace("<user_real_displayname>", profile.getRealDisplayName())
                .replace("<user_name>", profile.getName())
                .replace("<user_displayname>", profile.getDisplayName())
                .replace("<user_coloredname>", profile.getColoredName())
                .replace("<user_tag_prefix>", activeTag == null ? "" : activeTag.getPrefix())
                .replace("<user_tag_suffix>", activeTag == null ? "" : activeTag.getSuffix())
                ;
    }
}