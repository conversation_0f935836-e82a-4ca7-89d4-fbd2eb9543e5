package org.contrum.prisma.placeholders;

import lombok.RequiredArgsConstructor;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.punishment.Punishment;
import org.contrum.prisma.staff.evidences.StaffEvidence;
import org.contrum.prisma.staff.evidences.punishment.StaffPunishmentEvidence;
import org.contrum.prisma.utils.time.TimeUtils;
import org.contrum.tritosa.placeholder.Placeholder;
import org.contrum.tritosa.placeholder.complex.ComplexPlaceholder;

@RequiredArgsConstructor
public class StaffPunishmentPlaceholders extends Placeholder<StaffEvidence> {

    private final PaperServices services;

    @Override
    public ComplexPlaceholder<StaffEvidence> setupComplexPlaceholders() {
        return null;
    }

    @Override
    public String replace(StaffEvidence evidence, String input) {
        if (evidence instanceof StaffPunishmentEvidence punishmentEvidence) {
            PunishmentPlaceholder placeholder = (PunishmentPlaceholder) services.getLanguageHandler().getPlaceholderManager().getPlaceholder(Punishment.class);
            input = placeholder.replace(punishmentEvidence.getPunishment(), input);
        }

        return input.replace("<evidence_uuid>", evidence.getUuid().toString())
                .replace("<evidence_staff_name>", evidence.getStaff().getName())
                .replace("<evidence_link>", evidence.getProof() == null ? "none" : evidence.getProof().getLinksString())
                .replace("<evidence_approved_name>", evidence.getApprovedBy() == null ? "none" : evidence.getApprovedBy().getName())
                .replace("<evidence_approved_date>", TimeUtils.toDate(evidence.getApprovedAt()))
                .replace("<evidence_denied_name>", evidence.getDeniedBy() == null ? "none" : evidence.getDeniedBy().getName())
                .replace("<evidence_denied_cause>", evidence.getDeniedReason() == null ? "none" : evidence.getDeniedReason())
                .replace("<evidence_denied_date>", TimeUtils.toDate(evidence.getDeniedAt()));
    }
}