package org.contrum.prisma.placeholders.papi;

import lombok.RequiredArgsConstructor;
import me.clip.placeholderapi.expansion.PlaceholderExpansion;
import org.apache.commons.lang.StringUtils;
import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customarmor.CustomArmor;
import org.contrum.prisma.dropmultiplier.DropMultiplierTimer;
import org.contrum.prisma.gameevents.Event;
import org.contrum.prisma.grant.Grant;
import org.contrum.prisma.koth.AbstractKoth;
import org.contrum.prisma.koth.impl.ScheduledKoth;
import org.contrum.prisma.placeholders.PrismaServerPlaceholder;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.currency.Currency;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.punishment.PunishmentType;
import org.contrum.prisma.rank.Rank;
import org.contrum.prisma.server.PrismaServer;
import org.contrum.prisma.systems.impl.dtc.DTCSystem;
import org.contrum.prisma.systems.impl.regionevents.Region;
import org.contrum.prisma.systems.impl.regionevents.RegionEventsSystem;
import org.contrum.prisma.utils.time.TimeUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.UUID;

@RequiredArgsConstructor
public class PrismaPlaceholderExpansion extends PlaceholderExpansion {

    private final PaperServices services;

    @Override
    public @NotNull String getIdentifier() {
        return "core";
    }

    @Override
    public @NotNull String getAuthor() {
        return "Core";
    }

    @Override
    public @NotNull String getVersion() {
        return "1.0-SNAPSHOT";
    }

    @Override
    public @Nullable String onPlaceholderRequest(Player player, @NotNull String params) {
        Profile user = services.getProfileService().getProfile(player.getUniqueId());

        if (user == null) {
            return "";
        }

        ProfilePaperMetadata paperMetadata = user.getServerMetadata(services, ProfilePaperMetadata.class);

        if (params.equalsIgnoreCase("player")) {
            return user.getName();
        }

        if (params.equalsIgnoreCase("coins"))
            return Currency.COINS.format(paperMetadata.getCurrencyFormatted(null, Currency.COINS));

        if (params.startsWith("user_")) {
            switch (params) {
                case "user_displayname":
                    return user.getDisplayName();
                case "user_name":
                    return user.getName();
                case "user_coloredname":
                    return user.getColoredName();
                case "user_chattype":
                    return user.getChatType().toString();
                case "user_server":
                    return user.getLastServer() == null ? "" : user.getLastServer().getName();
                case "user_punishments_count":
                    return user.getPunishments().size() + "";
                case "user_punishments_active_count":
                    return user.getActivePunishmentsByType(PunishmentType.BAN, PunishmentType.IP_BAN, PunishmentType.BLACKLIST).size() + "";
                case "user_blacklists_count":
                    return user.getPunishmentsByType(PunishmentType.BLACKLIST).size() + "";
                case "user_blacklists_active_count":
                    return user.getActivePunishmentsByType(PunishmentType.BLACKLIST).size() + "";
                case "user_bans_count":
                    return user.getPunishmentsByType(PunishmentType.getBanPunishmentsType()).size() + "";
                case "user_bans_active_count":
                    return user.getActivePunishmentsByType(PunishmentType.getBanPunishmentsType()).size() + "";
                case "user_mutes_count":
                    return user.getPunishmentsByType(PunishmentType.MUTE).size() + "";
                case "user_mutes_active_count":
                    return user.getActivePunishmentsByType(PunishmentType.MUTE).size() + "";
                case "user_kicks_count":
                    return user.getPunishmentsByType(PunishmentType.KICK).size() + "";
                case "user_warns_count":
                    return user.getPunishmentsByType(PunishmentType.WARN).size() + "";
                case "user_warns_active_active":
                    return user.getActivePunishmentsByType(PunishmentType.WARN).size() + "";
            }
        }

        if (params.startsWith("online_")) {
            if (params.equals("online_count")) {
                return services.getServersService().getCurrentServer().getOnlinePlayers().size() + "";
            }

            //%online_SERVER-NAME_count%
            if (params.startsWith("online_") && params.endsWith("_count")) {
                String serverName = params.replace("online_", "").replace("_count", "");
                PrismaServer server = services.getServersService().getServer(serverName);

                if (server == null) {
                    return "";
                }

                return server.getOnlinePlayers().size() + "";
            }
        }

        if (params.contains("server_players_{")) {
            String serverName = StringUtils.substringBetween(params, "{", "}");
            PrismaServer server = services.getServersService().getServer(serverName);

            if (server == null) return 0 + "";

            return server.getOnlinePlayers().size() + "";
        }

        if (params.startsWith("server_")) {
            String[] split = params.split("_");

            if (split.length < 2) {
                return "";
            }

            String serverName = split[1];

            PrismaServer server = services.getServersService().getServer(serverName);

            if (server == null) {
                return "";
            }

            switch (split[2]) {
                case "online":
                    return server.getOnlinePlayers().size() + "";
                case "max_memory":
                    return server.getMaxMemory() + "";
                case "used_memory":
                    return server.getUsedMemory() + "";
                case "address":
                    return server.getAddress();
                case "port":
                    return server.getPort() + "";
                case "status":
                    return PrismaServerPlaceholder.parseServerStatus(services.getTranslator(), server);
            }
        }

        if (params.startsWith("rank_")) {
            Grant grant = user.getActiveGrant();
            Rank rank = user.getDisplayRank();

            if (grant == null) {
                return "";
            }

            switch (params) {
                case "rank_prefix":
                    return rank.getPrefix();
                case "rank_server":
                    return rank.toString();
                case "rank_added_at_date":
                    return grant.getAddedAtFormatted();
                case "rank_added_by_name":
                    return grant.getAddedBy().getName();
                case "rank_duration_detailed":
                    return TimeUtils.getFormattedTime(grant.getDuration(), true);
                case "rank_duration":
                    return TimeUtils.getFormattedTime(grant.getDuration());
                case "rank_reason":
                    return grant.getAddedReason();
            }
        }

        if(params.contains("compress_requiered_{")) {
            String mina = StringUtils.substringBetween(params, "{", "}");
            CustomArmor armor = services.getCustomArmorService().getArmor(mina);
            if (armor == null){
                return "mine " + mina + " dosen´t exist";
            }
            return String.valueOf(armor.getCompressRequired());
        }

        if (params.contains("ping")) {
            return player.getPing() + "ms";
        }

        if(params.contains("chest_status_{")) {
            String id = StringUtils.substringBetween(params, "{", "}");

            try {
                UUID uuid = UUID.fromString(id);
                return services.getTreasureChestService().isEmpty(uuid) ? "&cCofre vacio" : "&aClick para abrir";
            } catch (IllegalArgumentException ignored){
                return "UUID: " + id + " doesn't exist";
            }
        }

        if(params.contains("chest_nextreset_{")) {
            String id = StringUtils.substringBetween(params, "{", "}");
            UUID uuid = null;
            try {
                uuid = UUID.fromString(id);
            } catch (IllegalArgumentException ignored){
                return "UUID: " + id + " dosent exist";
            }

            return  String.valueOf(services.getTreasureChestService().getResetDuration(uuid).getTimeLeftAsDuration().toSeconds());
        }

        if(params.contains("chest_nextreset_formated_{")) {
            String id = StringUtils.substringBetween(params, "{", "}");

            try {
                UUID uuid = UUID.fromString(id);
                return services.getTreasureChestService().getResetDuration(uuid).getTimeLeftAsDuration().toSeconds() + " segundos";
            } catch (IllegalArgumentException ignored){
                return "UUID: " + id + " dosent exist";
            }
        }

        if(params.contains("scheduled_koth_remaining_{")) {
            String name = StringUtils.substringBetween(params, "{", "}");

            ScheduledKoth koth = services.getKothService().getScheduledKoth(name);
            if (koth != null) {

                if (koth.isActive()) return CC.translate("&aActivo");

                return koth.getRemainingTime() != null ? TimeUtils.getFormattedTime(koth.getRemainingTime().toMillis(), false) : "-1";
            }
            return "Koth doesnt exist";
        }

        if(params.contains("koth_remaining_{")) {
            String name = StringUtils.substringBetween(params, "{", "}");

            AbstractKoth koth = services.getKothService().getKoth(name);
            return koth != null ? TimeUtils.getFormattedTime(koth.getTimeToCamp().toMillis(), false) : "Koth doesnt exist";
        }

        if(params.contains("scheduled_koth_status")) {
            ScheduledKoth koth = services.getKothService().getActiveScheduledKoth();

            if (koth == null) return "&4❌";
            return "&e" + TimeUtils.getFormattedTime(koth.getTimeToCamp().toMillis(), false);
        }

        if(params.contains("events_active_name")) {
            Event event = services.getGameEventsService().getActiveEvent();
            return event == null ? "&eᴇɴ " + services.getGameEventsService().getNextEventRemainingTime() : event.getFormattedDisplayName();
        }

        if(params.contains("events_active_time_left")) {
            Event event = services.getGameEventsService().getActiveEvent();
            return event == null ? "" : event.getRemainingTimeFormatted();
        }

        if(params.contains("time")) {
            long segundosRestantes = LocalTime.now(ZoneId.of("America/New_York")).until(LocalTime.MIDNIGHT, ChronoUnit.SECONDS);
            if (segundosRestantes < 0) segundosRestantes += 24 * 3600;
            long hours = segundosRestantes / 3600;
            long minutes = (segundosRestantes % 3600) / 60;
            long seconds = segundosRestantes % 60;
            return String.format("%02d:%02d:%02d", hours, minutes, seconds);
        }

        if (params.startsWith("dtc_")) {
            DTCSystem dtcSystem = services.getSystemService().getSystem(DTCSystem.class);
            if (dtcSystem == null || !dtcSystem.isEnabled()) return "&cDTC disabled.";

            switch (params) {
                case "dtc_next_time":
                    return dtcSystem.getNextDTCTime().isNegative() ? "&aᴀᴄᴛɪᴠᴏ" : TimeUtils.getFormattedTime(dtcSystem.getNextDTCTime().toMillis(), true);
                case "dtc_remaining_hits":
                    return dtcSystem.getRemainingHits() < 1 ? "&4&l✕" : dtcSystem.getRemainingHits() + "";
            }
        }

        if (params.startsWith("region_")) {
            String[] split = params.split("_");

            if (split.length < 2) {
                return "";
            }
            String regionName = split[1];
            Region region = services.getSystemService().getSystem(RegionEventsSystem.class).getRegion(regionName);
            if (region == null) {
                return "";
            }

            switch (split[2]) {
                case "nextevent":
                    return TimeUtils.getFormattedTime(region.getNextEventRemaining().toMillis(), false);
                case "currentevent":
                    return region.getActiveEvent() == null ? "&cNinguno" : region.getActiveEvent().getDisplayName(player);
            }
        }

        return params;
    }

    @Override
    public String onRequest(OfflinePlayer player, @NotNull String params) {
        Profile user = services.getProfileService().getProfile(player.getUniqueId());

        if (user == null) {
            return "";
        }

        ProfilePaperMetadata paperMetadata = user.getServerMetadata(services, ProfilePaperMetadata.class);

        if (params.equalsIgnoreCase("player")) {
            return user.getName();
        }

        if (params.equalsIgnoreCase("coins"))
            return Currency.COINS.format(paperMetadata.getCurrencyFormatted(null, Currency.COINS));

        if (params.startsWith("user_")) {
            switch (params) {
                case "user_displayname":
                    return user.getDisplayName();
                case "user_name":
                    return user.getName();
                case "user_coloredname":
                    return user.getColoredName();
                case "user_chattype":
                    return user.getChatType().toString();
                case "user_server":
                    return user.getLastServer() == null ? "" : user.getLastServer().getName();
                case "user_punishments_count":
                    return user.getPunishments().size() + "";
                case "user_punishments_active_count":
                    return user.getActivePunishmentsByType(PunishmentType.BAN, PunishmentType.IP_BAN, PunishmentType.BLACKLIST).size() + "";
                case "user_blacklists_count":
                    return user.getPunishmentsByType(PunishmentType.BLACKLIST).size() + "";
                case "user_blacklists_active_count":
                    return user.getActivePunishmentsByType(PunishmentType.BLACKLIST).size() + "";
                case "user_bans_count":
                    return user.getPunishmentsByType(PunishmentType.getBanPunishmentsType()).size() + "";
                case "user_bans_active_count":
                    return user.getActivePunishmentsByType(PunishmentType.getBanPunishmentsType()).size() + "";
                case "user_mutes_count":
                    return user.getPunishmentsByType(PunishmentType.MUTE).size() + "";
                case "user_mutes_active_count":
                    return user.getActivePunishmentsByType(PunishmentType.MUTE).size() + "";
                case "user_kicks_count":
                    return user.getPunishmentsByType(PunishmentType.KICK).size() + "";
                case "user_warns_count":
                    return user.getPunishmentsByType(PunishmentType.WARN).size() + "";
                case "user_warns_active_active":
                    return user.getActivePunishmentsByType(PunishmentType.WARN).size() + "";
            }
        }

        if (params.startsWith("online_")) {
            if (params.equals("online_count")) {
                return services.getServersService().getCurrentServer().getOnlinePlayers().size() + "";
            }

            //%online_SERVER-NAME_count%
            if (params.startsWith("online_") && params.endsWith("_count")) {
                String serverName = params.replace("online_", "").replace("_count", "");
                PrismaServer server = services.getServersService().getServer(serverName);

                if (server == null) {
                    return "";
                }

                return server.getOnlinePlayers().size() + "";
            }
        }

        if (params.contains("server_players_{")) {
            String serverName = StringUtils.substringBetween(params, "{", "}");
            PrismaServer server = services.getServersService().getServer(serverName);

            if (server == null) return 0 + "";

            return server.getOnlinePlayers().size() + "";
        }

        if (params.startsWith("server_")) {
            String[] split = params.split("_");

            if (split.length < 2) {
                return "";
            }

            String serverName = split[1];

            PrismaServer server = services.getServersService().getServer(serverName);

            if (server == null) {
                return "";
            }

            switch (split[2]) {
                case "online":
                    return server.getOnlinePlayers().size() + "";
                case "max_memory":
                    return server.getMaxMemory() + "";
                case "used_memory":
                    return server.getUsedMemory() + "";
                case "address":
                    return server.getAddress();
                case "port":
                    return server.getPort() + "";
                case "status":
                    return PrismaServerPlaceholder.parseServerStatus(services.getTranslator(), server);
            }
        }

        if (params.startsWith("rank_") || params.contains("ranks_")) {
            Grant grant = user.getActiveGrant();
            Rank rank = user.getDisplayRank();

            if (grant == null) {
                return "";
            }

            switch (params) {
                case "rank_prefix", "ranks_prefix:":
                    return rank.getPrefix();
                case "rank_server", "ranks_server":
                    return grant.getContext().toString();
                case "rank_added_at_date":
                    return grant.getAddedAtFormatted();
                case "rank_added_by_name":
                    return grant.getAddedBy().getName();
                case "rank_duration_detailed":
                    return TimeUtils.getFormattedTime(grant.getDuration(), true);
                case "rank_duration", "ranks_duration":
                    return TimeUtils.getFormattedTime(grant.getDuration());
                case "rank_reason", "ranks_reason":
                    return grant.getAddedReason();
            }
        }

        if (params.contains("dropmultiplier_{")) {
            String nick = StringUtils.substringBetween(params, "{", "}");
            Player p = Bukkit.getPlayerExact(nick);
            if (p == null) return "";
            ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(p.getUniqueId(), ProfilePaperMetadata.class);
            if (metadata == null) return "";

            DropMultiplierTimer timer = metadata.getTimer(DropMultiplierTimer.class);
            if (timer != null && !timer.isExpired()) {
                return CC.translate(" " + timer.getOrbDisplayText());
            }
            return "";
        }

        if(params.contains("compress_requiered_{")) {
            String mina = StringUtils.substringBetween(params, "{", "}");
            CustomArmor armor = services.getCustomArmorService().getArmor(mina);
            if (armor == null){
                return "mine " + mina + " dosen´t exist";
            }
            return String.valueOf(armor.getCompressRequired());
        }

        if(params.contains("chest_status_{")) {
            String id = StringUtils.substringBetween(params, "{", "}");

            try {
                UUID uuid = UUID.fromString(id);
                return services.getTreasureChestService().isEmpty(uuid) ? "&cCofre vacio" : "&aClick para abrir";
            } catch (IllegalArgumentException ignored){
                return "UUID: " + id + " doesn't exist";
            }
        }

        if(params.contains("chest_nextreset_{")) {
            String id = StringUtils.substringBetween(params, "{", "}");
            UUID uuid = null;
            try {
                uuid = UUID.fromString(id);
            } catch (IllegalArgumentException ignored){
                return "UUID: " + id + " dosent exist";
            }

            return  String.valueOf(services.getTreasureChestService().getResetDuration(uuid).getTimeLeftAsDuration().toSeconds());
        }

        if(params.contains("chest_nextreset_formated_{")) {
            String id = StringUtils.substringBetween(params, "{", "}");

            try {
                UUID uuid = UUID.fromString(id);
                return services.getTreasureChestService().getResetDuration(uuid).getTimeLeftAsDuration().toSeconds() + " segundos";
            } catch (IllegalArgumentException ignored){
                return "UUID: " + id + " dosent exist";
            }
        }

        if(params.contains("scheduled_koth_remaining_{")) {
            String name = StringUtils.substringBetween(params, "{", "}");

            ScheduledKoth koth = services.getKothService().getScheduledKoth(name);
            if (koth != null) {

                if (koth.isActive()) return CC.translate("&aActivo");

                return koth.getRemainingTime() != null ? TimeUtils.getFormattedTime(koth.getRemainingTime().toMillis(), false) : "-1";
            }
            return "Koth doesnt exist";
        }

        if(params.contains("koth_remaining_{")) {
            String name = StringUtils.substringBetween(params, "{", "}");

            AbstractKoth koth = services.getKothService().getKoth(name);
            return koth != null ? TimeUtils.getFormattedTime(koth.getTimeToCamp().toMillis(), false) : "Koth doesnt exist";
        }

        if(params.contains("scheduled_koth_status")) {
            ScheduledKoth koth = services.getKothService().getActiveScheduledKoth();

            if (koth == null) return "&4❌";
            return "&e" + TimeUtils.getFormattedTime(koth.getTimeToCamp().toMillis(), false);
        }

        if(params.contains("events_active_name")) {
            Event event = services.getGameEventsService().getActiveEvent();
            return event == null ? "&eᴇɴ " + services.getGameEventsService().getNextEventRemainingTime() : event.getFormattedDisplayName();
        }

        if(params.contains("events_active_time_left")) {
            Event event = services.getGameEventsService().getActiveEvent();
            return event == null ? "" : event.getRemainingTimeFormatted();
        }

        if (player.isOnline()) {
            Player p = player.getPlayer();
            if (p == null) return params;

            if (params.contains("ping")) {
                return p.getPing() + "ms";
            }
        }

        if(params.contains("time")) {
            long segundosRestantes = LocalTime.now(ZoneId.of("America/New_York")).until(LocalTime.MIDNIGHT, ChronoUnit.SECONDS);
            if (segundosRestantes < 0) segundosRestantes += 24 * 3600;
            long hours = segundosRestantes / 3600;
            long minutes = (segundosRestantes % 3600) / 60;
            long seconds = segundosRestantes % 60;
            return String.format("%02d:%02d:%02d", hours, minutes, seconds);
        }

        if (params.startsWith("dtc_")) {
            DTCSystem dtcSystem = services.getSystemService().getSystem(DTCSystem.class);
            if (dtcSystem == null || !dtcSystem.isEnabled()) return "&cDTC disabled.";

            switch (params) {
                case "dtc_next_time":
                    return dtcSystem.getNextDTCTime().isNegative() ? "&aᴀᴄᴛɪᴠᴏ" : TimeUtils.getFormattedTime(dtcSystem.getNextDTCTime().toMillis(), true);
                case "dtc_remaining_hits":
                    return dtcSystem.getRemainingHits() < 1 ? "&4&l✕" : dtcSystem.getRemainingHits() + "";
            }
        }

        if (params.startsWith("region_")) {
            String[] split = params.split("_");

            if (split.length < 2) {
                return "";
            }
            String regionName = split[1];
            Region region = services.getSystemService().getSystem(RegionEventsSystem.class).getRegion(regionName);
            if (region == null) {
                return "";
            }

            switch (split[2]) {
                case "nextevent":
                    return TimeUtils.getFormattedTime(region.getNextEventRemaining().toMillis(), false);
                case "currentevent":
                    return region.getActiveEvent() == null ? "&cNinguno" : region.getActiveEvent().getDisplayName(player.getPlayer());
            }
        }

        return params;
    }
}