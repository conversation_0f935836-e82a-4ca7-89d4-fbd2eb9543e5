/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by txmydev on 28/03/2024
 * Website: contrum.org
 */

package org.contrum.prisma.placeholders;

import org.contrum.prisma.utils.time.TimeUtils;
import org.contrum.tritosa.placeholder.Placeholder;
import org.contrum.tritosa.placeholder.complex.ComplexPlaceholder;

import java.time.Duration;

public class DurationPlaceholders extends Placeholder<Duration> {
    @Override
    public ComplexPlaceholder<Duration> setupComplexPlaceholders() {
        ComplexPlaceholder<Duration> placeholder = new ComplexPlaceholder<>("duration_if");
        placeholder.addResolver("permanent", duration -> duration.getSeconds() == -1);

        return placeholder;
    }

    @Override
    public String replace(Duration duration, String s) {
        return s.replace("<duration_short>", TimeUtils.getFormattedTime(duration.toMillis(), false))
                .replace("<duration_full>", TimeUtils.getFormattedTime(duration.toMillis(), true))
                .replace("<duration_millis>", TimeUtils.getFormattedTimeMillis(duration.toMillis()));
    }
}
