package org.contrum.prisma.placeholders;

import org.contrum.prisma.punishment.Punishment;
import org.contrum.prisma.utils.time.TimeUtils;
import org.contrum.prisma.utils.user.SimpleUser;
import org.contrum.tritosa.placeholder.Placeholder;
import org.contrum.tritosa.placeholder.complex.ComplexPlaceholder;

public class PunishmentPlaceholder extends Placeholder<Punishment> {
    @Override
    public ComplexPlaceholder<Punishment> setupComplexPlaceholders() {
        ComplexPlaceholder<Punishment> placeholders = new ComplexPlaceholder<>("punishment_if");

        placeholders.addResolver("temporary", punishment -> !punishment.isPermanent());
        placeholders.addResolver("permanent", Punishment::isPermanent);
        placeholders.addResolver("active", Punishment::isActive);
        placeholders.addResolver("silent", Punishment::isSilent);

        return placeholders;
    }

    @Override
    public String replace(Punishment punishment, String input) {
        SimpleUser target = punishment.getTarget();
        SimpleUser addedBy = punishment.getAddedBy();

        return input.replace("<punishment_target_name>", target.getName())
                .replace("<punishment_target_coloredname>", target.getName())
                .replace("<punishment_target_uuid>", target.getUniqueId().toString())
                .replace("<punishment_sender_name>", addedBy.getName())
                .replace("<punishment_sender_coloredname>", addedBy.getName())
                .replace("<punishment_added_by_uuid>", addedBy.getUniqueId().toString())
                .replace("<punishment_reason>", punishment.getReason())
                .replace("<punishment_date>", TimeUtils.toDate(punishment.getAddedAt()))
                .replace("<punishment_duration>", TimeUtils.getFormattedTime(punishment.getDuration()))
                .replace("<punishment_duration_full>", TimeUtils.getFormattedTime(punishment.getDuration(), true))
                .replace("<punishment_duration_short>", TimeUtils.getFormattedTime(punishment.getDuration()))
                .replace("<punishment_expiry_full>", punishment.isPermanent() ? "Nunca" : punishment.getRemaining() < 0 ? "Expired" : TimeUtils.getFormattedTime(punishment.getRemaining(), true))
                .replace("<punishment_expiry_short>", punishment.isPermanent() ? "Nunca" : punishment.getRemaining() < 0 ? "Expired" : TimeUtils.getFormattedTime(punishment.getRemaining()))
                .replace("<punishment_removed_by>", punishment.getRemovedBy() == null ? "none" : punishment.getRemovedBy().getName())
                .replace("<punishment_removed_date>", TimeUtils.toDate(punishment.getRemovedAt()))
                .replace("<punishment_removed_reason>", punishment.getRemovedReason() == null ? "none" : punishment.getRemovedReason())
                .replace("<punishment_context>", punishment.getContext().toString());
    }
}
