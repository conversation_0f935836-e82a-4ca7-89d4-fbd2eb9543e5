package org.contrum.prisma.placeholders;

import lombok.RequiredArgsConstructor;
import org.contrum.prisma.queue.Queue;
import org.contrum.tritosa.placeholder.Placeholder;
import org.contrum.tritosa.placeholder.complex.ComplexPlaceholder;

@RequiredArgsConstructor
public class QueuePlaceholder extends Placeholder<Queue> {

    @Override
    public ComplexPlaceholder<Queue> setupComplexPlaceholders() {
        return null;
    }

    @Override
    public String replace(Queue queue, String input) {
        return input
                .replace("<queue_name>", queue.getServer());
    }
}