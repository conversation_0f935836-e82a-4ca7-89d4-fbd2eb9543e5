package org.contrum.prisma.placeholders;

import org.contrum.prisma.koth.AbstractKoth;
import org.contrum.tritosa.placeholder.Placeholder;
import org.contrum.tritosa.placeholder.complex.ComplexPlaceholder;

public class KothPlaceholder extends Placeholder<AbstractKoth> {
    @Override
    public ComplexPlaceholder<AbstractKoth> setupComplexPlaceholders() {
        return null;
    }

    @Override
    public String replace(AbstractKoth koth, String s) {
        return s.replace("<koth_name>", koth.getName());
    }
}
