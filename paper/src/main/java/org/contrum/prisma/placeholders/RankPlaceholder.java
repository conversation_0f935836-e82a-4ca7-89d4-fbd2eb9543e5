package org.contrum.prisma.placeholders;

import org.contrum.prisma.rank.Rank;
import org.contrum.tritosa.placeholder.Placeholder;
import org.contrum.tritosa.placeholder.complex.ComplexPlaceholder;

public class RankPlaceholder extends Placeholder<Rank> {
    @Override
    public ComplexPlaceholder<Rank> setupComplexPlaceholders() {
        return null;
    }

    @Override
    public String replace(Rank rank, String input) {
        return input.replace("<rank_name>", rank.getName())
                .replace("<rank_display_name>", rank.getDisplayName())
                .replace("<rank_prefix>", rank.getPrefix())
                .replace("<rank_suffix>", rank.getSuffix())
                .replace("<rank_color>", rank.getColor())
                .replace("<rank_weight>", String.valueOf(rank.getWeight()))
                .replace("<rank_default>", String.valueOf(rank.isDefaultRank()))
                .replace("<rank_permissions>", String.join(", ", rank.getPermissions()));
    }
}
