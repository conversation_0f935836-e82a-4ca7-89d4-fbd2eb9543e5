package org.contrum.prisma.placeholders;

import lombok.RequiredArgsConstructor;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.grant.Grant;
import org.contrum.prisma.rank.Rank;
import org.contrum.prisma.utils.time.TimeUtils;
import org.contrum.prisma.utils.user.SimpleUser;
import org.contrum.tritosa.placeholder.Placeholder;
import org.contrum.tritosa.placeholder.complex.ComplexPlaceholder;

@RequiredArgsConstructor
public class GrantPlaceholder extends Placeholder<Grant> {

    private final PaperServices paperServices;

    @Override
    public ComplexPlaceholder<Grant> setupComplexPlaceholders() {
        return null;
    }

    @Override
    public String replace(Grant grant, String input) {
        RankPlaceholder placeholder = (RankPlaceholder) paperServices.getLanguageHandler().getPlaceholderManager().getPlaceholder(Rank.class);
        input = placeholder.replace(grant.getRank(), input);

        return input.replace("<grant_added_by>", grant.getAddedBy() != null ? (grant.getAddedBy().equals(SimpleUser.CONSOLE_USER) ? "&cConsole" : grant.getAddedBy().getName()) : "none")
                .replace("<grant_added_reason>", grant.getAddedReason() != null ? grant.getAddedReason() : "none")
                .replace("<grant_added_at>", grant.getAddedAtFormatted() != null ? grant.getAddedAtFormatted() : "none")
                .replace("<grant_context>", grant.getContext() != null ? grant.getContext().toString() : "none")
                .replace("<grant_duration>", TimeUtils.getFormattedTime(grant.getDuration()))
                .replace("<grant_removed_by>", grant.getRemovedBy() != null ? (grant.getRemovedBy().equals(SimpleUser.CONSOLE_USER) ? "&cConsole" : grant.getRemovedBy().getName()) : "none")
                .replace("<grant_removed_reason>", grant.getRemovedReason() == null ? "none" : grant.getRemovedReason())
                .replace("<grant_removed_at>", grant.getRemovedAtFormatted() != null ? grant.getRemovedAtFormatted() : "none")
                .replace("<grant_remaining_time>", grant.getRemainingTime().getSeconds() == -1 ? "Permanente" : grant.getRemainingTime().isNegative() ? "Expirado" : TimeUtils.getFormattedTime(grant.getRemainingTime().toMillis(), false))
                .replace("<grant_expired_at>", grant.getExpiredAtFormatted() != null ? grant.getExpiredAtFormatted() : "none");
    }
}
