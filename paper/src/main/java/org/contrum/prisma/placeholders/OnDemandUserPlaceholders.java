/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by txmydev on 28/03/2024
 * Website: contrum.org
 */

package org.contrum.prisma.placeholders;

import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.tag.Tag;
import org.contrum.prisma.utils.user.SimpleUser;
import org.contrum.tritosa.placeholder.LocalPlaceholders;


public class OnDemandUserPlaceholders extends LocalPlaceholders {

    /**
     * Constructor for creating OnDemandUserPlaceholders for a CoreUser.
     * This constructor adds placeholders for the user's display name, name, colored name, full chat name, chat type, last server, first joined date, and time since first joined.
     *
     * @param prefix The prefix for the placeholders.
     * @param user The CoreUser for whom the placeholders are being created.
     * @param legacy A flag indicating if the user is a legacy user.
     */
    private OnDemandUserPlaceholders(String prefix, Profile user, boolean legacy) {
        this.add("<" + prefix + "_displayname>", user.getDisplayName())
                .add("<" + prefix + "_name>", user.getName())
                .add("<" + prefix + "_coloredname>", user.getColoredName())
                //.add("<" + prefix + "_chattype>", user.getChatType().toString())
                .add("<" + prefix + "_server>", user.getLastServer().getName());
    }

    private OnDemandUserPlaceholders(PaperServices services, String prefix, Player player) {
        Profile profile = services.getProfileService().getProfile(player.getUniqueId());
        String activeTagName = profile.getActiveTagName(profile.getLastServer());

        Tag activeTag = null;

        if (activeTagName != null) {
            activeTag = services.getTagService().getTag(activeTagName);
        }

        this.add("<" + prefix + "uuid>", player.getUniqueId().toString())
                .add("<" + prefix + "_real_name>", profile.getRealName())
                .add("<" + prefix + "_real_displayname>", profile.getRealDisplayName())
                .add("<" + prefix + "_name>", profile.getName())
                .add("<" + prefix + "_displayname>", profile.getDisplayName())
                .add("<" + prefix + "_coloredname>", profile.getColoredName())
                .add("<" + prefix + "_tag_prefix>", activeTag == null ? "" : activeTag.getPrefix())
                .add("<" + prefix + "_tag_suffix>", activeTag == null ? "" : activeTag.getSuffix());
    }

    private OnDemandUserPlaceholders(String prefix, SimpleUser user) {
        this.add("<" + prefix + "_name>", user.getName());
    }

    /**
     * Factory method for creating OnDemandUserPlaceholders for a CoreUser.
     *
     * @param prefix The prefix for the placeholders.
     * @param user The CoreUser for whom the placeholders are being created.
     * @return An instance of OnDemandUserPlaceholders for the given CoreUser.
     */
    public static OnDemandUserPlaceholders with(String prefix, Profile user) {
        return new OnDemandUserPlaceholders(prefix, user, false);
    }

    public static OnDemandUserPlaceholders with(String prefix, SimpleUser user) {
        return new OnDemandUserPlaceholders(prefix, user);
    }

    public static OnDemandUserPlaceholders with(PaperServices services, String prefix, Player player) {
        return new OnDemandUserPlaceholders(services, prefix, player);
    }
}
