package org.contrum.prisma.placeholders;

import lombok.RequiredArgsConstructor;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.gameevents.Event;
import org.contrum.prisma.utils.time.TimeUtils;
import org.contrum.tritosa.placeholder.Placeholder;
import org.contrum.tritosa.placeholder.complex.ComplexPlaceholder;

import java.time.Duration;

@RequiredArgsConstructor
public class GameEventPlaceholder extends Placeholder<Event> {

    private final PaperServices services;

    @Override
    public ComplexPlaceholder<Event> setupComplexPlaceholders() {
        return null;
    }

    @Override
    public String replace(Event event, String s) {
        return s.replace("<event_name>", event.getFormattedDisplayName())
                .replace("<event_duration>", TimeUtils.getFormattedTime(event.getDuration().toMillis()))
                .replace("<next_event_remaining>", services.getGameEventsService().getNextEventRemainingTime())
                .replace("<description>", event.getDescription());
    }
}
