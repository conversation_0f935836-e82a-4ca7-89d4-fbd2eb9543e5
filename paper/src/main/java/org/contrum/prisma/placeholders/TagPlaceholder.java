package org.contrum.prisma.placeholders;

import org.contrum.prisma.tag.Tag;
import org.contrum.tritosa.placeholder.Placeholder;
import org.contrum.tritosa.placeholder.complex.ComplexPlaceholder;

public class TagPlaceholder extends Placeholder<Tag> {
    @Override
    public ComplexPlaceholder<Tag> setupComplexPlaceholders() {
        return null;
    }

    @Override
    public String replace(Tag tag, String input) {
        return input.replace("<tag_name>", tag.getName())
                .replace("<tag_display_name>", tag.getDisplayName())
                .replace("<tag_prefix>", tag.getPrefix())
                .replace("<tag_suffix>", tag.getSuffix());
    }
}
