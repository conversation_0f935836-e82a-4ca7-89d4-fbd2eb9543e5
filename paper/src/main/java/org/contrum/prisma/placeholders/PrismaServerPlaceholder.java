package org.contrum.prisma.placeholders;

import lombok.RequiredArgsConstructor;
import org.bukkit.Bukkit;
import org.contrum.prisma.server.PrismaServer;
import org.contrum.tritosa.Translator;
import org.contrum.tritosa.holder.LanguageHolder;
import org.contrum.tritosa.placeholder.LocalPlaceholders;
import org.contrum.tritosa.placeholder.Placeholder;
import org.contrum.tritosa.placeholder.complex.ComplexPlaceholder;

@RequiredArgsConstructor
public class PrismaServerPlaceholder extends Placeholder<PrismaServer> {

    private final Translator translator;

    @Override
    public ComplexPlaceholder<PrismaServer> setupComplexPlaceholders() {
        return null;
    }

    @Override
    public String replace(PrismaServer server, String input) {
        return input.replace("<server_name>", server.getName())
                .replace("<server_status>", parseServerStatus(translator, server))
                .replace("<server_online>", String.valueOf(server.isOnline()))
                .replace("<server_online_players>", String.valueOf(server.getOnlinePlayers()));
    }

    public static String parseServerStatus(Translator translator, PrismaServer server) {
        return server.getStatus() == PrismaServer.ServerStatus.ONLINE ? translator.getAsStringRaw(Bukkit.getConsoleSender(), "SERVER.ONLINE_STATUS")
                : server.getStatus() == PrismaServer.ServerStatus.WHITELISTED ? translator.getAsStringRaw(Bukkit.getConsoleSender(), "SERVER.WHITELISTED_STATUS")
                : server.getStatus() == PrismaServer.ServerStatus.OFFLINE ? translator.getAsStringRaw(Bukkit.getConsoleSender(), "SERVER.OFFLINE_STATUS")
                : translator.getAsStringRaw(Bukkit.getConsoleSender(), "SERVER.INITIALIZING_STATUS", LocalPlaceholders.builder().add("<percentage>", server.getInitializingPercentage() + ""));
    }
}