/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by izLoki on 06/05/2024
 * Website: contrum.org
 */

package org.contrum.prisma.placeholders;

import lombok.AllArgsConstructor;
import org.bukkit.Bukkit;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customarmor.CustomArmor;
import org.contrum.prisma.customarmor.CustomArmorService;
import org.contrum.tritosa.placeholder.Placeholder;
import org.contrum.tritosa.placeholder.complex.ComplexPlaceholder;

@AllArgsConstructor
public class CustomArmorPlaceholder extends Placeholder<CustomArmor> {

    private final PaperServices services;

    @Override
    public ComplexPlaceholder<CustomArmor> setupComplexPlaceholders() {
        return null;
    }

    @Override
    public String replace(CustomArmor armor, String s) {
        String nextArmorText;
        if (services.getCustomArmorService().getNextArmor(armor.getName()) != null) nextArmorText = services.getTranslator().getAsText(Bukkit.getConsoleSender(), "CUSTOM_ARMOR.NEXT_MINE_LORE").replaceAll("<next_armor_display_name>", services.getCustomArmorService().getNextArmor(armor.getName()).getFormatDisplayName());
        else nextArmorText = services.getTranslator().getAsText(Bukkit.getConsoleSender(), "CUSTOM_ARMOR.MAX_LEVEL_LORE");

        return s.replaceAll("<armor_name>", armor.getName())
                .replaceAll("<armor_display_name>", armor.getFormatDisplayName())
                .replaceAll("<armor_sharpness>", String.valueOf(armor.getSharpness()))
                .replaceAll("<armor_protection>", String.valueOf(armor.getProtection()))
                .replaceAll("<armor_efficiency>", String.valueOf(armor.getEfficiency()))
                .replaceAll("<armor_durability>", String.valueOf(armor.getDurability()))
                .replaceAll("<armor_compress_required>", String.valueOf(armor.getCompressRequired()))
                .replaceAll("<armor_compress_cost>", String.valueOf(armor.getCompressCost()))
                .replaceAll("<armor_health_attribute>", String.valueOf(armor.getHealth() / 2))
                .replaceAll("<armor_pickaxe_multiplty_chance>", String.valueOf(armor.getPickaxeMultiplyChance()))
                .replaceAll("<armor_attack_speed_attribute>", String.valueOf(armor.getAttackSpeed()))
                .replaceAll("<armor_extra_damage_attribute>", String.valueOf(armor.getDamage()))
                .replaceAll("<armor_movement_speed_attribute>", String.valueOf(armor.getSpeed()))
                .replaceAll("<upgrade_status>", nextArmorText);
    }
}
