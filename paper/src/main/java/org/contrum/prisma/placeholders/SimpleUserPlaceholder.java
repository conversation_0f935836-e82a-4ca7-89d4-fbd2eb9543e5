package org.contrum.prisma.placeholders;

import org.contrum.prisma.utils.user.SimpleUser;
import org.contrum.tritosa.placeholder.Placeholder;
import org.contrum.tritosa.placeholder.complex.ComplexPlaceholder;

public class SimpleUserPlaceholder extends Placeholder<SimpleUser> {
    @Override
    public ComplexPlaceholder<SimpleUser> setupComplexPlaceholders() {
        return null;
    }

    @Override
    public String replace(SimpleUser user, String input) {
        return input.replace("<user_name>", user.getName())
                .replace("<user_coloredname>", user.getName())
                .replace("<user_displayname>", user.getName())
                .replace("<user_uuid>", user.getUniqueId().toString());
    }
}
