package org.contrum.prisma.dailystreak.menu;

import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.chat.CC;
import org.contrum.chorpu.menu.button.Button;
import org.contrum.chorpu.menu.impl.Menu;
import org.contrum.prisma.dailystreak.DailyStreak;
import org.contrum.prisma.dailystreak.DailyStreakManager;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public final class DailyStreakRewardsEditMenu extends Menu {

    private final DailyStreakManager manager;

    private final DailyStreak streak;

    public DailyStreakRewardsEditMenu(DailyStreak streak, DailyStreakManager manager) {
        this.manager = manager;
        this.streak = streak;

        /*setAllowMoveInventory(true);
        setAllowPlayerInventoryClick(true);*/
    }


    @Override
    public boolean shouldCancel(Player player, int slot, boolean top, Button button) {
        return false;
    }

    @Override
    public String getTitle(Player player) {
        return "Editing Streak: #" + streak.getNumber();
    }

    @Override
    public void onClose(InventoryCloseEvent event) {
        Inventory inventory = event.getInventory();
        Player player = (Player) event.getPlayer();
        manager.setRewards(streak, Arrays.asList(inventory.getContents()));
        player.sendMessage(CC.translate("&aYou've been updated rewards of daily streak &6#" + streak.getNumber() + "&a!"));
    }

    @Override
    public int getRows(Player player) {
        return 6;
    }

    @Override
    public Map<Integer, Button> getButtons(Player player) {
        Map<Integer, Button> buttons = new HashMap<>();

        int slot = 0;

        for (ItemStack item : manager.getRewards(streak)) {
            buttons.put(slot++, new Button() {

                @Override
                public boolean isCancel() {
                    return false;
                }

                @Override
                public ItemStack getDisplayItem(Player player) {
                    return item;
                }
            });
        }

        return buttons;
    }
}