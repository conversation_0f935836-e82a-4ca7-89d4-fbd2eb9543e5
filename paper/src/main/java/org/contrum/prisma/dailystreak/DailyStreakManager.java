package org.contrum.prisma.dailystreak;

import org.bukkit.inventory.ItemStack;
import org.contrum.prisma.utils.config.ConfigFile;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.utils.GenericUtils;

import java.util.*;
import java.util.stream.Collectors;

public final class DailyStreakManager {

    private final ConfigFile config;

    private final Map<DailyStreak, List<ItemStack>> dailyStreakRewards = new HashMap<>();

    public DailyStreakManager(PaperServices paperServices) {
        config = new ConfigFile(paperServices.getPlugin(), "data/daily_streaks.yml");

        for (DailyStreak streak : DailyStreak.values()) {
            Object rawItems = config.getConfig().get("daily-streaks." + streak.name());
            if (rawItems instanceof List<?>) {
                dailyStreakRewards.put(streak, GenericUtils.createList(rawItems, ItemStack.class, false));
            }
        }

        //CommandManager.getInstance().registerCommand(new DailyStreakCommand());
    }

    public List<ItemStack> getRewards(DailyStreak streak) {
        return dailyStreakRewards.getOrDefault(streak, Collections.emptyList());
    }

    public void saveDailyStreaks() {
        dailyStreakRewards.forEach((streak, items) -> config.getConfig().set("daily-streaks." + streak.name(), items));
        config.save();
    }

    public void setRewards(DailyStreak streak, List<ItemStack> items) {
        List<ItemStack> filteredItems = items.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (filteredItems.isEmpty()) return;

        dailyStreakRewards.put(streak, filteredItems);
        saveDailyStreaks();
    }
}