package org.contrum.prisma.dailystreak.menu;

import org.apache.commons.lang.time.DurationFormatUtils;
import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.chat.CC;
import org.contrum.chorpu.inventory.ItemBuilder;
import org.contrum.chorpu.menu.button.Button;
import org.contrum.chorpu.menu.impl.Menu;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.dailystreak.DailyStreak;
import org.contrum.prisma.dailystreak.DailyStreakManager;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.utils.InventoryUtils;
import org.contrum.prisma.utils.time.TimeUtils;
import org.contrum.tritosa.Translator;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public final class DailyStreakRewardsMenu extends Menu {

    private final DailyStreakManager dailyStreakManager;

    private final Translator translator;

    private final Profile profile;
    private final ProfilePaperMetadata metadata;

    public DailyStreakRewardsMenu(PaperServices paperServices, Profile profile) {
        this.dailyStreakManager = paperServices.getDailyStreakManager();
        this.translator = paperServices.getTranslator();
        this.profile = profile;
        this.metadata = profile.getServerMetadata(paperServices, ProfilePaperMetadata.class);
    }

    @Override
    public String getTitle(Player player) {
        return translator.getAsText(player, "DAILY_STREAK.REWARDS_MENU.TITLE");
    }

    @Override
    public boolean isAutoUpdate() {
        return true;
    }

    @Override
    public int getRows(Player player) {
        return 3;
    }

    @Override
    public Map<Integer, Button> getButtons(Player player) {
        Map<Integer, Button> buttons = new HashMap<>();
        buttons.put(4, new Button() {
            @Override
            public ItemStack getDisplayItem(Player player) {
                ItemBuilder builder = new ItemBuilder(Material.PLAYER_HEAD);
                builder.setSkullOwner(player.getName());

                boolean streak = metadata.isOnDailyStreak();
                String translationPath = "DAILY_STREAK.REWARDS_MENU.SUMMARY_BUTTON." + (streak ? "STREAK" : "NOT_STREAK");
                builder.setName(translator.getAsText(player, translationPath + ".NAME"));

                translator.getListString(player, translationPath + ".LORE").forEach(lore -> {
                    builder.addLore(lore.replace("<streak>", String.valueOf(metadata.getDailyStreak())));
                });

                return builder.build();
            }
        });

        int slot = 10;

        for (DailyStreak streak : DailyStreak.values()) {
            List<ItemStack> rewards = dailyStreakManager.getRewards(streak);

            String translationPath = "DAILY_STREAK.REWARDS_MENU.REWARD_BUTTON";

            buttons.put(slot++, new Button() {
                @Override
                public ItemStack getDisplayItem(Player player) {
                    boolean claimed = metadata.hasClaimed(streak), canClaim = metadata.canClaim(streak), claimable = !claimed && canClaim;
                    ItemBuilder builder = new ItemBuilder(claimed ? Material.MINECART : Material.CHEST_MINECART);

                    if (claimable) {
                        builder.addUnsafeEnchantment(Enchantment.LURE, 1);
                        builder.addItemFlags(ItemFlag.HIDE_ENCHANTS);
                    }

                    builder.setName(
                            translator.getAsText(player, translationPath + ".NAME")
                                    .replace("<number>", String.valueOf(streak.getNumber())));

                    String lorePath = translationPath + ".LORE";

                    translator.getListString(player, lorePath + ".BASE").forEach(lore -> {
                        if (lore.equals("<rewards>")) {
                            if (rewards.isEmpty()) {
                                builder.addLore(translator.getAsText(player, translationPath + ".MESSAGE.NOT_SET"));
                            } else {
                                rewards.forEach(reward ->
                                        builder.addLore(CC.translate("&7x" + reward.getAmount() + " " +
                                                InventoryUtils.getName(reward))));
                            }
                        } else {
                            builder.addLore(lore);
                        }
                    });

                    boolean availableIn = !claimable && !claimed && metadata.getNextDailyStreak() == streak && metadata.getNextDailyStreakRemainingCooldown() > 0L;
                    String statusPath = lorePath + "." + (claimable ? "CLAIMABLE" : claimed ? "CLAIMED" : availableIn ? "AVAILABLE_IN" : "NOT_YET");
                    translator.getListString(player, statusPath).forEach(lore -> {
                        if (availableIn) {
                            lore = lore.replace("<remaining>", DurationFormatUtils.formatDurationWords(metadata.getNextDailyStreakRemainingCooldown(), true, true));
                        }

                        builder.addLore(lore);
                    });

                    return builder.build();
                }

                @Override
                public void clicked(Player player) {
                    boolean claimed = metadata.hasClaimed(streak), canClaim = metadata.canClaim(streak);
                    if (claimed) {
                        //playFail(player);

                        translator.send(player, translationPath + ".MESSAGE.ALREADY_CLAIMED");
                        return;
                    }

                    if (!canClaim) {
                        //playFail(player);
                        translator.send(player, translationPath + ".MESSAGE.CANNOT_CLAIM");
                        return;
                    }

                    metadata.setDailyStreak(metadata.getDailyStreak() + 1);
                    metadata.setNextDailyStreakMillis(TimeUtils.getTomorrowMillis());

                    for (ItemStack item : rewards) {
                        for (Map.Entry<Integer, ItemStack> excess : player.getInventory().addItem(item.clone()).entrySet()) {
                            player.getWorld().dropItemNaturally(player.getLocation(), excess.getValue());
                        }
                    }
                }
            });
        }

        return buttons;
    }
}